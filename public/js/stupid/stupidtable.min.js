!function(i){i.fn.stupidtable=function(n){return this.each(function(){var t=i(this);n=n||{},n=i.extend({},i.fn.stupidtable.default_sort_fns,n),t.data("sortFns",n),t.stupidtable_build(),t.on("click.stupidtable","thead th",function(){i(this).stupidsort()}),t.find("th[data-sort-onload=yes]").eq(0).stupidsort()})},i.fn.stupidtable.default_settings={should_redraw:function(t){return!0},will_manually_build_table:!1},i.fn.stupidtable.dir={ASC:"asc",DESC:"desc"},i.fn.stupidtable.default_sort_fns={int:function(t,n){return parseInt(t,10)-parseInt(n,10)},float:function(t,n){return parseFloat(t)-parseFloat(n)},string:function(t,n){return t.toString().localeCompare(n.toString())},"string-ins":function(t,n){return t=t.toString().toLocaleLowerCase(),n=n.toString().toLocaleLowerCase(),t.localeCompare(n)}},i.fn.stupidtable_settings=function(a){return this.each(function(){var t=i(this),n=i.extend({},i.fn.stupidtable.default_settings,a);t.stupidtable.settings=n})},i.fn.stupidsort=function(t){var a=i(this),n=a.data("sort")||null;if(null!==n){var r=a.closest("table"),e={$th:a,$table:r,datatype:n};return r.stupidtable.settings||(r.stupidtable.settings=i.extend({},i.fn.stupidtable.default_settings)),e.compare_fn=r.data("sortFns")[n],e.th_index=l(e),e.sort_dir=u(t,e),a.data("sort-dir",e.sort_dir),r.trigger("beforetablesort",{column:e.th_index,direction:e.sort_dir,$th:a}),r.css("display"),setTimeout(function(){r.stupidtable.settings.will_manually_build_table||r.stupidtable_build();var t=s(e),n=d(t,e);r.stupidtable.settings.should_redraw(e)&&(r.children("tbody").append(n),o(e),r.trigger("aftertablesort",{column:e.th_index,direction:e.sort_dir,$th:a}),r.css("display"))},10),a}},i.fn.updateSortVal=function(t){var n=i(this);return n.is("[data-sort-value]")&&n.attr("data-sort-value",t),n.data("sort-value",t),n},i.fn.stupidtable_build=function(){return this.each(function(){var t=i(this),a=[];t.children("tbody").children("tr").each(function(t,n){var e={$tr:i(n),columns:[],index:t};i(n).children("td").each(function(t,n){var a=i(n).data("sort-value");if(void 0===a){var r=i(n).text();i(n).data("sort-value",r),a=r}e.columns.push(a)}),a.push(e)}),t.data("stupidsort_internaltable",a)})};var s=function(s){var t,n=s.$table.data("stupidsort_internaltable"),d=s.th_index,a=s.$th.data("sort-multicolumn");t=a?a.split(","):[];var o=i.map(t,function(t,n){return r(s.$table,t)});return n.sort(function(t,n){for(var a=o.slice(0),r=s.compare_fn(t.columns[d],n.columns[d]);0===r&&a.length;){var e=a[0],i=e.$e.data("sort");r=(0,s.$table.data("sortFns")[i])(t.columns[e.index],n.columns[e.index]),a.shift()}return 0===r?t.index-n.index:r}),s.sort_dir!=i.fn.stupidtable.dir.ASC&&n.reverse(),n},r=function(t,n){var a,r=t.find("th"),e=parseInt(n,10);return e||0===e?a=r.eq(e):(a=r.siblings("#"+n),e=r.index(a)),{index:e,$e:a}},d=function(t,a){var n=i.map(t,function(t,n){return[[t.columns[a.th_index],t.$tr,n]]});return a.column=n,i.map(t,function(t){return t.$tr})},o=function(t){var n=t.$table,a=t.$th,r=a.data("sort-dir");n.find("th").data("sort-dir",null).removeClass("sorting-desc sorting-asc"),a.data("sort-dir",r).addClass("sorting-"+r)},u=function(t,n){var a,r=n.$th,e=i.fn.stupidtable.dir;return t?a=t:(a=t||r.data("sort-default")||e.ASC,r.data("sort-dir")&&(a=r.data("sort-dir")===e.ASC?e.DESC:e.ASC)),a},l=function(t){var n=0,a=t.$th.index();return t.$th.parents("tr").find("th").slice(0,a).each(function(){var t=i(this).attr("colspan")||1;n+=parseInt(t,10)}),n}}(window.jQuery);
