function toShiftJIS(utf8String) {
  const detected = Encoding.detect(utf8String)
  const unicodeList = []

  for (let i = 0; i < utf8String.length; i += 1) {
    unicodeList.push(utf8String.charCodeAt(i))
  }

  const sjisArray = Encoding.convert(unicodeList, {
    to: 'SJIS',
    from: detected
  })
  return new Uint8Array(sjisArray)
}

$('.exampleS').on('click', function () {
  let content = toShiftJIS(window.example_import)
  let blob = new Blob([content], { "type": "text/plain" });

  if (window.navigator.msSaveBlob) {
    window.navigator.msSaveBlob(blob, "shiftjs.csv");
    window.navigator.msSaveOrOpenBlob(blob, "shiftjs.csv");
  } else {
    $(this).attr('href', window.URL.createObjectURL(blob));
  }
})

$('.exampleU').on('click', function () {
  let blob = new Blob([window.example_import], { "type": "text/plain" });

  if (window.navigator.msSaveBlob) {
    window.navigator.msSaveBlob(blob, "utf8.csv");
    window.navigator.msSaveOrOpenBlob(blob, "utf8.csv");
  } else {
    $(this).attr('href', window.URL.createObjectURL(blob));
  }
})

$('#export_btn').on('click', function (f) {
  let export_type = $('input:radio[name="radios_export"]:checked').val()
  const exportBtn = document.getElementById('export_btn');
  const type = exportBtn.dataset.type;

  $.ajax({
    url: '/ugc/get_orders?type=' + type + '&export_type=' + export_type,
    method: 'GET',
    success: function(response) {
      const link = document.createElement('a');
      if (window.location.origin === 'https://ugc-creative.com') {
        link.href = window.location.origin + '/ugc' + response.download_url;
      } else {
        link.href = response.download_url;
      }
      link.download = 'orders.csv';
      console.log(link);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    error: function(error) {
        console.error(error);
    },
    complete: function(response) {
      $.ajax({
        url: '/ugc/delete_csv',
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
        data: {
            download_url: response.responseJSON.download_url
        },
        success: function() {
          console.log('CSVファイルが削除されました');
        },
        error: function(error) {
          console.error('CSVファイルの削除に失敗しました:', error);
        }
      });
    }
  });
})