function fix_date(d) {
  return `${d.getFullYear()}-${('00' + (d.getMonth() + 1)).slice(-2)}-${('00' + d.getDate()).slice(-2)}`
}
function display_date(sd, ed) {
  $('#daterangepicker_disp').val(`${fix_date(sd)} / ${fix_date(ed)}`)
}
function dr_search() {
  window.g_datatable.search($('[name = "daterangepicker_disp"]').val(), 'daterange');
  if ($('.calendar').hasClass('display-none')) {
    $('#daterangepicker').css('left', '')
  }
}

jQuery(document).ready(function () {
  $('.daterangepicker-btn').on('click', function () {
    $('#daterangepicker').removeClass('display-none');
    $('#daterangepicker').addClass('display-flex');
    return false
  })

  $('#daterangepicker').on('click', function () {
    return false
  })

  $('#kt_body').on('click', function () {
    if ($('#daterangepicker').hasClass('display-flex')) {
      $('#daterangepicker').removeClass('display-flex');
      $('#daterangepicker').addClass('display-none');
    }
  });

  $('#add_date').on('click', function () {
    let sd = $('[name = "daterangepicker_start"]').val()
    let ed = $('[name = "daterangepicker_end"]').val()
    if (sd.length == 10 && ed.length == 10 && new Date(sd) <= new Date(ed)) {
      $('#daterangepicker_disp').val(`${sd} / ${ed}`)
      $('#daterangepicker').removeClass('display-flex');
      $('#daterangepicker').addClass('display-none');
      dr_search()
    }
  })

  $('#clear_date').on('click', function () {
    $('#daterangepicker_disp').val('')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    dr_search()
  })

  $('[data-range-key = "今日"]').on('click', function () {
    let sd = new Date()
    let ed = new Date()
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "昨日"]').on('click', function () {
    let sd = new Date()
    let ed = new Date()
    sd.setDate(sd.getDate() - 1);
    ed.setDate(ed.getDate() - 1);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "１週間以内"]').on('click', function () {
    let sd = new Date()
    let ed = new Date()
    sd.setDate(sd.getDate() - 7);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "１ヶ月以内"]').on('click', function () {
    let sd = new Date()
    let ed = new Date()
    sd.setDate(sd.getDate() - 30);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "今月以内"]').on('click', function () {
    let ed = new Date()
    let sd = new Date(ed.getFullYear(), ed.getMonth(), 1);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "先月以内"]').on('click', function () {
    let ed = new Date()
    let sd = new Date(ed.getFullYear(), ed.getMonth() - 1, 1);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "先月"]').on('click', function () {
    let now = new Date()
    let sd = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    let ed = new Date(now.getFullYear(), now.getMonth(), 1);
    ed.setDate(ed.getDate() - 1);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "今年以内"]').on('click', function () {
    let ed = new Date();
    let sd = new Date(ed.getFullYear(), 0, 1);
    $('.calendar').addClass('display-none')
    $('#daterangepicker').removeClass('display-flex');
    $('#daterangepicker').addClass('display-none');
    display_date(sd, ed)
    dr_search()
  })

  $('[data-range-key = "Custom Range"]').on('click', function () {
    $('.calendar').removeClass('display-none')

    let arrows = {
      leftArrow: '<i class="la la-angle-left"></i>',
      rightArrow: '<i class="la la-angle-right"></i>'
    }
    $('.wrapper-inline-calendar-start').datepicker({
      todayHighlight: true,
      templates: arrows
    }).on('changeDate', function (e) {
      $('[name = "daterangepicker_start"]').val(fix_date(e.date));
    });
    $('.wrapper-inline-calendar-end').datepicker({
      todayHighlight: true,
      templates: arrows
    }).on('changeDate', function (e) {
      $('[name = "daterangepicker_end"]').val(fix_date(e.date));
    });

    let w = $('#daterangepicker').width() - $('#input-group-date-wrapper').width()
    $('#daterangepicker').css('left', '-' + w + 'px')
  })
})