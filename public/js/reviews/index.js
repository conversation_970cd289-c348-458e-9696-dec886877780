"use strict";
// Class definition

var KTDatatableAutoColumnHideDemo = function () {
	// Private functions

	// basic demo
	var demo = function () {

		window.g_datatable = $('#kt_datatable').KTDatatable({
			// datasource definition
			data: {
				type: 'remote',
				source: {
					read: {
						url: '/ugc/reviews/search',
						params: {
							query: {
								'review_widget_id': $('#review_widget_id').val()
							}
						}
					},
				},
				pageSize: 10,
				saveState: false,
				serverPaging: false,
				serverFiltering: true,
				serverSorting: false,
			},

			layout: {
				scroll: false,
				class: 'datatable-brand'
			},

			detail: {
				title: 'Load sub table',
				content: subTableInit,
			},

			// column sorting
			sortable: true,

			pagination: true,

			translate: {
				toolbar: {
					pagination: {
						items: {
							info: '{{total}} レコードの内 {{start}} - {{end}}のレコードを表示中'
						}
					}
				}
			},

			// columns definition
			columns: [
				{
					field: 'id',
					title: '',
					sortable: false,
					width: 30,
					textAlign: 'center',
				},
				{
					field: '',
					title: '',
					sortable: false,
					width: 30,
					textAlign: 'center',
					selector: true
				},
				{
					field: 'name_of_reviewer',
					title: 'レビュー',
					width: 350,
					template: function (row) {
						if (!row.ratings_of_review) {
							return '-'
						}
						let s = Math.floor(row.ratings_of_review)
						let f = row.ratings_of_review - s
						let s_arr = new Array(5).fill(`<i class="far fa-star rating-avg"></i>`)
						for (let i = 0; i < s; i++) {
							s_arr[i] = `<i class="fas fa-star rating-avg"></i>`
						}
						if (f > 0) {
							s_arr[s] = `<i class="fas fa-star-half-alt rating-avg"></i>`
						}
						let image = ''
						if (row.image_path) {
							image = `
              <div class="review_images pull-left">
                <div class="divImgCheckBox">
                  <img class="" src="${row.image_path}" style="max-width: 150px;margin-top: 10px;">
                </div>
              </div>
              `
						}

						let display = row.description_of_review.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;').replace(/`/g, '&#x60;').replace(/\r?\n/g, '<br>')
						let review = `<div class="list_review_content">${display}</div>
							<div style="margin-top: 5px;">
								<a class="edit-admin-comment" data-id="${row.id}"
								href="javascript:void(0)" data-toggle="modal" data-target="#modal-review-edit" onclick="change_review(this)"
								data-review="${row.description_of_review}" data-published="${row.published}" data-new="0">
									<i class="la la-edit"></i>編集
								</a>
							</div>
						`

						let reply = ''
						if (row.description_of_reply) {
							reply = `<div style="background: #f1f1f1;margin-top: 12px;padding: 15px;">
                <span class="admin-comment">
                  <span>${row.description_of_reply}</span>
                </span>
                <div style="margin-top: 5px;">
                  <a class="edit-admin-comment" data-id="${row.id}"
									href="javascript:void(0)" data-toggle="modal" data-target="#modal-reply-edit" onclick="change_reply(this)"
									data-reply="${row.description_of_reply}" data-published="${row.published_reply}" data-new="0">
                    <i class="la la-edit"></i>編集
                  </a>
                </div>
              </div>`
						}

						// 項目別評価を追加
						const items = [
							{ name: row.item_rating_name_0, rating: row.item_rating1 },
							{ name: row.item_rating_name_1, rating: row.item_rating2 },
							{ name: row.item_rating_name_2, rating: row.item_rating3 },
							{ name: row.item_rating_name_3, rating: row.item_rating4 },
							{ name: row.item_rating_name_4, rating: row.item_rating5 }
						];

						// 各項目別評価のHTMLを生成
						const item_html = items
							.filter(profile => profile.name && profile.name !== 'NULL')
							.map(profile => {
								if (!profile.name) {
									return '';
								}
								// 星評価を生成
								let stars = '';
								if (profile.rating !== null && profile.rating !== undefined) {
									let s = Math.floor(profile.rating);
									let f = profile.rating - s;
									let s_arr = new Array(5).fill(`<i class="far fa-star rating-avg"></i>`);

										for (let i = 0; i < s; i++) {
											s_arr[i] = `<i class="fas fa-star rating-avg"></i>`;
										}
										if (f > 0) {
											s_arr[s] = `<i class="fas fa-star-half-alt rating-avg"></i>`;
										}

									stars = s_arr.join('');
								} else {
									stars = '<span class="no-rating">評価なし</span>';
								}

							return `
							<div class="profile-rating" style="display: flex; flex-direction: column; align-items: flex-start; margin-bottom: 5px;">
								<p style=" font-weight: bold; margin-bottom: 5px; text-align: left; width: 100%;">${profile.name}</p>
								<div style="display: flex; justify-content: flex-start; width: 100%;">${stars}</div>
							</div>
							`;
							})
							.join('');

							// レビュワー情報
							const reviewer_name = row.name_of_reviewer || '名前:-';
							const reviewer_nickname = row.nick_name_of_reviewer || 'ニックネームなし';

							const gender_list = {
								1: '男性',
								2: '女性'
							};
							const reviewer_gender = row.gender !== null && row.gender !== undefined? `性別: ${gender_list[row.gender] || '不明'}`: '-';

							const age_list = {
								1: '10~20代',
								2: '30~40代',
								3: '50~60代',
								4: '70~80代',
								default: '-'
							};
							const reviewer_age = `年齢: ${age_list[row.age] || age_list.default}`;

							const profileString_first = row.profileString_first
							const profileString_second = row.profileString_second

							const reviewer_date = row.updated_at ? `${row.review_date_time.split(' ')[0]}に回答` : '-';

						return `
							<div style="font-size: 14px; color: #333; line-height: 1.6; margin-bottom: 20px;">
								<p><strong>総合評価:</strong> ${s_arr.join('')}</p>
								<div style="margin-bottom: 10px; color: #666; font-size: 12px;">
									<div style="display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 5px;">${item_html}</div>
									<p>
									${reviewer_name} |	${reviewer_age} | ${reviewer_gender} | ${reviewer_date}
									</p>
								</div>
								<div style="margin-bottom: 10px; color: #666; font-size: 12px;">
									<p>
									${profileString_first}
									</p>
									<p>
									${profileString_second}
									</p>
								</div>
								<div style="margin-top: 10px;">
									<p style="font-weight: bold; margin-bottom: 5px;">${row.title_of_review}</p>
									<p>${review}</p>
									<div style="margin-top: 10px;">
										${image}
									</div>
								</div>
								<div style="margin-top: 20px; padding-top: 10px;">
									${reply}
								</div>
							</div>
            `
					},
				}, {
					field: 'published',
					title: '公開',
					width: 60,
					textAlign: 'center',
					template: function (row) {
						return `<span class="switch switch-icon">
            <label>
             <input class="check-published" type="checkbox" ${row.published == 1 ? 'checked="checked"' : ''}
             name="select" data-id="${row.id}" onchange="change_published(this)"/>
             <span></span>
            </label>
           </span>`
					},
				}, {
					field: 'score',
					title: 'スコア',
					width: 60,
					textAlign: 'center',
					template: function (row) {
						return row.displayTotalScore
					},
				}, {
					field: 'like_counts',
					title: 'いいね数',
					width: 60,
					textAlign: 'center',
					template: function (row) {
						let s = row.like_counts - row.bad_counts
						if (s >= 0) {
							return `<span class="text-success">${s}</span>`
						} else {
							return `<span class="text-danger">${s}</span>`
						}
					},
				}, {
					field: 'review_date_time',
					title: '投稿日時',
					textAlign: 'center',
					width: 80,
					template: function (row) {
						let d = new Date(row.review_date_time)
						return `${d.getFullYear()}-${('00' + (d.getMonth() + 1)).slice(-2)}-${('00' + d.getDate()).slice(-2)}<br>
						${('00' + d.getHours()).slice(-2)}:${('00' + d.getMinutes()).slice(-2)}:${('00' + d.getSeconds()).slice(-2)}`
					},
				},
				{
					field: 'sortOrder',
					title: $('#review_widget_id').val() == '' ? '' : 'ソート順',
					textAlign: 'center',
					width: $('#review_widget_id').val() == '' ? 0 : 80,
					template: function (row) {
						if ($('#review_widget_id').val() == '') {
							return ''
						}
						return `<input type="number" class="form-control" value="${row.sortOrder}" onchange="change_sort_order(this)"
						min="0" style="width:90%" data-review_widget_id="${$('#review_widget_id').val()}" data-review_id="${row.id}"/>`
					},
				}, {
					field: 'アクション',
					title: 'アクション',
					textAlign: 'center',
					sortable: false,
					width: 80,
					overflow: 'visible',
					template: function (row) {
						let add_comment = ''
						if (!row.description_of_reply) {
							add_comment = `<li class="navi-item">
									<a href="#" class="navi-link add-comment" data-id="${row.id}" target="_blank"
									data-toggle="modal" data-target="#modal-reply-edit" onclick="change_reply(this)"
									data-reply="${row.description_of_reply}" data-published="${row.published_reply}" data-new="1"
									>
											<span class="navi-icon"><i class="far fa-edit"></i></span>
											<span class="navi-text">返信</span>
									</a>
							</li>`
						}
						let delete_comment_set = ''
						if ($('#review_widget_id').val() != '') {
							delete_comment_set = `<li class="navi-item">
									<a href="#" class="navi-link" data-id="${row.id}" target="_blank"
									data-toggle="modal" data-target="#modal-comment-delete" onclick="delete_comment(this)"
									>
											<span class="navi-icon"><i class="far fa-trash-alt"></i></span>
											<span class="navi-text">セットから削除</span>
									</a>
							</li>`
						}
						return `
							<div class="dropdown dropdown-inline">
									<a href="javascript:;" class="btn btn-sm btn-clean btn-icon mr-2" data-toggle="dropdown">
										<i class="fas fa-ellipsis-h"></i>
									</a>
									<div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
											<ul class="navi flex-column navi-hover py-2">
													<li class="navi-item">
															<a href="#" class="navi-link review-edit-link" data-id="${row.id}"
															data-product_id="${row.productDisplayId}" data-product_image_url="${row.productImageUrl}"
															data-product_name="${row.productName}" data-display_order_id="${row.display_order_id}"
															data-toggle="modal" data-target="#modal-review-preview" onclick="review_preview(this)"
															>
																	<span class="navi-icon"><i class="fas fa-external-link-alt"></i></span>
																	<span class="navi-text">開く</span>
															</a>
													</li>
													${delete_comment_set}
													${add_comment}
											</ul>
									</div>
							</div>
						`;
					},
				}
			],
		});

		function subTableInit(e) {
			let row = e.data;
			$('<div/>').attr('id', 'child_data_ajax_' + e.data.id).appendTo(e.detailCell)
			let html = `
			<div class="child_data_ajax">
				<div class="row">
					<div class="col-md-2">
						<img class="" src="${row.productImageUrl}" style="max-width:100%;">
					</div>
					<div class="col-md-5">
						<p>
								<span class="label label-light-dark label-inline mr-2">
										商品ID:
								</span>
								<span> ${row.productDisplayId ? row.productDisplayId : ' -'}</span>
						</p><p>
								<span class="label label-light-dark label-inline mr-2">
										商品名:
								</span>
								<a href="${row.productUrl}" target="_blank"><span> ${row.productName ? row.productName : ' -'}</span></a>
						</p><p>
								<span class="label label-light-dark label-inline mr-2">
										注文ID:
								</span>
								<span> ${row.display_order_id ? row.display_order_id : ' -'}</span>
						</p><p>
							<span class="label label-light-dark label-inline mr-2">
									投稿経路:
							</span>
							<span> ${row.displayChannel ? row.displayChannel : ' -'}</span>
						</p>
					</div>
					<div class="col-md-5">
						<p>
								<span class="label label-light-dark label-inline mr-2">
										お名前:
								</span>
								<span> ${row.name_of_reviewer ? row.name_of_reviewer : ' -'}</span>
						</p>
						<p>
								<span class="label label-light-dark label-inline mr-2">
										メールアドレス:
								</span>
								<span> ${row.email_of_reviewer ? row.email_of_reviewer : ' -'}</span>
						</p>
						<p>
								<span class="label label-light-dark label-inline mr-2">
										電話番号:
								</span>
								<span> ${row.phone_of_reviewer ? row.phone_of_reviewer : ' -'}</span>
						</p>
					</div>
				</div>
			</div>
			`
			$(`#child_data_ajax_${e.data.id}`).html(html)
		}


		$('#kt_datatable_search_query').on('change', function () {
			g_datatable.search($(this).val().toLowerCase(), 'freeword');
		});

		$('#kt_datatable_search_query_email').on('change', function () {
			g_datatable.search($(this).val().toLowerCase(), 'email');
		});

		$('#status').on('change', function () {
			g_datatable.search($(this).val(), 'status');
		});

		$('#with_image').on('change', function () {
			g_datatable.search($(this).val(), 'with_image');
		});

		$('#product_display_id').on('change', function () {
			g_datatable.search($(this).val(), 'product_id');
		});

		$('#review_widget_id').on('change', function () {
			if ($(this).val() == '') {
				location.href = '/ugc/reviews'
			} else {
				location.href = '?review_widget_id=' + $(this).val()
			}
		});

		window.g_datatable.on(
			'datatable-on-check datatable-on-uncheck',
			function (e) {
				var checkedNodes = window.g_datatable.rows('.datatable-row-active').nodes();
				var count = checkedNodes.length;
				window.selected_ids = Array.from(checkedNodes).map(v => v.children[0].getAttribute('aria-label'))

				$('#kt_datatable_selected_records').html(count);
				if (count > 0) {
					$('#kt_datatable_group_action_form').collapse('show');
				} else {
					$('#kt_datatable_group_action_form').collapse('hide');
				}
			}
		);

		window.g_datatable.on(
			'datatable-on-ajax-done',
			function (e, d) {
				$('#review_count').text(d.length)
			}
		);

	};

	return {
		// public functions
		init: function () {
			demo();
		},
	};
}();

jQuery(document).ready(function () {
	KTDatatableAutoColumnHideDemo.init();
});

function change_published(_this) {
	let id = $(_this).data('id')
	$.ajax({
		type: "POST",
		url: `/ugc/reviews/update/published/${id}`,
		headers: {
			'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
		},
		data: {
			id: id,
			published: ($(_this).prop('checked') ? 1 : 0)
		},
		success: function (data) {
			toastr.success(data.msg)
		}
	});
}

function change_reply(_this) {
	let id = $(_this).data('id')
	let published = $(_this).data('published')
	let reply = $(_this).data('reply')
	let is_new = parseInt($(_this).data('new'))

	if (is_new) {
		$('#modal-reply-edit-str').removeClass('display-none')
	} else {
		$('#modal-reply-edit-str').addClass('display-none')
	}

	$(`#modal-reply-edit [name = reply]`).val(reply)
	$('#modal-reply-edit-form').attr('action', '/ugc/reviews/update/' + id)
	$(`#modal-reply-edit [name = published]`).prop('checked', published == '1')
	$(`#modal-reply-delete-form`).attr('action', '/ugc/reviews/update/' + id)
}

function delete_comment(_this) {
	let id = $(_this).data('id')
	let review_widget_id = $('#review_widget_id').val()

	$(`#modal-comment-delete [name = review_widget_id]`).val(review_widget_id)
	$('#modal-comment-delete-form').attr('action', '/ugc/reviews/delete/' + id)
}

function change_review(_this) {
	let id = $(_this).data('id')
	let published = $(_this).data('published')
	let review = $(_this).data('review')

	$(`#modal-review-edit [name = review]`).val(review)
	$('#modal-review-edit-form').attr('action', '/ugc/reviews/update/description_of_review/' + id)
	$(`#modal-review-edit [name = published]`).prop('checked', published == '1')
}

function change_sort_order(_this) {
	let id = $(_this).data('review_id')
	let review_widget_id = $(_this).data('review_widget_id')
	let v = $(_this).val()

	if (v != '') {
		$.ajax({
			type: "POST",
			url: `/ugc/reviews/update/sort_order/${id}`,
			headers: {
				'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
			},
			data: {
				id: id,
				review_widget_id: review_widget_id,
				sort_order: v
			},
			success: function (data) {
				toastr.success(data.msg)
			}
		});
	}

}

function review_preview(_this) {
	let id = $(_this).data('id')
	let product_display_id = $(_this).data('product_id')
	let product_image_url = $(_this).data('product_image_url')
	let product_name = $(_this).data('product_name')

	new ugcReview(
		{
			company_id: $('#company_id_hidden').val(),
			user_qid: $('#user_qid_hidden').val(),
		},
		{
			get_from_class: false,
			product_id: product_display_id,
			ignore_published: true,
			review_id: id
		},
		$('#api_host_hidden').val(),
		function () {
			// 他のコメントは非表示
			$('#UgcCreativeReview-ratingStarWriteReview').css('display', 'none')
			$('#UgcCreativeReview-moreReviewBtn').css('display', 'none')

			$('#modal-review-preview-img').attr('src', product_image_url)
			$('#modal-review-preview-product-name').html(product_name)
		}
	)
}
