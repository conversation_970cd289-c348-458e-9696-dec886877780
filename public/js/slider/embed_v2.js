var auto_play_video = document.getElementById("content-body").getAttribute("data-auto-play-video");

let video_sliders = document.querySelectorAll('video.cf-ugcc-thumb-img')
let play_video_btns = document.querySelectorAll('img.video-play-btn')
if (auto_play_video == '1') {
  video_sliders.forEach(function(video_slider) {
    video_slider.controls = false;
    video_slider.loop = true;
    video_slider.play();
  });
} else {
  video_sliders.forEach(function(video_slider) {
    video_slider.controls = false;
    video_slider.play();
    setTimeout(function() {
      video_slider.pause();
    }, 100);
  });
  play_video_btns.forEach(function(btn) {
    btn.onclick = function(e) {
      let ig_id = e.target.getAttribute('data-ig_id')

      let html = document.getElementById(ig_id).innerHTML

      // 一旦モーダル表示用のhtmlを送る
      window.parent.postMessage(["openModal", { frame_id: this.frame_id, html: html }], "*")

      // 取得したig_idの中身をエスケープ処理
      let new_ig = encodeURIComponent(ig_id);

      // クリックしたスライドをサーバーに送る
      if (!this.is_preview) {
        navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_ig}&qid=${this.qid}&type=click`)
      }
    };
  });
}

class ugcEmbed {
  constructor(qid, api_url, is_set_slider = true, option = {}) {
    console.log('[ugcEmbed] Start')
    this._initial_set(qid, api_url, is_set_slider, option)
  }

  _initial_set(qid, api_url, is_set_slider, option) {
    this.qid = qid
    this.api_url = api_url
    this.takejsLoaded = false
    this.is_preview = option.is_preview
    window.parent.postMessage(["loaded"], "*")
    window.addEventListener('message', e => {
      if (e.data.action == 'sendFrameId') {
        this.frame_id = e.data.message
        this.takejsLoaded = true
        if (is_set_slider) {
          this._set_slider()
        }
      } else if (e.data.action == 'sendProductId') {
        localStorage.setItem('product_id', e.data.message.product_id)
        localStorage.setItem('ig_id', e.data.message.ig_id)
        localStorage.setItem('qid', e.data.message.qid)
      } else if (e.data.action == 'takejsLoaded') {
        if (!this.takejsLoaded) {
          window.parent.postMessage(["loaded"], "*")
        }
      }
    })
    console.log('[ugcEmbed] End')
  }

  _set_slider() {
    // swiperの設定を取得
    let req = new XMLHttpRequest()
    req.onreadystatechange = () => {
      if (req.readyState == 4) {
        if (req.status == 200) {
          let slider_option = JSON.parse(req.responseText)
          this._define_display(slider_option)

          // iframeの高さ送信
          setTimeout(() => {
            let height = document.getElementById('content-body').offsetHeight + 5
            if (height > 5) {
              window.parent.postMessage(["setHeight", { frame_id: this.frame_id, height: height }], "*")
            }
          }, 1000)
        }
      }
    }
    req.open('GET', `${this.api_url}/ugc/api/get_option?qid=${this.qid}`, true)
    req.send(null)

    // モーダルのhtmlをすべて送る
    let c = document.getElementsByClassName('content-wrap')
    let ig_ids = Array.from(c).map(v => v.getAttribute('id'))
    let ig_htmls = Array.from(c).map(v => v.innerHTML)
    let ig_tmp = {}
    ig_ids.forEach((v, i) => ig_tmp[v] = ig_htmls[i])
    window.parent.postMessage(["getIgs", { frame_id: this.frame_id, data: ig_tmp }], "*")
  }

  _set_open_modal() {
    let elem = document.getElementsByClassName('modal_ig')
    for (let i = 0; i < elem.length; i++) {
      elem[i].addEventListener("click", (e) => {
        let ig_id = e.target.getAttribute('data-ig_id')

        let html = document.getElementById(ig_id).innerHTML

        // 一旦モーダル表示用のhtmlを送る
        window.parent.postMessage(["openModal", { frame_id: this.frame_id, html: html }], "*")

        // 取得したig_idの中身をエスケープ処理
        let new_ig = encodeURIComponent(ig_id);

        // クリックしたスライドをサーバーに送る
        if (!this.is_preview) {
          navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_ig}&qid=${this.qid}&type=click`)
        }
      }, false);
    }
  }

  _define_display(slider_option) {
    if (slider_option.display_type == 'general') {
      this._define_general(slider_option)
    } else if (slider_option.display_type == 'slider') {
      this._define_swiper(slider_option)
    } else if (slider_option.display_type == 'lvuy') {
      this._define_lvuy(slider_option);
    }
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/indicate_noig?qid=${this.qid}&type=look_group`)
    }
  }

  _define_general(slider_option) {
    let slide_count, piece_width, piece_height, margin, slide_vertical_count, margin_bottom
    let slide_actual_count = Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).length

    if (window.innerWidth < 400 || window.innerWidth < 480 && slider_option.display_type == 'general' && slider_option.sd_wide === 2) {
      slide_count = Math.min(2, slider_option.sd_wide)
      slide_vertical_count = Math.min(slider_option.sd_short, Math.ceil(slide_actual_count / slide_count))
      switch (slide_count)
      {
        case 1:
          piece_height = 430
          break;
        case 2:
          piece_height = 346
          break;
        case 3:
          break;
      }
      piece_width = slide_count == 2 ? window.innerWidth * 0.45 : 280
      
      document.getElementById('true-swiper').style.cssText += 'width: 100% !important';
    } else if (window.innerWidth < 480) {
      slide_count = slider_option.sd_wide
      slide_vertical_count = Math.min(slider_option.sd_short, Math.ceil(slide_actual_count / slide_count))
      piece_width = slide_count == 3 ? 113 : 143
      piece_height = 320
    } else if (480 <= window.innerWidth) {
      slide_count = slider_option.pc_wide
      slide_vertical_count = Math.min(slider_option.pc_short, Math.ceil(slide_actual_count / slide_count))
      piece_width = 183
      piece_height = 350
    }

    if (slider_option.is_display_comment == 0) {
      piece_height = piece_width
      margin = 0
      margin_bottom = 0
    } else {
      margin = 5
      margin_bottom = 5
    }

    let w = piece_width * slide_count + margin * (slide_count - 1)
    document.getElementsByClassName('cf-ugcc-swiper-container')[0].style.width = w + 'px'
    document.getElementsByClassName('cf-ugcc-swiper-wrapper')[0].style['flex-wrap'] = 'wrap'
    document.getElementsByClassName('cf-ugcc-swiper-wrapper')[0].style['align-content'] = 'flex-start'
    document.getElementsByClassName('more-view-button')[0].style.display = 'block'
    document.getElementById('true-swiper').style.display = 'block'
    document.getElementById('true-swiper').style.height = (piece_height + 15) * slide_vertical_count + 230 + 'px'
    document.getElementsByClassName('swiper-button-prev')[0].style.display = 'none'
    document.getElementsByClassName('swiper-button-next')[0].style.display = 'none'

    let igs = []
    Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach((v, i) => {
      v.style.width = piece_width + 'px'
      v.style.height = piece_height + 'px'
      v.style['margin-bottom'] = margin_bottom + 'px'
      if (i % slide_count != 0) {
        v.style['margin-left'] = margin + 'px'
      }

      // 指定段目以降初期は非表示
      if (i >= slide_count * slide_vertical_count) {
        v.style.display = 'none'
      } else {
        v.style.display = 'block'
        igs.push(v.getAttribute('data-ig_id'))
      }
    })

    // 取得したig_idの中身をエスケープ処理
    let new_igs = igs.map(value => encodeURIComponent(value));
    // blockになっているスライドを送信
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_igs.join(',')}&qid=${this.qid}&type=look`)
    }

    // スライドが全て見えていたらもっと見るボタンを消す
    if (Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).every(v => v.style.display == 'block')) {
      document.getElementsByClassName('more-view-button')[0].style.display = 'none'
    }

    // もっと見るボタン押下時
    document.getElementsByClassName('more-view-button')[0].addEventListener("click", () => {
      let tmp_count = 0
      igs = []
      Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach(v => {
        if (v.style.display == 'none' && tmp_count < slide_count) {
          v.style.display = 'block'
          tmp_count += 1
          igs.push(v.getAttribute('data-ig_id'))
        }
      })

      // 取得したig_idの中身をエスケープ処理
      let new_igs = igs.map(value => encodeURIComponent(value));

      // blockになっているスライドを送信
      if (!this.is_preview) {
        navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_igs.join(',')}&qid=${this.qid}&type=look`)
      }

      // スライドが全て見えていたらもっと見るボタンを消す
      if (Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).every(v => v.style.display == 'block')) {
        document.getElementsByClassName('more-view-button')[0].style.display = 'none'
      }

      // iFrameの高さを調整
      let height = document.getElementById('content-body').offsetHeight + 10
      window.parent.postMessage(["setHeight", { frame_id: this.frame_id, height: height + piece_height }], "*")
      height = document.getElementById('true-swiper').offsetHeight
      document.getElementById('true-swiper').style.height = height + piece_height + 10
    });

    this._set_open_modal()
  }

  _define_swiper(slider_option) {
    // 横幅の定義
    let slide_count, prev_pos, next_pos, margin, slide_vertical_count, piece_height, piece_width
    if (window.innerWidth < 400) {
      slide_count = Math.min(2, slider_option.sd_wide)
      slide_vertical_count = slider_option.sd_short
      prev_pos = (w) => `-5px`
      next_pos = (w) => `calc(100vw - 50px)`
      margin = 5
      piece_height = 330
      piece_width = 142
    } else if (window.innerWidth < 480) {
      slide_count = slider_option.sd_wide
      slide_vertical_count = slider_option.sd_short
      prev_pos = (w) => `-5px`
      next_pos = (w) => `calc(100vw - 50px)`
      margin = 5
      piece_height = 330
      piece_width = 162
    } else if (480 <= window.innerWidth) {
      slide_count = slider_option.pc_wide
      slide_vertical_count = slider_option.pc_short
      prev_pos = (w) => `calc(((100vw - 100%) / 2) - 30px)  !important`
      next_pos = (w) => `calc(((100vw - 100%) / 2 + 100%) - 43px)  !important`
      margin = 5
      piece_height = 350
      piece_width = 183
    }

    if (slider_option.is_display_comment == 0) {
      piece_height = piece_width
    }

    let w = piece_width * slide_count + margin * (slide_count - 1)

    var elements = Array.from(document.getElementsByClassName("cf-ugcc-true-swiper"));
    // elements.forEach(x => x.style.width = (w + 20));

    Array.from(document.getElementsByClassName('swiper-button-prev')).forEach(v => {
      v.style.left = prev_pos(w)
      v.style.top = `calc(${piece_height} / 2 + 5px)`
    })
    Array.from(document.getElementsByClassName('swiper-button-next')).forEach(v => {
      v.style.left = next_pos(w)
      v.style.top = `calc(${piece_height} / 2 + 5px)`
    })

    Array.from(document.getElementsByClassName('cf-ugcc-true-swiper')).forEach((v, i) => {
      if (slide_vertical_count <= i) {
        v.style.display = 'none'
      }
      if (slide_vertical_count != i + 1) {
        document.getElementById(`logo_${i}`).style.display = 'none'
      }

      v.style.height = piece_height + 80 + 'px'
    })

    Array.from(document.getElementsByClassName('cf-ugcc-swiper-container')).forEach(v => {
      v.style.width = w + 'px'
    })

    Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach((v, i) => {
      v.style.width = piece_width + 'px'
      v.style.height = piece_height + 'px'
    })

    let vert_arr = [...Array(slide_vertical_count).keys()]
    vert_arr.forEach(i => {
      this.my_swiper = new Swiper('.swiper-container' + i, {
        spaceBetween: margin,
        slidesPerView: slide_count,
        loop: true,
        navigation: {
          nextEl: '.swiper-button-next' + i,
          prevEl: '.swiper-button-prev' + i,
        },
        scrollbar: {
          el: '.swiper-scrollbar' + i
        },
        on: {
          slideChange: (e) => {
            // 見たスライドをサーバーに送る
            let tmp_arr = Array.from(document.querySelectorAll("div.cf-ugcc-swiper-div img.cf-ugcc-thumb-img, div.cf-ugcc-swiper-div video.cf-ugcc-thumb-img"))
            let end_index = e.activeIndex + e.params.slidesPerView
            let display_arr = tmp_arr.concat(tmp_arr).slice(e.activeIndex, end_index)
            let igs = display_arr.map(v => v.getAttribute('data-ig_id')).join(',')
            let parts = igs.split(',');
            // 取得したig_idの中身をエスケープ処理
            let new_parts = parts.map(part => encodeURIComponent(part));
            let new_igs = new_parts.join(',');
            if (!this.is_preview) {
              navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_igs}&qid=${this.qid}&type=look`)
            }
          },
          afterInit: () => {
            this._set_open_modal()
          }
        }
      })
    })

    // swiper定義後の挙動
    if (slider_option.is_display_comment == 0) {
      let tmp
      Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach((v, i) => {
        v.style.height = v.style.width
        if (i == 0) {
          tmp = v.style.width
        }
      })

      Array.from(document.getElementsByClassName('swiper-button-prev')).forEach(v => {
        v.style.top = `calc(${tmp} / 2 + 15px)`
      })
      Array.from(document.getElementsByClassName('swiper-button-next')).forEach(v => {
        v.style.top = `calc(${tmp} / 2 + 15px)`
      })
      Array.from(document.getElementsByClassName('cf-ugcc-true-swiper')).forEach((v, i) => {
        v.style.height = `calc(${tmp} + 200px)`
      })

    } else {
      Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach((v, i) => {
        v.style.height = piece_height + 'px'
      })
      Array.from(document.getElementsByClassName('swiper-button-prev')).forEach(v => {
        v.style.top = `calc(${piece_height + 220 + 'px'} / 2 - 70px)`
      })
      Array.from(document.getElementsByClassName('swiper-button-next')).forEach(v => {
        v.style.top = `calc(${piece_height + 220 + 'px'} / 2 - 70px)`
      })
      Array.from(document.getElementsByClassName('cf-ugcc-true-swiper')).forEach((v) => {
        v.style.height = piece_height + 220 + 'px'
      })
    }


  }

  _define_lvuy(slider_option) {
    console.log(slider_option);
    let slide_count, piece_width, piece_height, margin = 5, slide_vertical_count, margin_bottom = 5, piece_comment_height
    let slide_actual_count = Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).length
    let isMobile = false
    let thumb_height = 0

    if (window.innerWidth < 480) {
      slide_count = slider_option.sd_wide
      slide_vertical_count = Math.min(slider_option.sd_short, Math.ceil(slide_actual_count / slide_count))

      switch (slide_count) {
        case 1:
          piece_width = '100%'
          thumb_height = slider_option.display_style == 'square' ? 388 : 388 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 512 : 512 + (388 * 0.7777) + 30
          
          piece_comment_height = '9%'
          break;
        case 2:
          piece_width = '49.2%'
          thumb_height = slider_option.display_style == 'square' ? 190 : 190 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 333 : 333 + (190 * 0.7777) + 20
          piece_comment_height = '17%'
          break;
        case 3:
          piece_width = '32.4%'
          thumb_height = slider_option.display_style == 'square' ? 124 : 124 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 282 : 282 + (124 * 0.7777) + 15
          piece_comment_height = '25%'
          break;
        default:
          piece_width = 100
          piece_height = 520
          break;
      }
      isMobile = true
      document.getElementById('true-swiper').style.cssText += 'width: 100% !important';
    } else if (480 <= window.innerWidth) {
      slide_count = slider_option.pc_wide
      slide_vertical_count = Math.min(slider_option.pc_short, Math.ceil(slide_actual_count / slide_count))
      switch (slide_count) {
        case 1:
          piece_width = '100%'
          thumb_height = slider_option.display_style == 'square' ? 798 : 798 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 960 : 960 + (798 * 0.7777) + 30
          break;
        case 2:
          piece_width = '49%'
          thumb_height = slider_option.display_style == 'square' ? 390 : 390 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 565 : 565 + (390 * 0.7777) + 20
          break;
        case 3:
          piece_width = '32.5%'
          thumb_height = slider_option.display_style == 'square' ? 258 : 258 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 420 : 420 + (258 * 0.7777) + 15
          break;
        case 4:
          piece_width = '24.5%'
          thumb_height = slider_option.display_style == 'square' ? 194 : 194 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 362 : 362 + (194 * 0.7777) + 5
          break;
        case 5:
          piece_width = '19.5%'
          thumb_height = slider_option.display_style == 'square' ? 154 : 154 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 300 : 300 + (154 * 0.85) + 3
          break;
        case 6:
          piece_width = '16.1%'
          thumb_height = slider_option.display_style == 'square' ? 127 : 127 * 1.7777
          piece_height = slider_option.display_style == 'square' ? 290 : 290 + (127 * 0.7777)
          break;
        default: 
          piece_width = 240
          piece_height = 500
          break;
      }
    }

    let igs = []
    Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach((v, i) => {
      v.style.width = piece_width
      if (slider_option.is_display_comment == 0) {
        if (slider_option.display_style == 'square')
          piece_height = v.offsetWidth;
        else
          piece_height = v.offsetWidth * 1.777;
      }
      v.style.height = piece_height + 'px'
      v.style['margin-bottom'] = margin_bottom + 'px'
      if (i % slide_count != 0) {
        v.style['margin-left'] = margin + 'px'
      }

      if (slider_option.is_display_comment != 0 && piece_comment_height)
        v.querySelector('.grad-item').style.height = piece_comment_height;

      // 指定段目以降初期は非表示
      if (i >= slide_count * slide_vertical_count) {
        v.style.display = 'none'
      } else {
        v.style.display = 'block'
        igs.push(v.getAttribute('data-ig_id'))
      }
    })

    if (slider_option.is_display_comment == 0) {
      Array.from(document.getElementsByClassName('cf-ugcc-thumb-img')).forEach((v, i) => {
        v.style.height = piece_height + 'px'
        v.parentElement.style.height = piece_height + 'px'
      })
    } else {
      Array.from(document.getElementsByClassName('cf-ugcc-thumb-img')).forEach((v, i) => {
        if (thumb_height != 0)
          v.parentElement.style.height = thumb_height + 'px'
      })
    }
    document.getElementsByClassName('cf-ugcc-swiper-container')[0].style.width = '100% !important'
    document.getElementsByClassName('cf-ugcc-swiper-container')[0].style.margin = '0px !important'
    document.getElementsByClassName('cf-ugcc-swiper-wrapper')[0].style['flex-wrap'] = 'wrap'
    document.getElementsByClassName('cf-ugcc-swiper-wrapper')[0].style['align-content'] = 'flex-start'
    document.getElementsByClassName('more-view-button')[0].style.display = 'block'
    document.getElementById('true-swiper').style.display = 'block'
    // document.getElementById('cf-ugcc-true-swiper').style.margin = '0px'
    document.getElementById('true-swiper').style.height = (piece_height + 10) * slide_vertical_count + 200 + 'px'
    document.getElementsByClassName('swiper-button-prev')[0].style.display = 'none'
    document.getElementsByClassName('swiper-button-next')[0].style.display = 'none'
    document.body.style.margin = "0px"

    // 取得したig_idの中身をエスケープ処理
    let new_igs = igs.map(value => encodeURIComponent(value));

    // blockになっているスライドを送信
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_igs.join(',')}&qid=${this.qid}&type=look`)
    }

    // スライドが全て見えていたらもっと見るボタンを消す
    if (Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).every(v => v.style.display == 'block')) {
      document.getElementsByClassName('more-view-button')[0].style.display = 'none'
    }

    // もっと見るボタン押下時
    document.getElementsByClassName('more-view-button')[0].addEventListener("click", () => {
      let tmp_count = 0
      igs = []
      Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).forEach(v => {
        if (v.style.display == 'none' && tmp_count < slide_count) {
          v.style.display = 'block'
          tmp_count += 1
          igs.push(v.getAttribute('data-ig_id'))
        }
      })

      // 取得したig_idの中身をエスケープ処理
      let new_igs = igs.map(value => encodeURIComponent(value));

      // blockになっているスライドを送信
      if (!this.is_preview) {
        navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${new_igs.join(',')}&qid=${this.qid}&type=look`)
      }

      // スライドが全て見えていたらもっと見るボタンを消す
      if (Array.from(document.getElementsByClassName('cf-ugcc-swiper-slide')).every(v => v.style.display == 'block')) {
        document.getElementsByClassName('more-view-button')[0].style.display = 'none'
      }

      // iFrameの高さを調整
      let height = document.getElementById('content-body').offsetHeight + 5
      window.parent.postMessage(["setHeight", { frame_id: this.frame_id, height: height + piece_height }], "*")
      height = document.getElementById('true-swiper').offsetHeight
      document.getElementById('true-swiper').style.height = height + piece_height + 10
    });

    this._set_open_modal()
  }

  // 購入ページ到達後の処理
  send_cv_data() {
    let lastclick_ig_id = localStorage.getItem('ig_id')
    let lastclick_qid = localStorage.getItem('qid')
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/indicate?igs=${lastclick_ig_id}&qid=${lastclick_qid}&type=cv`)
    }

    let lastclick_product_id = localStorage.getItem('product_id')
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/indicate_product?product_ids=${lastclick_product_id}&qid=${lastclick_qid}&type=cv`)
    }

    localStorage.removeItem('ig_id')
    localStorage.removeItem('qid')
    localStorage.removeItem('product_id')
  }
}
