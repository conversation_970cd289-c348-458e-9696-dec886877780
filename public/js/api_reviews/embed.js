class ugcReview {

	constructor(user, product, api_url, after = () => { }, option = { mode: 'display' }) {
		console.log('[ugcReview] Start')

		var loadTimeCount = 0 
		var interval = setInterval(() => {
			if (document.getElementById('UgcCreativeReviewArea')) {
				if (typeof jQuery == 'undefined') {
					this._js_load('https://code.jquery.com/jquery-3.6.0.min.js', () => {
						this._init(user, product, api_url, after, option);
					});
				} else {
					this._init(user, product, api_url, after, option);
				}

				clearInterval(interval)
				return
			}

			loadTimeCount ++
			if (loadTimeCount >= 500) clearInterval(interval)
		}, 100)
	}

	_get_default_ugc_review_html() {
		var xmlHttp = new XMLHttpRequest();
		xmlHttp.open( "GET", `${this.api_url}/reviews/html`, false ); // false for synchronous request
		xmlHttp.send( null );
		return JSON.parse(xmlHttp.responseText)["html"];
	}

	// 初期処理
	_init(user, product, api_url, after, option) {

		this.user_id = user.user_id
		this.user_qid = user.user_qid
		this.valid = true

		if (product.get_from_class) {
			let product_id_dom = document.getElementsByClassName(product.id_class_name)
			this.product_display_id = product_id_dom.length >= 1 ? product_id_dom[0].innerText : null
		} else {
			this.product_display_id = product.product_id
		}

		if (this.product_display_id == 'site') {
			this.product_display_id = null
		}

		if (option.mode == 'function') {
			// functionの時はAPIにとりにいかない
			this.valid = false
		}

		this.get_all_review = product.get_all_review
		this.review_widget_id = product.review_widget_id
		this.ignore_published = product.ignore_published
		this.review_id = product.review_id
		this.api_url = api_url
		this.after_event = after ? after : () => { }

		let directory = '';
		if (product.base_path && product.base_path.match(/^https{0,1}:\/\/.+/is) !== null) {
			directory = product.base_path
		} else if (product.base_path) {
			directory = product.base_path + '/UgcCreativeReviewArea.html'
		} else {
			directory = '/UgcCreativeReviewArea.html'
		}
		if (this.valid) {
			$('#UgcCreativeReviewArea').load(directory, function(response, status, xhr) {
				if (status == 'success') {
					// jsの読み込み
					this._js_load('https://r2.ugc-creative.work/pkg/swiper-bundle.min.js', () => {
						this._set_dom();
					})
				} else {
					console.error('html notfound');
					console.log('load default ugc review html');
					const review_html = this._get_default_ugc_review_html();
					$('#UgcCreativeReviewArea').html(review_html);
					this._js_load('https://r2.ugc-creative.work/pkg/swiper-bundle.min.js', () => {
						this._set_dom();
					});
				}
			}.bind(this));
		} else {
			console.error('[ugcReview] not valid')
		}

		$(document).on('change', '.swiper-slide input[id^=UgcCreativeReview-trigger]', function() {
			var id = $(this).attr('id').replace(/^.+-([0-9]+)$/, '$1');
			if ($(this).prop('checked') === true) {
				if (id < Math.ceil($('.swiper-slide').length / 2)) {
					$('.swiper-slide input#' + $(this).attr('id').replace(/^(.+-)[0-9]+$/, '$1') + (parseInt(id) + Math.ceil($('.swiper-slide').length / 2))).prop('checked', true);
				} else if (id > Math.ceil($('.swiper-slide').length / 2)) {
					$('.swiper-slide label#' + $(this).attr('id').replace(/^(.+-)[0-9]+$/, '$1') + (parseInt(id) - Math.ceil($('.swiper-slide').length / 2))).prop('checked', true);
				}
			} else {
				if (id < Math.ceil($('.swiper-slide').length / 2)) {
					$('.swiper-slide input#' + $(this).attr('id').replace(/^(.+-)[0-9]+$/, '$1') + (parseInt(id) + Math.ceil($('.swiper-slide').length / 2))).prop('checked', false);
				} else if (id > Math.ceil($('.swiper-slide').length / 2)) {
					$('.swiper-slide label#' + $(this).attr('id').replace(/^(.+-)[0-9]+$/, '$1') + (parseInt(id) - Math.ceil($('.swiper-slide').length / 2))).prop('checked', false);
				}
			}
		});
		console.log('[ugcReview] End')
	}

	// 外部JavaScriptロード
	_js_load(s, callback) {
		let script = document.createElement('script')
		script.src = s
		script.onload = () => callback(script)
		document.head.appendChild(script)
	}

	// PC表示判定
	_is_pc() {
		return window.matchMedia('(min-width: 480px)').matches
	}

	// Send engage
	_send_engage(type) {
		if (!localStorage.ugcReviewVisitorId) {
			localStorage.ugcReviewVisitorId = Math.random().toString(32).substring(2)
		}
		let visitor_id = localStorage.ugcReviewVisitorId

		let query = [
			`product_display_id=${this.product_display_id}`,
			`visitor_id=${visitor_id}`,
			`engage_type=${type}`,
			`user_id=${this.user_id}`,
			`user_qid=${this.user_qid}`,
		].join('&')
		navigator.sendBeacon(`${this.api_url}/api/engage/store?${query}`)
	}


	// DOM設定
	_set_dom() {

		let req = new XMLHttpRequest();
		req.onreadystatechange = () => {

			if (req.readyState == 4) {

				if (req.status != 200) {
					return;
				}

				let res = JSON.parse(req.response);

				if (!res['status']) {
					console.error(res['message']);
					return;
				}

				this.reviews = res['data'];
				this.review_setting = res['review_setting'];
				this.review_widget = res['review_widget'];
				this.product = res['product'];
				this.mail_setting = res['mail_setting'];
				this.review_widget_tags = res['review_widget_tags'];
				this.mail_setting_profiles = res['mail_setting_profiles'];
				this.setting = res['setting'];
				this.total_review_count = res['total_review_count'];
				this.is_review_count = res['is_review_count'];

				this.conditions = {
						star: 'all'
					,	other: 'all'
					,	tag: ''
					,	sort_key: ''
					,	sort_asc: false
				}

				this.tmp = {
					'ratings_of_review': 0
				,	'cnt_star_1': 0
				,	'cnt_star_2': 0
				,	'cnt_star_3': 0
				,	'cnt_star_4': 0
				,	'cnt_star_5': 0
				,	'avg_item_rating_1': 0
				,	'avg_item_rating_2': 0
				,	'avg_item_rating_3': 0
				,	'avg_item_rating_4': 0
				,	'avg_item_rating_5': 0
				,	'cnt_item_rating_1': 0
				,	'cnt_item_rating_2': 0
				,	'cnt_item_rating_3': 0
				,	'cnt_item_rating_4': 0
				,	'cnt_item_rating_5': 0
				};

				this.min_text_count = this.review_setting['min_text_count'];
				this.max_text_count = this.review_setting['max_text_count'];
				this.require_title = this.review_setting['require_title'];
				this.is_post_image = this.review_setting['is_post_image'];

				this.reviews_dom_hide = [];
				this.page = 1;

				// 平均値の算出
				for (var i = 0; i < this.reviews.length; i++) {
					let review = this.reviews[i];
					this.tmp['ratings_of_review'] += review['ratings_of_review'];
					this.tmp['cnt_star_' + review['ratings_of_review']] += 1;
					this.tmp['avg_item_rating_1'] += review['item_rating1'] ? review['item_rating1'] : 0;
					this.tmp['cnt_item_rating_1'] += review['item_rating1'] ? 1 : 0;
					this.tmp['avg_item_rating_2'] += review['item_rating2'] ? review['item_rating2'] : 0;
					this.tmp['cnt_item_rating_2'] += review['item_rating2'] ? 1 : 0;
					this.tmp['avg_item_rating_3'] += review['item_rating3'] ? review['item_rating3'] : 0;
					this.tmp['cnt_item_rating_3'] += review['item_rating3'] ? 1 : 0;
					this.tmp['avg_item_rating_4'] += review['item_rating4'] ? review['item_rating4'] : 0;
					this.tmp['cnt_item_rating_4'] += review['item_rating4'] ? 1 : 0;
					this.tmp['avg_item_rating_5'] += review['item_rating5'] ? review['item_rating5'] : 0;
					this.tmp['cnt_item_rating_5'] += review['item_rating5'] ? 1 : 0;
				}
				if (this.reviews.length > 0) {
					this.tmp['ratings_of_review'] /= this.reviews.length;
					this.tmp['avg_item_rating_1'] /= this.tmp['cnt_item_rating_1'] ? this.tmp['cnt_item_rating_1'] : 1;
					this.tmp['avg_item_rating_2'] /= this.tmp['cnt_item_rating_2'] ? this.tmp['cnt_item_rating_2'] : 1;
					this.tmp['avg_item_rating_3'] /= this.tmp['cnt_item_rating_3'] ? this.tmp['cnt_item_rating_3'] : 1;
					this.tmp['avg_item_rating_4'] /= this.tmp['cnt_item_rating_4'] ? this.tmp['cnt_item_rating_4'] : 1;
					this.tmp['avg_item_rating_5'] /= this.tmp['cnt_item_rating_5'] ? this.tmp['cnt_item_rating_5'] : 1;
				}

				if (!this.review_setting['display_star_without_reviews_widget'] && this.reviews.length == 0) {
					$('#UgcCreativeReviewArea').empty();
					return;
				}

				// スタイル設定
				set_style(this);

				// 評価表示
				set_dom_stararea(this, res);

				// 項目別評価
				set_dom_item_rate(this);

				// タグ
				set_dom_review_tag(this);

				// レビューを書く
				set_dom_comment(this);

				// 詳細フィルター
				set_dom_filter(this);

				// レビュー表示
				set_dom_review(this);

				// その他
				set_dom_other(this);

				$('.UgcCreativeReview-show').show();

				// スワイパー設定
				if (this.review_widget['display_type'] != 'list') {
					this._set_swiper();
					this.swiper_mode = true;
				} else {
					this.swiper_mode = false;
				}

				this.after_event();
				this._send_engage('visit');

			}

		}

		// レビュー内容の取得
		let body = new URLSearchParams();
		if (!this.get_all_review && this.product_display_id) {
			body.append('product_display_id', this.product_display_id)
		}
		if (this.review_id) {
			body.append('review_id', this.review_id)
		}
		
		body.append('review_widget_id', this.review_widget_id)
		body.append('get_all_review', this.get_all_review ? 1 : 0)
		body.append('ignore_published', this.ignore_published ? 1 : 0)
		body.append('user_qid', this.user_qid)
		body.append('user_id', this.user_id)

		req.open('POST', `${this.api_url}/api/review_data`, true)
		req.send(body)


		///--- 表示設定 ---///
		// スタイル設定
		function set_style(_this) {

			let review_widget = _this.review_widget;

			if (review_widget['is_custom_css']) {
				$('#UgcCreativeReview-custom_css').append(review_widget['custom_css']);
			}

			let main_color = review_widget['main_color'];
			let background_color = review_widget['background_color'];
			let text_color = review_widget['background_color'] == 'black' ? 'white' : 'black';
			
			$('#UgcCreativeReview-css-common').text(
				$('#UgcCreativeReview-css-common').text()
					.replaceAll('%text_color%', text_color)
					.replaceAll('%main_color%', main_color)
					.replaceAll('%background_color%', background_color)
			);

			if (review_widget['display_frame_line']) {
				$('.UgcCreativeReview-swiper-slide').css('border', '1px solid ' + main_color);
			}

			if (review_widget['display_type'] == 'slider' || review_widget['display_type'] == 'amazon') {
				$('#UgcCreativeReview-css-link').remove();

				$('#UgcCreativeReview-css-slider').text(
					$('#UgcCreativeReview-css-slider').text()
						.replaceAll('%main_color%', main_color)
				);

			} else {
				$('#UgcCreativeReview-css-slider').remove();
				$('#UgcCreativeReview-css-slider_link').remove();

				let swiper_width_sd = `calc(calc(100% / ${review_widget['sd_width']}) - calc(${10 * review_widget['sd_width']}px / ${review_widget['sd_width']}))`;
				let swiper_width_pc = `calc(calc(100% / ${review_widget['pc_width']}) - calc(${10 * review_widget['pc_width']}px / ${review_widget['pc_width']}))`;
				$('#UgcCreativeReview-css-link').text(
					$('#UgcCreativeReview-css-link').text()
						.replaceAll('%swiper_width_sd%', swiper_width_sd)
						.replaceAll('%swiper_width_pc%', swiper_width_pc)
				);
			}
		}


		// 評価
		function set_dom_stararea (_this, res) {

			let review_widget = _this.review_widget;
			let reviews = _this.reviews;
			let tmp = _this.tmp;

			// ヘッダー
			if (is_empty(review_widget['header_image'])) {
				$('#UgcCreativeReview-stararea-header_image').remove();
			} else {
				$('#UgcCreativeReview-stararea-header_image').find('img').attr('src', review_widget['header_image']);
			}

			if (!review_widget['display_title']) {
				$('#UgcCreativeReview-customerstr').remove();
			} else {
				$('#UgcCreativeReview-customerstr').text(review_widget['title']);
			}

			if (!review_widget['display_star_rate']) {
				$('#UgcCreativeReview-starArea').remove();
			}
			if (reviews.length == 0) {
				$('#UgcCreativeReview-stararea-span').remove();
			}
			if (!review_widget['display_tooltip']) {
				$('#UgcCreativeReview-stararea-tooltip').remove();
			}

			$('.UgcCreativeReview-stararea-star_average').text(`星５つ中の${Math.floor(tmp['ratings_of_review'] * 10) / 10}`);
			// レビュー件数表示設定に基づいて表示する件数を切り替える
			if (_this.is_review_count && _this.total_review_count > 0) {
				$('.UgcCreativeReview-stararea-star_total').text(`${_this.total_review_count}件`);
			} else {
				$('.UgcCreativeReview-stararea-star_total').text(`${reviews.length}件`);
			}

			let ratio_star = {1 : 0, 2 : 0, 3 : 0, 4 : 0, 5 : 0}
			for (var i =  0; i < reviews.length; i++) {
				ratio_star[reviews[i]['ratings_of_review']]++;
			}

			let gauge_width_tooltip = $('#UgcCreativeReview-stararea-tooltip').find('.UgcCreativeReview-stararea-gauge_width');
			let gauge_percent_tooltip = $('#UgcCreativeReview-stararea-tooltip').find('.UgcCreativeReview-stararea-gauge_percent');
			let gauge_width = $('#UgcCreativeReview-stararea-ratio').find('.UgcCreativeReview-stararea-gauge_width');
			let gauge_percent = $('#UgcCreativeReview-stararea-ratio').find('.UgcCreativeReview-stararea-gauge_percent');
			for (var i = 0; i < 5; i ++) {
				let average_star = tmp['cnt_star_' + (5 - i)] / reviews.length * 100;
				$(gauge_width).eq(i).css('width', average_star + '%');
				$(gauge_percent).eq(i).text(Math.floor(average_star) + '%');
				$(gauge_width_tooltip).eq(i).css('width', average_star + '%');
				$(gauge_percent_tooltip).eq(i).text(Math.floor(average_star) + '%');
			}

			for (var i = 0; i < 5; i ++) {
				let child_star = $('#UgcCreativeReview-reviews-star').find('span')[i];
				if (tmp['ratings_of_review'] >= i + 1) {
					$(child_star).addClass('UgcCreativeReview-star');
				} else if (tmp['ratings_of_review'] >= i + 0.5) {
					$(child_star).addClass('UgcCreativeReview-starHalf');
				} else {
					$(child_star).addClass('UgcCreativeReview-starWhite');
				}
			}

			if (!review_widget['display_write_comment']) {
				$('.UgcCreativeReview-reviews-display_write_comment').remove();
			} else {
				// レビュー件数表示設定に基づいて表示する件数を切り替える
				if (_this.is_review_count && _this.total_review_count > 0) {
					$('#UgcCreativeReview-reviews-count').text(_this.total_review_count);
				} else {
					$('#UgcCreativeReview-reviews-count').text(reviews.length);
				}
			}

			if (!review_widget['display_customer_review_graph']){
				$('#UgcCreativeReview-stararea-ratio').remove();
			}

			// コメント表示
			if (review_widget['is_display_comment']) {
				let commentText = '';
				if (review_widget['bottom_right_text_option']) {
					switch (parseInt(review_widget['bottom_right_text_option'])) {
						case 1:
							commentText = '※薬事法を鑑みてレビュー内の一部表現については修正を行っております。';
							break;
						case 2:
							commentText = '※お客様の感想であり、商品の効果・効能を表すものではございません。';
							break;
						case 3:
							commentText = '※個人の感想です。';
							break;
						case 4:
							commentText = review_widget['bottom_right_text'] || '';
							break;
					}
				}
				if (commentText) {
					const $commentArea = $('#UgcCreativeReview-bottom-comment');
					// case 4の場合は改行を維持するために特別処理
					if (review_widget['bottom_right_text_option'] && parseInt(review_widget['bottom_right_text_option']) === 4) {
						$commentArea.html(commentText.replace(/\n/g, '<br>'));
					} else {
						$commentArea.text(commentText);
					}
					$commentArea.css('text-align', review_widget['bottom_text_align'] || 'left');
					$commentArea.show();
				}
			}
		}


		// 項目別評価
		function set_dom_item_rate(_this) {

			let mail_setting = _this.mail_setting;
			let review_widget = _this.review_widget;
			let tmp = _this.tmp;

			if (is_empty(mail_setting['item_rating1_name']) || !review_widget['display_item_rating']) {
				$('#UgcCreativeReview-itemrate-title').remove();
				$('#UgcCreativeReview-itemrate-table').remove();
				return;
			}

			for (var i = 1; i <= 5; i++) {
				let index = `item_rating${i}_name`;

				if (is_empty(mail_setting[`${index}`])) {
					$('#UgcCreativeReview-itemrate-tr' + i).remove();
					continue;
				}

				$('#UgcCreativeReview-itemrate-title' + i).text(mail_setting[`${index}`]);
				let avg = Math.floor(tmp[`avg_item_rating_${i}`] * 10) / 10;

				for (var j = 0; j < 5; j++) {
					let child_star = $('#UgcCreativeReview-itemrate-star' + i).find('span')[j];

					if (avg >= j + 1) {
						$(child_star).addClass('UgcCreativeReview-star');
					} else if (avg  >= j + 0.5) {
						$(child_star).addClass('UgcCreativeReview-starHalf');
					} else {
						$(child_star).addClass('UgcCreativeReview-starWhite');
					}
				}
				$('#UgcCreativeReview-itemrate-star' + i).find('span').eq(5).text(avg);
			}
		}


		// タグ
		function set_dom_review_tag(_this) {
			let review_widget_tags = _this.review_widget_tags;
			let review_widget = _this.review_widget;
			
			if (!review_widget['display_tag']) {
				$('UgcCreativeReview-tag').remove();
				return;
			}
			
			if (review_widget_tags != null && review_widget_tags.length > 0) {
				for (var i = 0; i < review_widget_tags.length; i++) {
					let tag = ('<span class="UgcCreativeReview-Tag" onClick="window.UgcCreativeReview.filterTag(this)">' + review_widget_tags[i]['keyword']  + '</span>');
					$(".UgcCreativeReview-TagArea").append(tag);
				}
				$(".UgcCreativeReview-TagArea").show();
			}
		}


		// レビューを書く
		function set_dom_comment(_this) {

			let review_setting = _this.review_setting;
			let review_widget = _this.review_widget;
			let mail_setting = _this.mail_setting;
			let mail_setting_profiles = _this.mail_setting_profiles;
			let setting = _this.setting;

			if (!review_setting['is_post_with_widget'] || !review_widget['display_write_comment']) {
				$('#UgcCreativeReview-comment-write').remove();
				return;
			}

			// ヘッダ表示
			$('#UgcCreativeReview-comment-post_page').text(review_setting['post_page']);

			// 項目別評価
			let item_rating_name = '';
			let required = '<span class="UgcCreativeReview-require">*</span>';
			if (!review_widget['display_item_rating']) {
				$('#UgcCreativeReview-comment-item_rating').remove();
			} else {
				for (var i = 1; i <= 5; i++) {
					let name = mail_setting[`item_rating${i}_name`];
					if (is_empty(name)) {
						$('.UgcCreativeReview-comment-item_rating' + i).remove();
					} else {
						$('#UgcCreativeReview-comment-item_rating' + i + '_title')
							.text(name)
							.append(required);
						$('#UgcCreativeReview-star1item_rating' + i).attr('data-item', name);

						item_rating_name = item_rating_name + name + '|';
					}
				}
			}
			if (is_empty(item_rating_name))  {
				$('#UgcCreativeReview-comment-item_rating_name').remove();
			} else {
				item_rating_name = item_rating_name.slice(0, -1);
				$('#UgcCreativeReview-comment-item_rating_name').val(item_rating_name);
			}

			// タイトル
			if (!review_setting['require_title']) {
				$('#UgcCreativeReview-comment-require_title').remove();
			}

			// レビュー
			$('#UgcCreativeReview-comment-maxlength').text(review_setting['max_text_count']);

			// 画像
			if (!review_setting['is_post_image']) {
				$('#UgcCreativeReview-comment-image').remove();
			} else {
				$('#UgcCreativeReview-comment-post_image').text(review_setting['image_page']);
			}

			// ニックネーム
			if (!review_setting['is_nickname_item']) {
				$('#UgcCreativeReview-comment-nickname').remove();
			} else {
				$('#UgcCreativeReview-comment-nickname_page').text(review_setting['nickname_page']);
			}

			// プロファイル項目
			if (!review_widget['display_profile_select']) {
				$('#UgcCreativeReview-comment-profile').remove();
			} else {
				for (var i = 1; i <= 5; i++) {
					let index = 'profile_name' + i;
					if (is_empty(mail_setting[`${index}`])) {
						$('#UgcCreativeReview-comment-profile_div' + i).remove();
						$('#UgcCreativeReview-comment-profile_select' + i).remove();
						continue;
					}

					let showFlg = false;

					for (var j = 0; j < mail_setting_profiles.length; j++) {
						if (mail_setting_profiles[j]['profile_id'] == i) {
							$('#UgcCreativeReview-comment-profile_select' + i).append('<option value="' + mail_setting_profiles[j]['name'] + '">' + mail_setting_profiles[j]['name'] + '</option>');
							showFlg = true;
						}
					}

					if (showFlg) {
						$('#UgcCreativeReview-comment-profile_div' + i).text(mail_setting[`${index}`]).append(required);
						$('#UgcCreativeReview-comment-profile_select' + i).attr('name', 'profile' + i).attr('data-item', mail_setting[`${index}`]);
					} else {
						$('#UgcCreativeReview-comment-profile_div' + i).remove();
						$('#UgcCreativeReview-comment-profile_select' + i).remove();
					}

				}
			}

			// 年齢
			for (let key in setting['age']) {
				$('#UgcCreativeReview-comment-age_select').append('<option value="' + key + '">' + setting['age'][key] + '</option>')
			}

			// 性別
			for (let key in setting['gender']) {
				$('#UgcCreativeReview-comment-gender_select').append('<option value="' + key + '">' + setting['gender'][key] + '</option>')
			}


		}


		// 詳細フィルター
		function set_dom_filter(_this) {
			let tmp = _this.tmp;
			let review_widget = _this.review_widget;
			
			if (!review_widget['display_search'] || !_this.review_setting['display_star_without_reviews_widget'] && _this.reviews.length == 0) {
				$('#UgcCreativeReview-filter').remove();
				return;
			}

			// 星による評価
			for (var i = 0; i < 5; i++) {
				let star = 5 - i;
				let select = $('<option>').val(star).text(star + ' (' + tmp['cnt_star_' + star] + '件)');
				$('#UgcCreativeReview-FilterStarSelect').append(select);
			}

			// ソートキー
			$('#UgcCreativeReview-SortAreaDetail').children()
				.css('color', review_widget['main_color'])
				.css('border-color', review_widget['main_color']);
		}


		// レビュー表示
		function set_dom_review(_this) {
			if (!_this.review_setting['display_star_without_reviews_widget'] && _this.reviews.length == 0) {
				$('#UgcCreativeReview-comment').empty();
				$('#UgcCreativeReview-reviews-nothing').text(review_setting['text_without_reviews_widget']);
				return;
			}

			let reviews = _this.reviews;
			let review_setting = _this.review_setting;
			let review_widget = _this.review_widget;
			let mail_setting = _this.mail_setting;
			let mail_setting_profiles = _this.mail_setting_profiles;

			// 表示形式
			if (!review_widget) {
				$('#UgcCreativeReviewArea-commentSwiper1').show();
				$('#UgcCreativeReviewArea-commentSwiper2').remove();
				_this.list_id = '#UgcCreativeReviewArea-commentSwiper1';
			} else if (_this._is_pc && review_widget.pc_width == 1 || !_this._is_pc && review_widget['sd_width'] == 1) {
				$('#UgcCreativeReviewArea-commentSwiper1').remove();
				$('#UgcCreativeReviewArea-commentSwiper2').show();
				_this.list_id = '#UgcCreativeReviewArea-commentSwiper2';
			} else {
				$('#UgcCreativeReviewArea-commentSwiper1').show();
				$('#UgcCreativeReviewArea-commentSwiper2').remove();
				_this.list_id = '#UgcCreativeReviewArea-commentSwiper1';
			}

			/// 共通設定
			let dom_base = $('#UgcCreativeReview-review-base');

			// 色
			$(dom_base).find('.UgcCreativeReview-reviews-main_color_fill').attr('fill', review_widget['main_color']);
			$(dom_base).find('.UgcCreativeReview-reviews-main_color').css('color', review_widget['main_color']);
			if (!review_widget['display_profile_select']) {
				$(dom_base).find('.UgcCreativeReview-reviews-display_profile_select').remove();
			}

			// プロファイル項目
			if (is_empty(mail_setting['profile_name1'])) {
				$(dom_base).find('.UgcCreativeReview-review-profile1').remove();
			}
			if (is_empty(mail_setting['profile_name2'])) {
				$(dom_base).find('.UgcCreativeReview-review-profile2').remove();
			}
			if (is_empty(mail_setting['profile_name3'])) {
				$(dom_base).find('.UgcCreativeReview-review-profile3').remove();
			}
			if (is_empty(mail_setting['profile_name4'])) {
				$(dom_base).find('.UgcCreativeReview-review-profile4').remove();
			}
			if (is_empty(mail_setting['profile_name5'])) {
				$(dom_base).find('.UgcCreativeReview-review-profile5').remove();
			}

			// 星
			if (!review_widget['display_star_rate']) {
				$(dom_base).find('.UgcCreativeReview-reviews-display_star_rate').remove();
			}

			// 項目別評価
			if (is_empty(mail_setting['item_rating1_name']) || !review_widget['display_item_rating']) {
				$(dom_base).find('.UgcCreativeReview-reviews-display_item_rating').remove();
			}
			if (is_empty(mail_setting['item_rating1_name'])) {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_1').remove();
			} else {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_1_name').text(mail_setting['item_rating1_name']);
			}
			if (is_empty(mail_setting['item_rating2_name'])) {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_2').remove();
			} else {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_2_name').text(mail_setting['item_rating2_name']);
			}
			if (is_empty(mail_setting['item_rating3_name'])) {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_3').remove();
			} else {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_3_name').text(mail_setting['item_rating3_name']);
			}
			if (is_empty(mail_setting['item_rating4_name'])) {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_4').remove();
			} else {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_4_name').text(mail_setting['item_rating4_name']);
			}
			if (is_empty(mail_setting['item_rating5_name'])) {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_5').remove();
			} else {
				$(dom_base).find('.UgcCreativeReview-reviews-rating_item_5_name').text(mail_setting['item_rating5_name']);
			}

			// レビュー本文
			if (navigator.userAgent.indexOf('/safari/is') != -1) {
				if (review_widget['background_color'] == 'white') {
					$(dom_base).find('.UgcCreativeReview-reviews-description_background').addClass('UgcCreativeReview-ugcWhiteGradWithoutGradient');
				} else {
					$(dom_base).find('.UgcCreativeReview-reviews-description_background').addClass('UgcCreativeReview-ugcBlackGradWithoutGradient');
				}
			} else {
				if (review_widget['background_color'] == 'white') {
					$(dom_base).find('.UgcCreativeReview-reviews-description_background').addClass('UgcCreativeReview-ugcWhiteGrad');
				} else {
					$(dom_base).find('.UgcCreativeReview-reviews-description_background').addClass('UgcCreativeReview-ugcBlackGrad');
				}
			}

			// 参考になった
			if (!review_widget['display_like_counts'] && !review_widget['can_bad']) {
				$(dom_base).find('.UgcCreativeReview-reviews-display_like_counts').remove();
			} else if (!review_widget['display_like_counts']) {
				$(dom_base).find('.UgcCreativeReview-review-likeBtn').remove();
			} else if (!review_widget['can_bad']) {
				$(dom_base).find('.UgcCreativeReview-review-badBtn').remove();
			}
			// 参考にならなかった
			if (review_widget['display_type'] == 'list' && review_widget['display_more_button']) {
				if (review_widget['more_button_text'] == 'default') {
					$('#UgcCreativeReview-moreReviewBtn').text('さらに' + _this._reviews_per_page() + '件を表示');
				} else if (review_widget['more_button_text'] == 'jpn') {
					$('#UgcCreativeReview-moreReviewBtn').addClass('UgcCreativeReview-radius').text('もっと見る');
				} else if (review_widget['more_button_text'] == 'eng') {
					$('#UgcCreativeReview-moreReviewBtn').addClass('UgcCreativeReview-radius').text('view more');
				} else {
					$('.UgcCreativeReview-reviews-display_more').remove();
				}
			} else {
				$('.UgcCreativeReview-reviews-display_more').remove();
			}

			// ページ送り
			if (review_widget['display_type'] != 'slider' || !review_widget['display_next_icon']) {
				$('.UgcCreativeReview-reviews-swiper_btn').remove();
			} else {
			}
			if (review_widget['display_type'] != 'amazon') {
				$('.UgcCreativeReview-reviews-amazon_btn').remove();
			}


			// レビュー無し
			if (reviews.length == 0) {
				$('#UgcCreativeReview-starArea').remove();
				$('#UgcCreativeReview-reviews-nothing').text(review_setting['text_without_reviews_widget']);
			} else {
				let width_count = _this._is_pc() ? review_widget['pc_width'] : review_widget['sd_width'];
				if (reviews.length > width_count) {
					$('.UgcCreativeReview-reviews-swiper_btn').show();
					$('.UgcCreativeReview-reviews-amazon_btn').show();
				}
				$('#UgcCreativeReview-reviews-nothing').remove();
			}

			let limit = _this._display_item_count();

			// レビューごとの設定
			for (var i =  0; i < reviews.length; i++) {

				let review = reviews[i];

				review['has_pushed_like'] = localStorage.getItem(`like_count_${review['id']}`) == 1;
				review['has_pushed_bad'] = localStorage.getItem(`bad_count_${review['id']}`) == 1;
				review['has_image'] = review['image_path'] ? 1 : 0
				review['is_confirm'] = review['channel'] == 'mail' || review['channel'] == 'import' ? 1 : 0;
				review['has_reply'] = review['description_of_reply'] && review['published_reply'] == 1 ? 1 : 0;
				review['description_of_review'] = review['description_of_review'].replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;').replace(/`/g, '&#x60;').replace(/\r?\n/g, '<br>');

				let clone_review = $('#UgcCreativeReview-review-base').clone();
				$(clone_review).attr('id', '');
				$('.UgcCreativeReview-review_parent').append(clone_review);
				let dom = $('.UgcCreativeReview-review_parent').children()[i + 1];

				if (i > limit - 1) {
					$(dom).hide();
				}

				// ID
				$(dom).find('.UgcCreativeReview-review-id').text(review['id']);

				// 名前
				$(dom).find('.UgcCreativeReview-review-name').text(review['display_review_name']);

				// 確認済
				if (!review['is_confirm']) {
					$(dom).find('.UgcCreativeReview-review-is_confirm').remove();
				}

				// 性別・年齢
				if (review['display_gender'] && review['display_age']) {
					$(dom).find('.UgcCreativeReview-review-gender_age').text('【' + review['display_gender'] + ' / ' + review['display_age'] + '】');
				} else if (review['display_gender']) {
					$(dom).find('.UgcCreativeReview-review-gender_age').text('【' + review['display_gender'] + '】');
				} else if (review['display_age']) {
					$(dom).find('.UgcCreativeReview-review-gender_age').text('【' + review['display_age'] + '】');
				} else {
					$(dom).find('.UgcCreativeReview-review-gender_age').remove();
				}

				// プロファイル項目
				if (review['profile1']) {
					$(dom).find('.UgcCreativeReview-review-profile1').text(mail_setting['profile_name1'] + '：' + review['profile1']);
				} else {
					$(dom).find('.UgcCreativeReview-review-profile1').remove();
				}
				if (review['profile2']) {
					$(dom).find('.UgcCreativeReview-review-profile2').text(mail_setting['profile_name2'] + '：' + review['profile2']);
				} else {
					$(dom).find('.UgcCreativeReview-review-profile2').remove();
				}
				if (review['profile3']) {
					$(dom).find('.UgcCreativeReview-review-profile3').text(mail_setting['profile_name3'] + '：' + review['profile3']);
				} else {
					$(dom).find('.UgcCreativeReview-review-profile3').remove();
				}
				if (review['profile4']) {
					$(dom).find('.UgcCreativeReview-review-profile4').text(mail_setting['profile_name4'] + '：' + review['profile4']);
				} else {
					$(dom).find('.UgcCreativeReview-review-profile4').remove();
				}
				if (review['profile5']) {
					$(dom).find('.UgcCreativeReview-review-profile5').text(mail_setting['profile_name5'] + '：' + review['profile5']);
				} else {
					$(dom).find('.UgcCreativeReview-review-profile5').remove();
				}

				// 評価
				for (var j = 0; j < 5; j++) {
					let dom_star_rate = $(dom).find('.UgcCreativeReview-reviews-display_star_rate').children()[j];
					if (j + 1 <= review['ratings_of_review']) {
						$(dom_star_rate).addClass('UgcCreativeReview-star');
					} else {
						$(dom_star_rate).addClass('UgcCreativeReview-starWhite');
					}
				}

				// 項目別評価
				for (var j = 0; j < 5; j++) {
					if (mail_setting[`item_rating${j}_name`] == '') {
						$(dom).find('.UgcCreativeReview-review-rating_item_' + j).remove();
					} else {
						for (var k = 0; k < 5; k++) {
							let dom_star_rate = $(dom).find('.UgcCreativeReview-review-rating_item_' + (k + 1) + '_star').children()[j];
							if (j + 1 <= review['item_rating' + (k + 1)]) {
								$(dom_star_rate).addClass('UgcCreativeReview-star');
							} else {
								$(dom_star_rate).addClass('UgcCreativeReview-starWhite');
							}
						}
					}
				}

				// タイトル
				$(dom).find('.UgcCreativeReview-review-title').text(review['title_of_review']);
				// レビュー
				$(dom).find('.UgcCreativeReview-review-trigger_id').attr('id', 'UgcCreativeReview-trigger' + review['id']);
				$(dom).find('.UgcCreativeReview-review-review').html(review['description_of_review']);
				$(dom).find('.UgcCreativeReview-review-trigger_for').attr('for', 'UgcCreativeReview-trigger' + review['id']);

				// 画像
				if (!review['image_path']) {
					$(dom).find('.UgcCreativeReview-review-image_div').remove();
				} else {
					$(dom).find('.UgcCreativeReview-review-popup').attr('for', 'UgcCreativeReview-popUp' + review['id']);
					$(dom).find('.UgcCreativeReview-review-image').attr('src', review['image_path']);
				}

				// 参考になった
				$(dom).find('.UgcCreativeReview-review-likeBtn').attr('onClick', "UgcCreativeReview.push_like_button(this, " + review['id'] + ", 'like')");
				$(dom).find('.UgcCreativeReview-review-like_count').text(review['like_counts']);
				if (review['has_pushed_like']) {
					$(dom).find('.UgcCreativeReview-review-likeI').addClass('UgcCreativeReview-mainColor');
				}
				// 参考にならなかった
				$(dom).find('.UgcCreativeReview-review-badBtn').attr('onClick', "UgcCreativeReview.push_like_button(this, " + review['id'] + ", 'bad')");
				$(dom).find('.UgcCreativeReview-review-bad_count').text(review['bad_counts']);
				if (review['has_pushed_bad']) {
					$(dom).find('.UgcCreativeReview-review-badI').addClass('UgcCreativeReview-mainColor');
				}

				// 返信
				if (!review['has_reply']) {
					$(dom).find('.UgcCreativeReview-review-has_reply').remove();
				} else {
					$(dom).find('.UgcCreativeReview-review-reply_company').text(review['company_name']);
					$(dom).find('.UgcCreativeReview-reply_balloon').append(review['description_of_reply']);
				}

				// 日時
				$(dom).find('.UgcCreativeReview-review-date').text(review['display_review_date']);

				// 画像ポップアップ
				let clone_popup = $('#UgcCreativeReview-popup_parent').children().eq(0).clone();
				$('#UgcCreativeReview-popup_parent').append(clone_popup);
				let dom_popup = $('#UgcCreativeReview-popup_parent').children()[i + 1];
				$(dom_popup).find('.UgcCreativeReview-popup-checkbox').attr('id', 'UgcCreativeReview-popUp' + review['id']);
				$(dom_popup).find('.UgcCreativeReview-popup-close').attr('for', 'UgcCreativeReview-popUp' + review['id']);
				$(dom_popup).find('.UgcCreativeReview-popup-image').attr('src', review['image_path']);

			}

			// コピー元要素を削除
			$('#UgcCreativeReview-review-base').remove();

		}

		// その他
		function set_dom_other(_this) {

			let setting = _this.setting;
			let product = _this.product;

			// ロゴ
			$('#UgcCreativeReview-app_url').attr('href', setting.app_url);
			$('#UgcCreativeReview-logo_img').attr('src', setting.logo_url);

			// リッチスニペット
			if (_this.review_widget['is_rich_snippet']) {
				// レビュー件数表示設定に基づいて表示する件数を切り替える
				let displayReviewCount = (_this.is_review_count && _this.total_review_count > 0) ? _this.total_review_count : _this.reviews.length;

				$('.UgcCreativeReview-reviews-is_rich_snippet').text(
					$('.UgcCreativeReview-reviews-is_rich_snippet').text()
						.replaceAll('%name%', typeof product['name'] == 'undefined' ? '' : product['name'])
						.replaceAll('%image%', typeof product['image_url'] == 'undefined' ? '' : product['image_url'])
						.replaceAll('%ratingValue%', Math.floor(_this.tmp['ratings_of_review'] * 10) / 10)
						.replaceAll('%ratingCount%', displayReviewCount)
				);
			} else {
				$('.UgcCreativeReview-reviews-is_rich_snippet').remove();
			}
		}


		// Empty判定
		function is_empty(val) {
			if (val == null) {
				return true;
			}

			switch(typeof val) {
				case 'string':
					return (val === '');
				case 'number':
					return (val == 0);
				case 'boolean':
					return !val;
				case 'undefined':
				case 'null':
					return true;
				default:
					return false;
			}
		}



		///--- 関数群の定義 ---//
		window.UgcCreativeReview = {}

		// レビュー閲覧
		window.onscroll = () => {
			if (document.getElementById('UgcCreativeReviewArea').getBoundingClientRect().top < 0) {
				// レビューの最上部がウィンドウより上に行った状態が３秒以上続いたら送る
				if (!this.timer && !this.send_look_review) {
					this.timer = setTimeout(() => {
						if (document.getElementById('UgcCreativeReviewArea').getBoundingClientRect().top < 0) {
							this._send_engage('look_review')
							clearTimeout(this.timer)
							this.send_look_review = true
						}
					}, 3000);
				}
			}
		}


		// レビュー入力文字数チェック
		window.UgcCreativeReview.reviewLengthCount = () => {
			let review = document.getElementById('UgcCreativeReview-review').value;
			document.getElementById('UgcCreativeReview-reviewLengthCount').innerText = review.length;
			let check_dom = document.getElementById('UgcCreativeReview-reviewLengthCountCheck');

			if (review.length >= this.min_text_count && review.length <= this.max_text_count) {
				check_dom.style.display = 'inline-block'
			} else {
				check_dom.style.display = 'none'
			}
		}

		// 画像追加
		window.UgcCreativeReview.chooseImage = (f) => {
			this.image_file = f.files[0];
			$('#UgcCreativeReview-image_box').css('background-image', 'url(' + URL.createObjectURL(f.files[0]) + ')');
			$('#UgcCreativeReview-image_box').addClass('UgcCreativeReview-image_stop_events');
			$('#UgcCreativeReview-image_add').hide();
			$('#UgcCreativeReview-image_delete').show();
		}

		// 画像削除
		window.UgcCreativeReview.deleteImage = (e) => {
			this.image_file = '';
			$('#UgcCreativeReview-image_box').css('background-image', 'url()');
			$('#UgcCreativeReview-image_box').removeClass('UgcCreativeReview-image_stop_events');
			$('#UgcCreativeReview-image_add').show();
			$('#UgcCreativeReview-image_delete').hide();
			$('#UgcCreativeReview-image_file').val('');
		}

		// レビュー投稿
		window.UgcCreativeReview.submitForm = (is_submit = false) => {

			// フォーム内容をAPIに送信する
			let star = Array.from(document.getElementsByName('star')).map(v => v.checked ? v.value : false).find(v => v);
			star = star ? star : ''
			let title = document.getElementById('UgcCreativeReview-title').value;
			let review = document.getElementById('UgcCreativeReview-review').value;
			let name = document.getElementById('UgcCreativeReview-name').value;
			let nick_name = document.getElementById('UgcCreativeReview-nickname') ? document.getElementById('UgcCreativeReview-nickname').value : '';
			let email = document.getElementById('UgcCreativeReview-email').value;

			let minTextCount, maxTextCount;
			if (typeof window.reviewSettingFromServer !== 'undefined') {
				minTextCount = window.reviewSettingFromServer.min_text_count;
				maxTextCount = window.reviewSettingFromServer.max_text_count;
			} else {
				minTextCount = this.min_text_count;
				maxTextCount = this.max_text_count;
			}

			// --- バリデーション ---
			let errs = [];
			if (review.length == 0) { errs.push('レビューを入力してください') };
			if (star.length == 0) { errs.push('評価を入力してください') };
			if (this.require_title == 1 && title.length == 0) { errs.push('タイトルを入力してください') };
			if (name.length == 0) { errs.push('名前を入力してください') };
			let regexp = /^[A-Za-z0-9]{1}[A-Za-z0-9_.-]*@{1}[A-Za-z0-9_.-]{1,}\.[A-Za-z0-9]{1,}$/;
			if (email.length == 0) { errs.push('Eメールアドレスを入力してください') };
			if (email.length > 0 && !regexp.test(email)) { errs.push('Eメールアドレスを正しい形式で入力してください') };
			if (review.length < minTextCount) { errs.push(`レビューを${minTextCount}文字以上入力してください`); }
			if (review.length > maxTextCount) { errs.push(`レビューを入力できるのは${maxTextCount}文字までです`); }

			let keys = ['age', 'gender', 'profile1', 'profile2', 'profile3', 'profile4', 'profile5',
						'item_rating1', 'item_rating2', 'item_rating3', 'item_rating4', 'item_rating5', 'item_rating_name']
			let append_data = {}
			keys.forEach(k => {
				let v;
				let elem = document.querySelector(`[name = "${k}"]`);

				if (elem) {
					let item_name = elem.getAttribute('data-item')

					if (k == 'item_rating1' || k == 'item_rating2' || k == 'item_rating3' || k == 'item_rating4' || k == 'item_rating5') {
						v = Array.from(document.getElementsByName(k)).map(vv => vv.checked ? vv.value : false).find(vv => vv);
					} else {
						v = elem.value;
					}

					if (!v || v.length == 0) {
						if (elem.classList.contains('required')) {
							errs.push(item_name + 'を入力してください');
						}
					} else {
						append_data[k] = v;
					}
				}
			});

			if (errs.length > 0) {
				let err_dom = document.getElementById('UgcCreativeReview-errorMsg');
				err_dom.style.display = 'block';
				err_dom.innerHTML = errs.map(v => v).join('<br>');
				return;
			}

			if (is_submit) {
				document.getElementById('create_form').submit();
			}

			let req = new XMLHttpRequest();
			req.onreadystatechange = () => {
				if (req.readyState == 4) {
					if (req.status == 200) {
						let tmp = JSON.parse(req.responseText);
						if (tmp.status) {
							alert('メールを送信しました')
							$('input#UgcCreativeReview-popupReviewForm').prop('checked', false)
							return
						} else {
							alert(tmp.msg)
						}
					}
				}
			}
			let body = new FormData();
			body.append('rating', star);
			body.append('title', title);
			body.append('review', review);
			body.append('name', name);
			body.append('nick_name', nick_name);
			body.append('email', email);
			if (this.product_display_id) {
				body.append('product_display_id', this.product_display_id);
			}
			if (this.review_widget_id) {
				body.append('review_widget_id', this.review_widget_id);
			}
			body.append('user_qid', this.user_qid);
			body.append('user_id', this.user_id);

			Object.keys(append_data).forEach(
				key => body.append(key, append_data[key])
			);

			if (this.is_post_image == 1) {
				body.append('image_file', this.image_file);
			}

			req.open('POST', `${this.api_url}/api/create_mail_send`, true);
			req.send(body);
		}


		// 星の絞り込み
		window.UgcCreativeReview.search_star = (star) => {
			this.conditions.star = star;
			this._get_reviews();
		}

		// タグの絞り込み
		window.UgcCreativeReview.filterTag = (e) => {

			$(".UgcCreativeReview-TagArea").children().removeClass("UgcCreativeReview-TagSelect");

			if (this.conditions.tag != e.innerText) {
				this.conditions.tag = e.innerText;
				$(e).addClass('UgcCreativeReview-TagSelect');
			} else {
				this.conditions.tag = '';
			}

			this._get_reviews();
		}

		// 詳細フィルター
		window.UgcCreativeReview.pushDetailFilterBtn = () => {
			let tmp = document.getElementById('UgcCreativeReview-FilterAreaDetail');
			tmp.style.display = tmp.style.display == 'block' ? 'none' : 'block';
			tmp = document.getElementById('UgcCreativeReview-SortAreaDetail');
			tmp.style.display = tmp.style.display == 'block' ? 'none' : 'block';
		}

		// 星による評価
		window.UgcCreativeReview.changeStarSelect = (e) => {
			this.conditions.star = e.value;
			this._get_reviews();
		}

		// 全てのレビュー・写真付きのレビュー
		window.UgcCreativeReview.changeFilterOtherSelect = (e) => {
			this.conditions.other = e.value;
			this._get_reviews();
		}

		// ソート
		window.UgcCreativeReview.sort = (e) => {
			let sortKey = e.id.replace('UgcCreativeReview-sort_', '');
			let sort_asc = !this.conditions.sort_asc;

			if (this.conditions.sort_key != '' && this.conditions.sort_key != sortKey) {
				$('#UgcCreativeReview-sort_' + this.conditions.sort_key).children(0).text('');
				$('#UgcCreativeReview-sort_' + this.conditions.sort_key).removeClass('UgcCreativeReview-btnSelectColor');
				sort_asc = true;
			}

			this.conditions.sort_key = sortKey;
			this.conditions.sort_asc = sort_asc;

			$(e).addClass("UgcCreativeReview-btnSelectColor");

			if (this.conditions.sort_asc) {
				$(e).children(0).text('↑');
			} else {
				$(e).children(0).text('↓');
			}

			this._get_reviews();
		}


		// 項目別評価 続きを見る
		window.UgcCreativeReview.show_all_rating_item = (e) => {
			// td->tr->tbody->find('tr')
			$(e).parent().parent().parent().find('tr').show();
			$(e).hide();
		}

		// 画像ポップアップ
		window.UgcCreativeReview.engage = (type) => {
			this._send_engage(type)
		}

		// 評価ボタン
		window.UgcCreativeReview.push_like_button = (e, id, type) => {

			let reviews = this.reviews.filter(function(r) {return r['id'] == id})
			let review = reviews[0]
			let value = review[`has_pushed_${type}`] ? 'minus' : 'plus'
			let count = 0

			// localStorage
			if (value == 'minus') {
				localStorage.removeItem(`${type}_count_${id}`)
				count = -1
			} else {
				localStorage.setItem(`${type}_count_${id}`, 1)
				count = +1
			}

			// API送信
			navigator.sendBeacon(`${this.api_url}/api/reviews/update_count?id=${id}&type=${type}&value=${value}`)

			// 変数設定
			review[`has_pushed_${type}`] = !review[`has_pushed_${type}`];
			review[`${type}_counts`] = review[`${type}_counts`] + count;

			// dom描画
			if ($(e).closest('div[id^="swiper-wrapper"]').length > 0) {
				$(e).closest('div[id^="swiper-wrapper"]').children('div').each(function(i, elem) {
					if ($(elem).find('input[id^="UgcCreativeReview-trigger' + id + '"]').length > 0) {
						$(elem).find(`.UgcCreativeReview-review-${type}_count`).text(
							parseInt($(elem).find(`.UgcCreativeReview-review-${type}_count`).text()) + count
						);
						if (value == 'minus') {
							$(elem).find(`.UgcCreativeReview-review-${type}I`).removeClass('UgcCreativeReview-mainColor');
						} else {
							$(elem).find(`.UgcCreativeReview-review-${type}I`).addClass('UgcCreativeReview-mainColor');
						}
					}
				});
			} else {
				$(e).find(`.UgcCreativeReview-review-${type}_count`).text(
					parseInt($(e).find(`.UgcCreativeReview-review-${type}_count`).text()) + count
				);
				if (value == 'minus') {
					$(e).find(`.UgcCreativeReview-review-${type}I`).removeClass('UgcCreativeReview-mainColor');
				} else {
					$(e).find(`.UgcCreativeReview-review-${type}I`).addClass('UgcCreativeReview-mainColor');
				}
			}
		}


		// もっと見る
		window.UgcCreativeReview.push_more_button = () => {

			this.page += 1

			let display_item_count = this._display_item_count();

			for (var i = 0; i < display_item_count; i++) {
				$('.UgcCreativeReview-review_parent').children().eq(i).show();
			}

			if ($('.UgcCreativeReview-review_parent').children().filter(":hidden").length == 0) {
				$('.UgcCreativeReview-reviews-display_more').hide();
			}
		}

		window.UgcCreativeReview.click_slider_arrow = ()=> {
			let t = document.getElementById('UgcCreativeReviewArea-comment').getBoundingClientRect().top + window.pageYOffset;
			window.scrollTo({
				top: t - 50
			,	behavior: 'smooth'
			});
		}
	}


	// レビュー表示件数取得
	_display_item_count() {

		let limit = 99999999;

		if (!this.review_widget) {
			return limit;
		}

		if (this.review_widget.display_type != 'slider' && this.review_widget.display_type != 'amazon') {
			return this.page * this._reviews_per_page();
		} else {
			return limit;
		}
	}


	// 1ページあたりの表示件数取得
	_reviews_per_page() {

		if (!this.review_widget) {
			return 1;
		}

		if (this._is_pc()) {
			return this.review_widget.pc_width * this.review_widget.pc_height;
		} else {
			return this.review_widget.sd_width * this.review_widget.sd_height;
		}
	}


	// レビューフィルター
	_filter_reviews() {

		let data_tmp = Object.assign(this.reviews, [])
		let data = data_tmp.filter(d => {
			if (this.conditions.star != 'all') {
				if (Math.floor(d.ratings_of_review) != this.conditions.star) {
					return false
				}
		}

		if (this.conditions.other != 'all') {
			if (!d.image_path) {
				return false
			}
		}

		if (this.conditions.tag != '') {
			if (d.description_of_review.indexOf(this.conditions.tag) == -1) {
				return false
			}
		}
			return true
		})

		return data
	}


	// レビューソート
	_sort_reviews() {

		let data = Object.assign(this._filter_reviews(), []);

		if (this.conditions.sort_key == '') {
			return data;
		}

		data.sort((a, b) => {
			let target_key = ''
			if (this.conditions.sort_key.includes('date')) {
				target_key = 'review_date_time'
			} else if (this.conditions.sort_key.includes('rate')) {
				target_key = 'ratings_of_review'
			} else if (this.conditions.sort_key.includes('like')) {
				target_key = 'like_counts'
			} else if (this.conditions.sort_key.includes('confirm')) {
				target_key = 'is_confirm'
			} else if (this.conditions.sort_key.includes('reply')) {
				target_key = 'has_reply'
			} else if (this.conditions.sort_key.includes('image')) {
				target_key = 'has_image'
			}
			if (this.conditions.sort_asc) {
				if (a[target_key] < b[target_key]) return -1
				if (a[target_key] > b[target_key]) return 1
			} else {
				if (a[target_key] < b[target_key]) return 1
				if (a[target_key] > b[target_key]) return -1
			}

			if (a[target_key] == b[target_key]) return (a.id > b.id ? 1 : 0)
		})

		return data;
	}


	// レビュー取得
	_get_reviews() {

		// 全てのレビューのDOM（表示対象外のDOM + 表示中のDOM(Swiper.jsで生成される要素を除外)）
		let dom = this.reviews_dom_hide.concat(Array.from($('.UgcCreativeReview-review_parent > div').not('.swiper-slide-duplicate')));

		let data = this._sort_reviews();

		this.reviews_dom_hide = [];
		let id_arr = [];

		// Swiper.jsのスライダーを再生成するため、親要素から削除
		$(this.list_id).empty();
		// 子要素(1)を再生成
		$(this.list_id).append('<div class="UgcCreativeReview-review_parent UgcCreativeReview-swiper-wrapper swiper-wrapper">');

		// レビュー表示件数
		let limit = this._display_item_count();

		for (var i = 0; i < data.length; i++) {

			let dom_show = dom.filter(d => {
				return data[i]['id'] == $(d).find('.UgcCreativeReview-review-id').text();
			});

			// Swiper.jsで付与されるクラスを削除するため、クラスを再生成
			$(dom_show[0]).removeClass();
			$(dom_show[0]).addClass('UgcCreativeReview-commentBackground UgcCreativeReview-swiper-slide swiper-slide');

			if (this.review_widget['display_type'] == 'list') {
				if (i > limit - 1) {
					$(dom_show[0]).hide();
				} else {
					$(dom_show[0]).show();
				}
			}

			$('.UgcCreativeReview-review_parent').append(dom_show[0]);
			id_arr.push(data[i]['id']);
		}

		// 横1列以外 の場合、スクロールバーを追加
		if (this.list_id == '#UgcCreativeReviewArea-commentSwiper1') {
			$(this.list_id).append('<div class="UgcCreativeReview-swiper-scrollbar swiper-scrollbar"></div>');
		}

		// 表示対象外のDOMはreviews_dom_hideへ格納
		for (var i = 0; i < dom.length; i++) {
			if (!id_arr.includes(parseInt($(dom[i]).find('.UgcCreativeReview-review-id').text()))) {
				this.reviews_dom_hide.push(dom[i]);
			}
		}

		// もっと見るボタン
		if ($('.UgcCreativeReview-review_parent').children().filter(":hidden").length == 0) {
			$('.UgcCreativeReview-reviews-display_more').hide();
		} else {
			$('.UgcCreativeReview-reviews-display_more').show();
		}

		// スライダー・ページ送り
		if (this.swiper_mode) {
			let width_count = this._is_pc() ? this.review_widget['pc_width'] : this.review_widget['sd_width'];
			let height_count = this._is_pc() ? this.review_widget['pc_height'] : this.review_widget['sd_height'];
			if (id_arr.length > width_count * height_count) {
				$('.UgcCreativeReview-reviews-swiper_btn').show();
				$('.UgcCreativeReview-reviews-amazon_btn').show();
			} else {
				$('.UgcCreativeReview-reviews-swiper_btn').hide();
				$('.UgcCreativeReview-reviews-amazon_btn').hide();
			}
			this._set_swiper();
		}

	}


	// スワイパー設定
	_set_swiper() {

		let review_widget = this.review_widget;

		let width_count = this._is_pc() ? review_widget['pc_width'] : review_widget['sd_width']
		let height_count = this._is_pc() ? review_widget['pc_height'] : review_widget['sd_height']
		let pagination = false
		let slides_per_group = 1
		let next_el = '.UgcCreativeReview-swiper-button-next'
		let prev_el = '.UgcCreativeReview-swiper-button-prev'

		if (review_widget['display_type'] == 'amazon') {
			slides_per_group = width_count;
			next_el += '-amazon';
			prev_el += '-amazon';
		}

		let swiper_params = {
			slidesPerView: width_count
		,	slidesPerGroup: slides_per_group
		,	spaceBetween: 10
		,	navigation: {
				nextEl: next_el
			,	prevEl: prev_el
			}
		,	scrollbar: {
				el: '.UgcCreativeReview-swiper-scrollbar'
			}
		,	loop: (this.reviews.length - this.reviews_dom_hide.length) > width_count * height_count
		,	grid: {
				fill: 'row'
			,	rows: height_count
			}
		,	pagination: pagination
		,	on: {
				init: function() {
					const abc = Array.from(
						document.getElementsByClassName('UgcCreativeReview-gradItemInner')
					,	e => e.clientHeight
					);
					const maxHeight = Math.max.apply(null, abc)

					Array.from(document.getElementsByClassName('UgcCreativeReviewArea-commentArea')).forEach((v, i) => {
						let textContentsOuter = document.getElementsByClassName('UgcCreativeReview-gradItem')[i]
						let textContents = document.getElementsByClassName('UgcCreativeReview-gradItemInner')[i]
						let moreButton = document.getElementsByClassName('UgcCreativeReview-gradBtnWrap')[i]
						let limit_row = 5
						if (!textContentsOuter) {
							return;
						}

						// テキスト要素の高さを取得
						let textHeight = textContents.clientHeight
						let lineHeight = getComputedStyle(textContents).getPropertyValue('line-height')
						let fontSize = getComputedStyle(textContents).getPropertyValue('font-size')

						if (lineHeight == 'normal') {
							lineHeight = fontSize.replace(/[^-\d\.]/g, '') * 1.5
						} else {
							lineHeight = lineHeight.replace(/[^-\d\.]/g, '')
						}

						// 高さを最大行に揃える
						if (maxHeight < lineHeight * limit_row) {
							textContentsOuter.style.height = (maxHeight + 30) + 'px'
							moreButton.style.display = 'none'
						}

						// limit_row 行以内だと「もっと見る」削除
						if (textHeight <= lineHeight * limit_row && moreButton) {
							moreButton.style.visibility = 'hidden'
							if (textContentsOuter.classList.contains('UgcCreativeReview-ugcBlackGrad')) {
								textContentsOuter.classList.remove("UgcCreativeReview-ugcBlackGrad")
								textContentsOuter.classList.add("UgcCreativeReview-ugcBlackAll")
							} else if (textContentsOuter.classList.contains('UgcCreativeReview-ugcWhiteGrad')) {
								textContentsOuter.classList.remove("UgcCreativeReview-ugcWhiteGrad")
								textContentsOuter.classList.add("UgcCreativeReview-ugcWhiteAll")
							}
						}
					})

					for (var i = 0; i < $('.swiper-slide').length; i++) {
						var id = $($('.swiper-slide')[i]).find('input[id^=UgcCreativeReview-trigger]').attr('id');
						$($('.swiper-slide')[i]).find('input[id^=UgcCreativeReview-trigger]').attr('id', id + '-' + i);
						$($('.swiper-slide')[i]).find('label[for^=UgcCreativeReview-trigger]').attr('for', id + '-' + i);
					}
				}
			}
		}
		
		if (review_widget['display_type'] == 'slider') {
			new Swiper(this.list_id, swiper_params);
		}
	}
}
