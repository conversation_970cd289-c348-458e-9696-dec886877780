$('.search-open').on('click', function () {
  if ($('.card-pos').hasClass('card')) {
    OnOffsearchBox();
    $('.fa-chevron-up').hide();
    $('.fa-chevron-down').show();
    $('.card-pos').removeClass('card');
    $('.card-body-pos').removeClass('card-body');
  } else {
    OnAll()
  }
})
function OnOffsearchBox() {
  $('.search-box').slideToggle(300, 'swing');
}
function OnAll() {
  OnOffsearchBox();
  $('.fa-chevron-up').show();
  $('.fa-chevron-down').hide();
  $('.card-pos').addClass('card');
  $('.card-body-pos').addClass('card-body');
}
OnAll()

var multipleCancelButton = new Choices('#choices_multiple_product', {
  removeItemButton: true,
  searchResultLimit:5,
  renderChoiceLimit:5
});
