let video_sliders = document.querySelectorAll('video.thumb-img')
if (video_sliders.length > 0) {
  video_sliders.forEach(function(video_slider) {
    video_slider.controls = false;
    video_slider.loop = true;
    video_slider.play();
  });
}

class ugcTiktokEmbed {
  constructor(qid, api_url, is_set_slider = true, option = {}) {
    console.log('[ugcTiktokEmbed] Start')
    this._initial_set(qid, api_url, is_set_slider, option)
  }

  _initial_set(qid, api_url, is_set_slider, option) {
    this.qid = qid
    this.api_url = api_url
    this.takejsTiktokLoaded = false
    this.is_preview = option.is_preview
    window.parent.postMessage(["loaded"], "*")
    window.addEventListener('message', e => {
      if (e.data.action == 'sendFrameId') {
        this.frame_id = e.data.message
        this.takejsTiktokLoaded = true
        if (is_set_slider) {
          this._set_slider()
        }
      } else if (e.data.action == 'sendTiktokProductId') {
        localStorage.setItem('tiktok_product_id', e.data.message.tiktok_product_id)
        localStorage.setItem('tiktok_id', e.data.message.tiktok_id)
        localStorage.setItem('tiktok_qid', e.data.message.tiktok_qid)
      } else if (e.data.action == 'takejsTiktokLoaded') {
        if (!this.takejsTiktokLoaded) {
          window.parent.postMessage(["loaded"], "*")
        }
      }
    })
    console.log('[ugcTiktokEmbed] End')
  }

  _set_slider() {
    // swiperの設定を取得
    let req = new XMLHttpRequest()
    req.onreadystatechange = () => {
      if (req.readyState == 4) {
        if (req.status == 200) {
          let slider_option = JSON.parse(req.responseText)
          this._define_display(slider_option)

          // iframeの高さ送信
          setTimeout(() => {
            let height = document.getElementById('content-body').offsetHeight + 5
            if (height > 5) {
              window.parent.postMessage(["setTiktokHeight", { frame_id: this.frame_id, height: height }], "*")
            }
          }, 1000)
        }
      }
    }
    req.open('GET', `${this.api_url}/ugc/api/tiktok/get_option?qid=${this.qid}`, true)
    req.send(null)

    // モーダルのhtmlをすべて送る
    let c = document.getElementsByClassName('content-wrap')
    let ig_ids = Array.from(c).map(v => v.getAttribute('id'))
    let ig_htmls = Array.from(c).map(v => v.innerHTML)
    let ig_tmp = {}
    ig_ids.forEach((v, i) => ig_tmp[v] = ig_htmls[i])
    window.parent.postMessage(["getTiktoks", { frame_id: this.frame_id, data: ig_tmp }], "*")
  }

  _set_open_modal() {
    let elem = document.getElementsByClassName('modal_ig')
    for (let i = 0; i < elem.length; i++) {
      elem[i].addEventListener("click", (e) => {
        let video_id = e.target.getAttribute('data-ig_id')

        let html = document.getElementById(video_id).innerHTML

        // 一旦モーダル表示用のhtmlを送る
        window.parent.postMessage(["openTiktokModal", { frame_id: this.frame_id, html: html }], "*")

        // クリックしたスライドをサーバーに送る
        if (!this.is_preview) {
          navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate?igs=${video_id}&qid=${this.qid}&type=click`)
        }
      }, false);
    }
  }

  _define_display(slider_option) {
    if (slider_option.display_type == 'general') {
      this._define_general(slider_option)
    } else if (slider_option.display_type == 'slider') {
      this._define_swiper(slider_option)
    }
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate_noig?qid=${this.qid}&type=look_group`)
    }
  }

  _define_general(slider_option) {
    let slide_count, piece_width, piece_height, margin = 1, slide_vertical_count, margin_bottom = 1, slide_top = 80, slide_margin_bottom = 0
    let slide_actual_count = Array.from(document.getElementsByClassName('swiper-slide')).length

    if (window.innerWidth < 480) {
      slide_count = slider_option.sd_wide
      slide_vertical_count = Math.min(slider_option.sd_short, Math.ceil(slide_actual_count / slide_count))
      piece_width = window.innerWidth / slider_option.sd_wide - margin
      piece_height = piece_width * 2.59
      slide_margin_bottom = slide_count <= 2 ? 10 : slide_margin_bottom
      document.getElementById('true-swiper').style.cssText += 'width: 100% !important';
    }  else if (480 <= window.innerWidth) {
      slide_count = slider_option.pc_wide
      slide_top = slide_count == 1 ? 79 : slide_top
      slide_margin_bottom = slide_count <= 3 ? 10 : slide_margin_bottom
      slide_vertical_count = Math.min(slider_option.pc_short, Math.ceil(slide_actual_count / slide_count))
      piece_width = window.innerWidth / slider_option.pc_wide - margin
      piece_height = piece_width * 2.662
    }

    if (slider_option.is_display_comment == 0) {
      piece_height = piece_width*1.75
    } else {
      if (window.innerWidth < 480) {
        piece_height = piece_width*2.9
      }
      else {
        piece_height = piece_width*2.652
      }
    }

    let w = piece_width * slide_count + margin * (slide_count - 1)
    document.getElementsByClassName('swiper-container')[0].style.width = w + 'px'
    document.getElementsByClassName('swiper-wrapper')[0].style['flex-wrap'] = 'wrap'
    document.getElementsByClassName('swiper-wrapper')[0].style['align-content'] = 'flex-start'
    document.getElementsByClassName('tiktok-more-view-button')[0].style.display = 'inline'
    document.getElementById('true-swiper').style.display = 'block'
    document.getElementById('true-swiper').style.height = slider_option.is_display_comment == 0 ? (piece_width * 1.75 * slide_vertical_count + 'px') : ((piece_width * 1.75 + 160 + slide_margin_bottom) * slide_vertical_count + 'px')
    document.getElementsByClassName('swiper-button-prev')[0].style.display = 'none'
    document.getElementsByClassName('swiper-button-next')[0].style.display = 'none'

    let igs = []
    Array.from(document.getElementsByClassName('swiper-slide')).forEach((v, i) => {
      v.style.width = piece_width + 'px'
      v.style.height = slider_option.is_display_comment == 0 ? (piece_width * 1.75 + 'px') : (piece_width * 1.75 + 160 + slide_margin_bottom + 'px')
      v.style['margin-bottom'] = margin_bottom + 'px'
      if (i % slide_count != 0) {
        v.style['margin-left'] = margin + 'px'
      }

      // 指定段目以降初期は非表示
      if (i >= slide_count * slide_vertical_count) {
        v.style.display = 'none'
      } else {
        v.style.display = 'block'
        igs.push(v.getAttribute('data-ig_id'))
      }
    })

    Array.from(document.getElementsByClassName('tiktok-title')).forEach((v, i) => {
      v.style['padding-top'] = 15
      v.style['margin-top'] = slide_top + '%'
    })

    if (piece_width > 290) {
      Array.from(document.getElementsByClassName('video-overlays')).forEach((v, i) => {
        v.style.width = '54px'
        v.style.height = '64px'
      })
    } else {
      Array.from(document.getElementsByClassName('video-overlays')).forEach((v, i) => {
        v.style.width = '24px'
        v.style.height = '30px'
      })
    }

    // blockになっているスライドを送信
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate?igs=${igs.join(',')}&qid=${this.qid}&type=look`)
    }

    // スライドが全て見えていたらもっと見るボタンを消す
    if (Array.from(document.getElementsByClassName('swiper-slide')).every(v => v.style.display == 'block')) {
      document.getElementsByClassName('tiktok-more-view-button')[0].style.display = 'none'
    }

    // もっと見るボタン押下時
    document.getElementsByClassName('tiktok-more-view-button')[0].addEventListener("click", () => {
      let tmp_count = 0
      igs = []
      Array.from(document.getElementsByClassName('swiper-slide')).forEach(v => {
        if (v.style.display == 'none' && tmp_count < slide_count) {
          v.style.display = 'block'
          tmp_count += 1
          igs.push(v.getAttribute('data-ig_id'))
        }
      })

      // blockになっているスライドを送信
      if (!this.is_preview) {
        navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate?igs=${igs.join(',')}&qid=${this.qid}&type=look`)
      }

      // スライドが全て見えていたらもっと見るボタンを消す
      if (Array.from(document.getElementsByClassName('swiper-slide')).every(v => v.style.display == 'block')) {
        document.getElementsByClassName('tiktok-more-view-button')[0].style.display = 'none'
      }

      // iFrameの高さを調整
      let height = document.getElementById('content-body').offsetHeight + 5
      window.parent.postMessage(["setTiktokHeight", { frame_id: this.frame_id, height: height + piece_height + margin_bottom }], "*")
      height = document.getElementById('true-swiper').offsetHeight + margin_bottom
      document.getElementById('true-swiper').style.height = slider_option.is_display_comment == 0 ? (height + piece_width * 1.75 + 'px') : (height + piece_width * 1.75 + 160 + slide_margin_bottom + 'px')
    });

    this._set_open_modal()
  }

  _define_swiper(slider_option) {
    // 横幅の定義
    let slide_count, prev_pos, next_pos, margin = 1, slide_vertical_count, piece_height, piece_width, slide_top = 80, slide_margin_bottom = 0
    if (window.innerWidth < 480) {
      slide_count = slider_option.sd_wide
      slide_vertical_count = slider_option.sd_short
      slide_margin_bottom = slide_count <= 2 ? 10 : slide_margin_bottom
      prev_pos = (w) => `-5px`
      next_pos = (w) => `calc(100vw - 45px)`
      piece_height = piece_width * 2.59
      piece_width = window.innerWidth / slider_option.sd_wide
    } 
    else if (480 <= window.innerWidth) {
      slide_count = slider_option.pc_wide
      slide_top = slide_count == 1 ? 79 : slide_top
      slide_margin_bottom = slide_count <= 10 ? 10 : slide_margin_bottom
      slide_vertical_count = slider_option.pc_short
      prev_pos = (w) => `calc(((100vw - 100%) / 2) + 2px)`
      next_pos = (w) => `calc(((100vw - 100%) / 2 + 100%) - 43px)`
      piece_height = piece_width * 2.662
      piece_width = window.innerWidth / slider_option.pc_wide

      if(slide_count == 1) {
        document.getElementsByClassName('swiper-scrollbar')[0].style.bottom = '-60px'
      }
    }
    let top = '50%'
    if (slider_option.is_display_comment == 0) {
      piece_height = piece_width*1.75
    }
    let w = piece_width * slide_count + margin * (slide_count - 1)

    var elements = Array.from(document.getElementsByClassName("true-swiper"));
    elements.forEach(x => x.style.width = (w + 20));

    Array.from(document.getElementsByClassName('swiper-button-prev')).forEach(v => {
      v.style.top = top
      if( piece_width >= 310 ){
        v.style.top = '300px'
      }
    })

    Array.from(document.getElementsByClassName('swiper-button-next')).forEach(v => {
      v.style.top = top
      if( piece_width >= 310 ){
        v.style.top = '300px'
      }
    })

    Array.from(document.getElementsByClassName('true-swiper')).forEach((v, i) => {
      if (slide_vertical_count <= i) {
        v.style.display = 'none'
      }
      if (slide_vertical_count != i + 1) {
        document.getElementById(`logo_${i}`).style.display = 'none'
      }

      v.style.height = slider_option.is_display_comment == 0 ? (piece_width * 1.75 + 'px') : (piece_width * 1.75 + 210 + slide_margin_bottom + 'px')
      if( piece_width >= 310 ){
        v.style.height = '720px'
      }
    })

    Array.from(document.getElementsByClassName('swiper-container')).forEach(v => {
      v.style.width = w + 'px'
    })

    Array.from(document.getElementsByClassName('swiper-slide')).forEach((v, i) => {
      v.style.width = piece_width + 'px'
      v.style.height = slider_option.is_display_comment == 0 ? (piece_width * 1.75 + 'px') : (piece_width * 1.75 + 160 + slide_margin_bottom + 'px')
      if( piece_width >= 310 ){
        v.style.height = '650px'
      }
    })

    Array.from(document.getElementsByClassName('tiktok-title')).forEach((v, i) => {
      v.style['margin-top'] = slide_top + '%'
      v.style['padding-top'] = 15
    })

    if (piece_width > 290) {
      Array.from(document.getElementsByClassName('video-overlays')).forEach((v, i) => {
        v.style.width = '54px'
        v.style.height = '64px'
      })
    } else {
      Array.from(document.getElementsByClassName('video-overlays')).forEach((v, i) => {
        v.style.width = '24px'
        v.style.height = '30px'
      })
    }

    let vert_arr = [...Array(slide_vertical_count).keys()]
    vert_arr.forEach(i => {
      this.my_swiper = new Swiper('.swiper-container' + i, {
        spaceBetween: margin,
        slidesPerView: slide_count,
        loop: true,
        navigation: {
          nextEl: '.swiper-button-next' + i,
          prevEl: '.swiper-button-prev' + i,
        },
        scrollbar: {
          el: '.swiper-scrollbar' + i
        },
        on: {
          slideChange: (e) => {
            // 見たスライドをサーバーに送る
            let swiperWrapper = document.querySelector('.swiper-wrapper')
            let tmp_arr = [];
            for (let i = 0; i < swiperWrapper.childNodes.length; i++) {
                if (swiperWrapper.childNodes[i].className != null) {
                  tmp_arr.push(swiperWrapper.childNodes[i].getAttribute('data-ig_id'));
                }
            }

            let end_index = e.activeIndex + e.params.slidesPerView
            let img_arr = tmp_arr.concat(tmp_arr).slice(e.activeIndex, end_index)
            let igs = img_arr.join(',')

            if (!this.is_preview) {
              navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate?igs=${igs}&qid=${this.qid}&type=look`)
            }
          },
          afterInit: () => {
            this._set_open_modal()
          }
        }
      })
    })

    // swiper定義後の挙動
    if (slider_option.is_display_comment == 0) {
      let tmp
      Array.from(document.getElementsByClassName('swiper-slide')).forEach((v, i) => {
        if(window.innerWidth < 900){
          v.style.height = piece_width * 1.75;
        }else {
          if(slide_count == 3){
            v.style.height = '550px'
          }else if(slide_count == 4){
            v.style.height = '410px'
          }else if(slide_count == 5){
            v.style.height = '330px'
          }else if(slide_count == 6){
            v.style.height = '280px'
          }
        }
        if (i == 0) {
          tmp = v.style.width
        } else {
          tmp = v.style.height
        }
      })

      Array.from(document.getElementsByClassName('swiper-button-prev')).forEach(v => {
        v.style.top = `calc(${tmp} / 2 + 5px)`
      })
      Array.from(document.getElementsByClassName('swiper-button-next')).forEach(v => {
        v.style.top = `calc(${tmp} / 2 + 5px)`
      })
      if(window.innerWidth < 900) {
        Array.from(document.getElementsByClassName('true-swiper')).forEach((v, i) => {
          v.style.height = `calc(${tmp} + 50px)`
        })
      }else {
        Array.from(document.getElementsByClassName('true-swiper')).forEach((v, i) => {
          if(slide_count == 3){
            v.style.height = '630px'
          }else if(slide_count == 4){
            v.style.height = '490px'
          }else if(slide_count == 5){
            v.style.height = '410px'
          }else if(slide_count == 6){
            v.style.height = '360px'
          }
        })
      }
    }
  }

  // 購入ページ到達後の処理
  send_cv_data() {
    let lastclick_tiktok_id = localStorage.getItem('tiktok_id')
    let lastclick_tiktok_qid = localStorage.getItem('tiktok_qid')
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate?igs=${lastclick_tiktok_id}&qid=${lastclick_tiktok_qid}&type=cv`)
    }

    let lastclick_product_id = localStorage.getItem('tiktok_product_id')
    if (!this.is_preview) {
      navigator.sendBeacon(`${this.api_url}/ugc/api/tiktok/indicate_product?product_ids=${lastclick_product_id}&qid=${lastclick_tiktok_qid}&type=cv`)
    }

    localStorage.removeItem('tiktok_id')
    localStorage.removeItem('tiktok_qid')
    localStorage.removeItem('tiktok_product_id')
  }
}
