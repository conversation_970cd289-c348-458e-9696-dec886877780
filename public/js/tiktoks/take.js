// iframeスライダーの情報取得
let ugcTiktokSliderLoadTimeCount = 0

let ugcTiktokSliderLoadinterval = setInterval(function () {
  if (ugcTiktokSliderLoadTimeCount > 500) {
    clearInterval(ugcTiktokSliderLoadinterval)
    return
  }
  let ugcSliderInfoElm = document.getElementById('ugc-tiktok-slider-info')
  if (!ugcSliderInfoElm) return

  ugcTiktokSliderLoadTimeCount++

  let host = ugcSliderInfoElm.getAttribute('data-host')
  window.adtecSliderOption = {}
  window.adtecTiktok = {}
  window.ugcTiktokLoadedFrameNum = 0

  let iframes = Array.from(document.getElementsByClassName('ugc-tiktok-slider'))
  iframes.forEach(function (iframe, ind) {
    iframe.setAttribute("data-num", ind)

    let src = iframe.getAttribute('src')
    let qid = (new URL(src)).searchParams.get('qid')
    let frame_id = `${qid}_${ind}`

    let req = new XMLHttpRequest()

    req.onreadystatechange = () => {
      if (req.readyState == 4) {
        if (req.status == 200) {
          window.adtecSliderOption[frame_id] = JSON.parse(req.responseText)

          // アクティブがOFFだったらDOMごと削除
          if (window.adtecSliderOption[frame_id].is_active == 0 || window.adtecSliderOption[frame_id].is_active == undefined) {
            iframe.closest(".ugc-tiktok-slider-wrapper").remove()
          }
        }
      }
    }
    req.open('GET', `${host}/ugc/api/tiktok/get_option?qid=${qid}`, true)
    req.send(null)
  })

  iframes.forEach(function (iframe) {
    iframe.contentWindow.postMessage({ action: 'takejsTiktokLoaded' }, '*')
  })

  clearInterval(ugcTiktokSliderLoadinterval)
}, 100);

var load_flg = true
// iframeスライダーからのメッセージを受け取る
window.addEventListener('message', function (e) {
  if (e.data[0] == 'setTiktokHeight') {
    let iframes = Array.from(document.getElementsByClassName('ugc-tiktok-slider'))
    iframes.forEach(function (iframe) {
      let src = iframe.getAttribute('src')
      let qid = (new URL(src)).searchParams.get('qid')
      let data_num = iframe.getAttribute('data-num')
      let frame_id = `${qid}_${data_num}`
      if (frame_id == e.data[1].frame_id) {
        iframe.closest(".ugc-tiktok-slider-wrapper").style.height = `${e.data[1].height}px`
      }
    })

    let hv2 = document.getElementById('ugc-tiktok-slider-wrapper')
    if (hv2) {
      hv2.style.height = `${e.data[1].height}px`
    }
  }

  if (e.data[0] == 'openTiktokModal') {
    window.adtecTiktokSwal2 = Swal.fire({
      html: e.data[1].html,
      showCloseButton: false,
      width: 900,
      animation: false,
      showConfirmButton: false,
      background: window.adtecSliderOption[e.data[1].frame_id].modal_background_color,
      didOpen: function () { create_tiktok_modal(e.data[1].frame_id) },
      onClose: function () {
        load_flg = true
      }
    })
  }
  if (e.data[0] == 'getTiktoks') {
    window.adtecTiktok[e.data[1].frame_id] = e.data[1].data
  }
  if (e.data[0] == 'loaded') {
    window.ugcTiktokLoadedFrameNum += 1

    let iframes = Array.from(document.getElementsByClassName('ugc-tiktok-slider'))

    if (window.ugcTiktokLoadedFrameNum >= iframes.length) {
      iframes.forEach(function (iframe, ind) {
        let src = iframe.getAttribute('src')
        let qid = (new URL(src)).searchParams.get('qid')
        let frame_id = `${qid}_${ind}`
        iframe.contentWindow.postMessage({ action: 'sendFrameId', message: frame_id }, '*')
      })
    }
  }
}, false)
function arrow_click(s, frame_id) {
  load_flg = true

  let present = document.getElementById('ugc-present-ig-id').value
  let html = ''
  let targets = window.adtecTiktok[frame_id]

  let len_a = Object.keys(targets).length
  let index = Object.keys(targets).indexOf(present)
  if (index >= 0) {
    if (s == 'left') {
      index = (index + (len_a - 1)) % len_a
    } else {
      index = (index + 1) % len_a
    }
    html = targets[Object.keys(targets)[index]]
  }

  window.adtecTiktokSwal2 = Swal.fire({
    html: html,
    showCloseButton: false,
    width: 900,
    animation: false,
    showConfirmButton: false,
    background: window.adtecSliderOption[frame_id].modal_background_color,
    didOpen: function () { create_tiktok_modal(frame_id) }
  })
}
function create_tiktok_modal(frame_id) {
  let textContents = document.querySelector('.grad-item-inner')
  let moreButton = document.querySelector('.grad-btn-wrap')
  let video = document.querySelector('video.ugc-creative-thumb')
  if (video !== null) {
    video.controls = true;
    video.play();
  }
  let limit_row
  if (window.innerWidth < 480) {
    limit_row = 9
  } else {
    limit_row = 12
  }

  // テキスト要素の高さを取得
  let textHeight = textContents.clientHeight
  let lineHeight = getComputedStyle(textContents).getPropertyValue('line-height')
  let fontSize = getComputedStyle(textContents).getPropertyValue('font-size')

  if (lineHeight == 'normal') {
    lineHeight = fontSize.replace(/[^-\d\.]/g, '') * 1.5
  } else {
    lineHeight = lineHeight.replace(/[^-\d\.]/g, '');
  }

  // limit_row 行以内だと「もっと見る」削除
  if (textHeight <= lineHeight * limit_row) {
    moreButton.style.display = 'none';
  }

  // 矢印の定義
  if (load_flg === true) {
    let prev_html = `<div class="swiper-button-common swiper-button-prev" onClick="arrow_click('left', '${frame_id}')"></div>`
    let prev_next = `<div class="swiper-button-common swiper-button-next" onClick="arrow_click('right', '${frame_id}')"></div>`
    document.querySelector('.swal2-container').insertAdjacentHTML('beforeend', prev_html);
    document.querySelector('.swal2-container').insertAdjacentHTML('beforeend', prev_next);

    if (window.innerWidth >= 480) {
      let w = document.getElementsByClassName('swal2-popup')[0].offsetWidth
      document.getElementsByClassName('swiper-button-next')[0].style.right = `calc((100% - ${w}px) / 2 - var(--swiper-navigation-size))`
      document.getElementsByClassName('swiper-button-prev')[0].style.left = `calc((100% - ${w}px) / 2 - var(--swiper-navigation-size))`
      load_flg = false;
    }
  }

  // indicatorへの送信
  let host = document.getElementById('ugc-tiktok-slider-info').getAttribute('data-host')
  let tiktok_qid = frame_id.split('_')[0]
  let iframes = Array.from(document.getElementsByClassName('ugc-tiktok-slider'))
  Array.from(document.getElementsByClassName('ugc-btn-product-tiktok-link')).forEach(v => {
    v.onclick = function () {
      let id = this.getAttribute('data-id')
      let href = this.getAttribute('href')
      let tiktok_id = this.getAttribute('data-ig_id')
      iframes[0].contentWindow.postMessage({ action: 'sendTiktokProductId', message: {tiktok_product_id: id, tiktok_id: tiktok_id, tiktok_qid: tiktok_qid }}, '*')
      navigator.sendBeacon(`${host}/ugc/api/tiktok/indicate_product?product_ids=${id}&qid=${tiktok_qid}&type=click`)
      // localStorageにtiktok_idを保存
      localStorage.setItem('tiktok_id', tiktok_id)
      localStorage.setItem('tiktok_qid', tiktok_qid)

     // botchanを開く、もしくは商品URLを開く
      if (href === ':open_botchan:') {
        if (typeof btag === 'function') {
          btag('event', 'open')
        }
      } else if(href === ':open_ecch:'){
        var targetWindow = document.getElementById('previewSdk').contentWindow;
        targetWindow.postMessage({action: "openPreview", actionData: "none"}, "https://ec-chatbot.com");
      } else {
        window.open(href, '_blank')
      }
    }
  })
}
