@extends('layouts.app')
<style>
  input[type='text']{
  }
  
  .bold{
    font-weight: bold;
  }
  .flex{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
</style>
@section('content')
<div class="container">
  <div class="bread"><a href={{url('/')}}>HOME</a> > <a href="{{url('/group')}}">UGC一覧</a> > <span>UGCセット{{ $is_edit ? '編集' : '登録' }}</span></div>
  <div class="top-border"></div> 

  <div class="row title-top">
    <div class="col-md-12">
      <h3 class="title-text">UGCセット{{ $is_edit ? '編集' : '登録' }}</h3>
    </div>

    @if($is_edit)
    <div class="text-right mb-2" style="width:100%;">
      <button class="dash_button btn btn-3 btn--brown mr-4" style="position: relative; border:none; width:12rem;"
      data-id="{{$group->id}}" >
        <span style="padding: 0.5rem">ダッシュボード</span>
        <i class="py-2 fas fa-chart-line icon-center" style=""></i>
      </button>

      <button class="preview_button btn btn-3 btn--brown mr-4" style="position: relative; border:none; width:12rem;"
      data-qid="{{$group->qid}}">
        <span style="padding: 0.5rem">プレビュー</span>
        <i class="py-2 far fa-eye icon-center" style=""></i>
      </button>
    </div>
    @endif

    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          {{ Form::model($group, ['url' => !!$group->id ? "group/update/{$group->id}" : 'group/create', 'files' => true]) }}
            <div class="form-group">
              {{ Form::label('name', 'UGCセット名', ['class' => 'col-12 col-form-label bold']) }}
              <div class="col-md-10">
                {{ Form::text('name', $group->name, ['class' => 'col-12 new-text-input form-control']) }}
              </div>
            </div>

            <div class="form-group">
              {{ Form::label('url', '表示先（URL）', ['class' => 'col-12 col-form-label bold']) }}
              <div class="col-md-10">
                {{ Form::text('url', $group->url, ['class' => 'col-12 new-text-input form-control']) }}
              </div>
            </div>

            <div class="form-group">
              <div class="col-md-10 col-form-label bold">
                チャットボット内表示
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_display_inside_chatbot1" class="form-check-label radio-label">
                      {{ Form::radio('is_display_inside_chatbot', 1, $group->is_display_inside_chatbot == 1, ['id' => 'is_display_inside_chatbot1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label for="is_display_inside_chatbot2" class="form-check-label radio-label">
                      {{ Form::radio('is_display_inside_chatbot', 0, $group->is_display_inside_chatbot == 0, ['id' => 'is_display_inside_chatbot2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                表示形式
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="display_type1" class="form-check-label radio-label">
                      {{ Form::radio('display_type', 'general', $group->display_type == 'general', ['id' => 'display_type1', 'class' => 'form-check-input', 'checked']) }}
                      <span>一覧表示</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="display_type2" class="form-check-label radio-label">
                      {{ Form::radio('display_type', 'slider', $group->display_type == 'slider', ['id' => 'display_type2', 'class' => 'form-check-input']) }}
                      <span>スライダー表示</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="display_type3" class="form-check-label radio-label">
                      {{ Form::radio('display_type', 'lvuy', $group->display_type == 'lvuy', ['id' => 'display_type3', 'class' => 'form-check-input']) }}
                      <span>LVUY型一覧表示</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="form-group lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                UGCの形
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="display_style1" class="form-check-label radio-label">
                      {{ Form::radio('display_style', 'square', $group->display_type == 'square', ['id' => 'display_style1', 'class' => 'form-check-input', 'checked']) }}
                      <span>正方形</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="display_style2" class="form-check-label radio-label">
                      {{ Form::radio('display_style', 'portrait', $group->display_type == 'portrait', ['id' => 'display_style2', 'class' => 'form-check-input']) }}
                      <span>縦長</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                日付表示
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_display_date1" class="form-check-label radio-label">
                      {{ Form::radio('is_display_date', 1, $group->is_display_date == 1, ['id' => 'is_display_date1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_display_date2" class="form-check-label radio-label">
                      {{ Form::radio('is_display_date', 0, $group->is_display_date == 0, ['id' => 'is_display_date2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                写真利用について報告
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_display_report_mark1" class="form-check-label radio-label">
                      {{ Form::radio('is_display_report_mark', 1, $group->is_display_report_mark == 1, ['id' => 'is_display_report_mark1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_display_report_mark2" class="form-check-label radio-label">
                      {{ Form::radio('is_display_report_mark', 0, $group->is_display_report_mark == 0, ['id' => 'is_display_report_mark2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                コメント表示
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_display_comment1" class="form-check-label radio-label">
                      {{ Form::radio('is_display_comment', 1, $group->is_display_comment == 1, ['id' => 'is_display_comment1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_display_comment2" class="form-check-label radio-label">
                      {{ Form::radio('is_display_comment', 0, $group->is_display_comment == 0, ['id' => 'is_display_comment2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                コメントの文字色
              </div>
              <div class="col-md-10 col-form-label row comment_text_color">
                <span class="sharp">#</span>
                <div class="color-padding" style="position: relative;">
                  {{ Form::text('col', str_replace("#","",$group->comment_text_color), ['class' => 'new-text-input form-control selectpicker' ,'style' => 'width:130px;']) }}
                  <label for="comment_text_color">
                    <div class="col-picker-box" style="background: {{$group->comment_text_color}};"></div>
                  </label>
                  <input type="color" id="comment_text_color" name="comment_text_color" value="{{ $group->comment_text_color }}" style="position:absolute;bottom:0;right:30px;z-index:-999;">
                </div>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                動画自動生成
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_auto_play_video1" class="form-check-label radio-label">
                      {{ Form::radio('is_auto_play_video', 1, $group->is_auto_play_video == 1 || $group->is_auto_play_video == 0, ['id' => 'is_auto_play_video1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_auto_play_video2" class="form-check-label radio-label">
                      {{ Form::radio('is_auto_play_video', 0, $group->is_auto_play_video == 2, ['id' => 'is_auto_play_video2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                枠線
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_use_border1" class="form-check-label radio-label">
                      {{ Form::radio('is_use_border', 1, $group->is_use_border == 1, ['id' => 'is_use_border1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_use_border2" class="form-check-label radio-label">
                      {{ Form::radio('is_use_border', 0, $group->is_use_border == 0, ['id' => 'is_use_border2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="{{$group->display_type == 'lvuy' && $group->is_use_border == 1 ? '' : 'hide'}} form-group border-color-group">
              <div class="col-md-10 col-form-label bold">
                枠線の角枠
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="border_style1" class="form-check-label radio-label">
                      {{ Form::radio('border_style', 'circle', $group->border_style == 'circle', ['id' => 'border_style1', 'class' => 'form-check-input', 'checked']) }}
                      <span>丸</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="border_style2" class="form-check-label radio-label">
                      {{ Form::radio('border_style', 'square', $group->border_style == 'square' || $group->border_style == '', ['id' => 'border_style2', 'class' => 'form-check-input']) }}
                      <span>四角</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="border-color-group lvuy-display {{$group->display_type == 'lvuy' && $group->is_use_border == 1 ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                枠線の色
              </div>
              <div class="col-md-10 col-form-label row border_color">
                <span class="sharp">#</span>
                <div class="color-padding" style="position: relative;">
                  {{ Form::text('col', str_replace("#","",$group->border_color), ['class' => 'new-text-input form-control selectpicker' ,'style' => 'width:130px;']) }}
                  <label for="border_color">
                    <div class="col-picker-box" style="background: {{$group->border_color}};"></div>
                  </label>
                  <input type="color" id="border_color" name="border_color" value="{{ $group->border_color }}" style="position:absolute;bottom:0;right:30px;z-index:-999;">
                </div>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                背景色
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_use_background_color1" class="form-check-label radio-label">
                      {{ Form::radio('is_use_background_color', 1, $group->is_use_background_color == 1, ['id' => 'is_use_background_color1', 'class' => 'form-check-input']) }}
                      <span>オン</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_use_background_color2" class="form-check-label radio-label">
                      {{ Form::radio('is_use_background_color', 0, $group->is_use_background_color == 0, ['id' => 'is_use_background_color2', 'class' => 'form-check-input']) }}
                      <span>オフ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy' ? 'form-group' : 'form-group hide'}}">
              <div class="col-md-10 col-form-label bold">
                背景の色
              </div>
              <div class="col-md-10 col-form-label row background_color">
                <span class="sharp">#</span>
                <div class="color-padding" style="position: relative;">
                  {{ Form::text('col', str_replace("#","",$group->background_color), ['class' => 'new-text-input form-control selectpicker' ,'style' => 'width:130px;']) }}
                  <label for="background_color">
                    <div class="col-picker-box" style="background: {{$group->background_color}};"></div>
                  </label>
                  <input type="color" id="background_color" name="background_color" value="{{ $group->background_color }}" style="position:absolute;bottom:0;right:30px;z-index:-999;">
                </div>
              </div>
            </div>

            <div class="form-group ">
              <div class="col-md-2 col-form-label bold">
                ヘッダー
              </div>
              <div class="col-md-10 col-form-label">
                <div class="row" style="margin-bottom: 20px">
                  @foreach([1,2,9,10,3,4,5,6] as $i)
                  <div class="col-md-3 col-xs-6 sample-image" 
                  style="cursor: pointer; align-items: center;display: flex; margin-top: 30px;" data-id="sample{{ $i }}">
                    <div>
                      <img src={{ asset("/images/ugc_banner{$i}.png") }} alt="バナー" style="width: 100%;" />

                      <ul class="b-radio label_list ul-radio" style="display: flex;justify-content: center;margin-right:15px;">
                        <li class="label_list_item">
                          <label for="sample{{$i}}_radio" class="form-check-label baner">
                            <input type="radio" id="sample{{$i}}_radio" name="baner_image" data-id="sample{{ $i }}_radio"
                             {{ 'sample' . $i == $group->header_image ? 'checked' : '' }}/>
                            <span></span>
                          </label>
                        </li>
                      </ul>

                    </div>
                  </div>
           
                  @endforeach
                  {{ Form::hidden('header_image', $group->header_image, ['id' => 'header_image']) }}
  
                  <div class=" col-xs-6 ml-5" style=" margin-top: 50px;">
                      @if(!str_starts_with($group->header_image, 'sample'))
                        <img class="baner-img pointer" src={{ asset($group->header_url()) }}  alt="バナー" style="width: 250px;" />
                      @else
                        <img class="baner-img pointer hide" src="" alt="バナー" style="width: 250px;" />
                      @endif
                     
                      <label class="row pointer" for="add-image">
                        {{ Form::file('header_image', ['id' => 'add-image', 'accept' => '.jpg,.png']) }}
                        <i class="fas fa-plus-circle fa-3x flex-c banar-i 
                        @if(!str_starts_with($group->header_image, 'sample')) 
                          hide 
                        @endif 
                         " style="width: 100%;text-align:center;"></i>
                      </label>
                  
                    <div style="margin-bottom: 10px;text-align:center;font-size:0.8rem;">推奨 250px × 250px</div>
                    
                    <ul class="b-radio label_list flex-c ul-radio" style="margin-right:15px;">
                      <li class="label_list_item">
                        <label for="baner_radio_add" class="form-check-label baner">
                          <input type="radio" id="baner_radio_add" name="baner_image" data-id="sample_add_radio"
                          @if(!str_starts_with($group->header_image, 'sample')) checked @endif />
                          <span></span>
                        </label>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <div class="form-group">
              <div class="col-md-10 col-form-label bold">
                メインカラー
              </div>
              <div class="col-md-10 col-form-label row main-color">
                <span class="sharp">#</span>
                <div class="color-padding" style="position: relative;">
                  {{ Form::text('col', str_replace("#","",$group->main_color), ['class' => 'new-text-input form-control selectpicker' ,'style' => 'width:130px;']) }}
                  <label for="main_color">
                    <div class="col-picker-box" style="background: {{$group->main_color}};"></div>
                  </label>
                  <input type="color" id="main_color" name="main_color" value="{{ $group->main_color }}" style="position:absolute;bottom:0;right:30px;z-index:-999;">
                </div>
                
              </div>
            </div>

            <div class="lvuy-display {{$group->display_type == 'lvuy'? '' : 'hide'}} form-group">
              <div class="col-md-10 col-form-label bold">
                インスタグラムアイコン
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="instagram_mark_style1" class="form-check-label radio-label">
                      {{ Form::radio('instagram_mark_style', 'default', $group->instagram_mark_style == 'default', ['id' => 'instagram_mark_style1', 'class' => 'form-check-input', 'checked']) }}
                      <span>デフォルト</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="instagram_mark_style2" class="form-check-label radio-label">
                      {{ Form::radio('instagram_mark_style', 'main_color', $group->instagram_mark_style == 'main_color' || $group->instagram_mark_style == '', ['id' => 'instagram_mark_style2', 'class' => 'form-check-input']) }}
                      <span>メインカラーに合わせる</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <?php $op6 = array(1=>1,2=>2,3=>3,4=>4,5=>5,6=>6); ?>
            <?php $op3 = array(1=>1,2=>2,3=>3); ?>
      
            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                UGC表示数（スマホ縦）
              </div>
              {{ Form::select('sd_short', $op6, $group->sd_short,['id' => 'sd_short', 'class' => 'selectpicker col-2']) }}
            </div>

            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                UGC表示数（スマホ横）
              </div>
              {{ Form::select('sd_wide', $op3, $group->sd_wide,['id' => 'sd_wide', 'class' => 'selectpicker col-2']) }}
            </div>

            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                UGC表示数（PC縦）
              </div>
              {{ Form::select('pc_short', $op6, $group->pc_short,['id' => 'pc_short', 'class' => 'selectpicker col-2']) }}
            </div>

            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                UGC表示数（PC横）
              </div>
              {{ Form::select('pc_wide', $op6, $group->pc_wide,['id' => 'pc_wide', 'class' => 'selectpicker col-2']) }}
            </div>

            <div class="form-group ">
              <div class="col-md-10 col-form-label bold">
                もっと見るテキスト
              </div>
              <div class="col-md-10 col-form-label">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="more_text1" class="form-check-label radio-label">
                      {{ Form::radio('more_text', 'もっと見る', $group->more_text == 'もっと見る', ['id' => 'more_text1', 'class' => 'form-check-input']) }}
                      <span>もっと見る</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="more_text2" class="form-check-label radio-label">
                      {{ Form::radio('more_text', 'View more', $group->more_text == 'View more', ['id' => 'more_text2', 'class' => 'form-check-input']) }}
                      <span>View more</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="not-lvuy-display form-group {{ $group->display_type == 'lvuy' ? 'hide' : '' }}">
              <div class="col-md-10 col-form-label bold">
                モーダル背景色
              </div>

              <div class="col-md-10 col-form-label">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="modal_background_color1" class="form-check-label radio-label">
                      {{ Form::radio('modal_background_color', 'white', $group->modal_background_color == 'white', ['id' => 'modal_background_color1', 'class' => 'form-check-input']) }}
                      <span>white</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="modal_background_color2" class="form-check-label radio-label">
                      {{ Form::radio('modal_background_color', 'black', $group->modal_background_color == 'black', ['id' => 'modal_background_color2', 'class' => 'form-check-input']) }}
                      <span>black</span>
                    </label>
                  </li>
                </ul>
              </div>
              <!-- <div class="col-md-10 col-form-label">
                <div class="form-check form-check-inline">
                    {{ Form::label('', '', ['class' => 'form-check-label']) }}
                </div>
                <div class="form-check form-check-inline">
                    {{ Form::label('', '', ['class' => 'form-check-label']) }}
                </div>
              </div> -->
            </div>

            <div class="not-lvuy-display form-group {{ $group->display_type == 'lvuy' ? 'hide' : '' }}">
              <div class="col-md-10 col-form-label">
                <div class="bold">固定表示</div>
                <p style="font-size: 95%;color: #BE914E !important;">（Instagramのタブ表示機能をご利用の場合のみ「ON」してください）</p>
              </div>

              <div class="col-md-10 col-form-label">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="is_fixed_display1" class="form-check-label radio-label">
                      {{ Form::radio('is_fixed_display', '0', $group->is_fixed_display == 0, ['id' => 'is_fixed_display1', 'class' => 'form-check-input']) }}
                      <span>OFF</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="is_fixed_display2" class="form-check-label radio-label">
                      {{ Form::radio('is_fixed_display', '1', $group->is_fixed_display == '1', ['id' => 'is_fixed_display2', 'class' => 'form-check-input']) }}
                      <span>ON</span>
                    </label>
                  </li>
                </ul>
              </div>
              <!-- <div class="col-md-10 col-form-label">
                <div class="form-check form-check-inline">
                    {{ Form::label('', '', ['class' => 'form-check-label']) }}
                </div>
                <div class="form-check form-check-inline">
                    {{ Form::label('', '', ['class' => 'form-check-label']) }}
                </div>
              </div> -->
            </div>
            

            <div class="form-group">
              <div class="col-md-10 col-form-label bold">
                コメントの位置
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item">
                    <label for="bottom_text_align1" class="form-check-label radio-label">
                      {{ Form::radio('bottom_text_align', 'left', $group->bottom_text_align == 'left', ['id' => 'bottom_text_align1', 'class' => 'form-check-input']) }}
                      <span>左寄せ</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="bottom_text_align2" class="form-check-label radio-label">
                      {{ Form::radio('bottom_text_align', 'center', $group->bottom_text_align == 'center', ['id' => 'bottom_text_align2', 'class' => 'form-check-input']) }}
                      <span>中央寄せ</span>
                    </label>
                  </li>
                  <li class="label_list_item">
                    <label  for="bottom_text_align3" class="form-check-label radio-label">
                      {{ Form::radio('bottom_text_align', 'right', $group->bottom_text_align == 'right', ['id' => 'bottom_text_align3', 'class' => 'form-check-input']) }}
                      <span>右寄せ</span>
                    </label>
                  </li>
                </ul>
              </div>
            </div>

            <div class="form-group">
              <div class="col-md-10 col-form-label bold">
                コメント
              </div>
              <div class="col-md-10">
                <ul class="b-radio label_list ul-radio">
                  <li class="label_list_item" style="width: 100%">
                    <label for="sentence_1" class="form-check-label radio-label">
                      {{ Form::radio('bottom_right_text_option', 0, $group->bottom_right_text_option == 0, ['id' => 'sentence_1', 'class' => 'form-check-input']) }}
                      <span>なし</span>
                    </label>
                  </li>
                  <li class="label_list_item" style="width: 100%">
                    <label for="sentence_2" class="form-check-label radio-label">
                      {{ Form::radio('bottom_right_text_option', 1, $group->bottom_right_text_option == 1, ['id' => 'sentence_2', 'class' => 'form-check-input']) }}
                      <span>※薬事法を鑑みてレビュー内の一部表現については修正を行っております。</span>
                    </label>
                  </li>
                  <li class="label_list_item" style="width: 100%">
                    <label for="sentence_3" class="form-check-label radio-label">
                      {{ Form::radio('bottom_right_text_option', 2, $group->bottom_right_text_option == 2, ['id' => 'sentence_3', 'class' => 'form-check-input']) }}
                      <span>※お客様の感想であり、商品の効果・効能を表すものではございません。</span>
                    </label>
                  </li>
                  <li class="label_list_item" style="width: 100%">
                    <label for="sentence_4" class="form-check-label radio-label">
                      {{ Form::radio('bottom_right_text_option', 3, $group->bottom_right_text_option == 3, ['id' => 'sentence_4', 'class' => 'form-check-input']) }}
                      <span>※個人の感想です。</span>
                    </label>
                  </li>
                  <li class="label_list_item" style="width: 100%">
                    <label for="sentence_6" class="form-check-label radio-label">
                      {{ Form::radio('bottom_right_text_option', 5, $group->bottom_right_text_option == 5, ['id' => 'sentence_6', 'class' => 'form-check-input']) }}
                      <span>
                        PR
                      </span>
                        <div style="margin-left: 28px;">
                          ※Instagram投稿からお喜びの声を抜粋しております。<br>
                          ※個人の感想であり、商品の効果・効能を表すものではございません。
                        </div>
                    </label>
                  </li>
                  <li class="label_list_item" style="width: 100%">
                    <label for="sentence_5" class="form-check-label radio-label" style="width: 100%;">
                      {{ Form::radio('bottom_right_text_option', 4, $group->bottom_right_text_option == 4, ['id' => 'sentence_5', 'class' => 'form-check-input']) }}
                      <span>その他（自由入力）</span>
                    </label>
                    {{ Form::textarea('bottom_right_text', $group->bottom_right_text, ['id' => 'bottom_right_text', 'rows' => 4, 'cols' => 54, 'style' => 'margin-top: 15px; width: 100%;', 'disabled' => $group->bottom_right_text_option != 4]) }}
                  </li>
                </ul>
              </div>
            </div>

            <div class="form-group">
              <div class="col text-center">
                {{ Form::button('登録', ['id' => 'submit_button', 'class' => 'col-md-3 btn btn--brown']) }}
              </div>
            </div>

          {{ Form::close() }}

          <div id="error_message_wrapper" class="alert alert-danger display-none">
            <ul id="error_message_js">
            </ul>
          </div>
          @if ($errors->any())
            <div class="alert alert-danger">
              <ul>
                @foreach ($errors->all() as $error)
                  <li>{{ $error }}</li>
                @endforeach
              </ul>
            </div>
          @endif 
        </div>
      </div>
    </div>
  </div>
</div>

<script>
    const radioBtn = document.getElementById('display_type3');
    const targetElement = document.getElementById('target-element');

    radioBtn.addEventListener('change', function() {
        if (this.checked) {
            targetElement.style.display = 'block'; // または 'inline', 'flex' など、適切な表示方法
        } else {
            targetElement.style.display = 'none';
        }
    });

    // 初期状態も反映 (ページロード時)
    if (radioBtn.checked) {
        targetElement.style.display = 'block';
    }
</script>
<script>
$(function(){
  $('#submit_button').on('click', function(){
    let errors = [];
    if ($('[name = "name"]').val().length == 0) {
      errors.push('UGCセット名を入力してください')
    }
    if ($('[name = "url"]').val().length == 0) {
      errors.push('表示先（URL）を入力してください')
    }
    if (errors.length > 0) {
      $('#error_message_wrapper').removeClass('display-none');
      $('#error_message_js').empty();
      errors.forEach(function(e){
        $('#error_message_js').append(`<li>${e}</li>`);
      })
      return
    }
    $(this).closest('form').submit();
  })

  disableCheckbox();
  function disableCheckbox(){
    if( $('.baner-img').hasClass('hide') ){
      $('#baner_radio_add + span').addClass('hide')
    }else{
      $('#baner_radio_add + span').removeClass('hide')
    }
  }

  $('.sample-image').on('click', function(){
    $('#header_image').val($(this).data('id'));
  })

  $('.baner-img').on('click',function(){
    $('#add-image').click();
    return false;
  })

  $('#add-image').on('change',function(){
    if (this.files.length > 0) {
      // 選択されたファイル情報を取得
      var file = this.files[0];
      
      // readerのresultプロパティに、データURLとしてエンコードされたファイルデータを格納
      var reader = new FileReader();
      reader.readAsDataURL(file);
      
      reader.onload = function() {
        $('.baner-img').attr('src', reader.result );
        $('.baner-img').removeClass('hide')
        $('.banar-i').addClass('hide')
        disableCheckbox();
      }
    }
    
  })

  $('#main_color').on('change', function(){
    $('.main-color .col-picker-box').css('background-color',$(this).val());
    $('.main-color input[type="text"]').val( $(this).val().replace('#',''));
  })

  $('#background_color').on('change', function(){
    $('.background_color .col-picker-box').css('background-color',$(this).val());
    $('.background_color input[type="text"]').val( $(this).val().replace('#',''));
  })

  $('#border_color').on('change', function(){
    $('.border_color .col-picker-box').css('background-color',$(this).val());
    $('.border_color input[type="text"]').val( $(this).val().replace('#',''));
  })

  $('#comment_text_color').on('change', function(){
    $('.comment_text_color .col-picker-box').css('background-color',$(this).val());
    $('.comment_text_color input[type="text"]').val( $(this).val().replace('#',''));
  })

  $('.main-color input[type="text"]').on('input', function(){
    var color = '#' + $(this).val().replace('#','');
    $('.main_color .col-picker-box').css('background-color', color);
    $('#main_color').val(color);
  })

  $('.border-color input[type="text"]').on('input', function(){
    var color = '#' + $(this).val().replace('#','');
    $('.border_color .col-picker-box').css('background-color', color);
    $('#border_color').val(color);
  })

  $('.background_color input[type="text"]').on('input', function(){
    var color = '#' + $(this).val().replace('#','');
    $('.background_color .col-picker-box').css('background-color', color);
    $('#background_color').val(color);
  })

  $('[name=is_use_border]').on('change', function(e) {
    console.log("lol: " + e.target.value);
    var is_use_border = e.target.value;

    if (is_use_border == 1) {
      $(".border-color-group").removeClass('hide');
    } else {
      $(".border-color-group").addClass('hide');
    }
  });

  $('[name=is_display_inside_chatbot]').on('change', function(){
    var is_chatbot_enabled = $(this).val() == 1;
    if (is_chatbot_enabled) {
      // チャットボット内表示がオンの場合、LVUY型一覧表示を非表示にする
      $('#display_type3').closest('li').hide();
      if ($('#display_type3').is(':checked')) {
        // LVUY型が選択されている場合は、スライダー表示に変更
        $('#display_type2').prop('checked', true).trigger('change');
      }
    } else {
      // チャットボット内表示がオフの場合、すべての表示形式を表示
      $('#display_type3').closest('li').show();
    }
  });

  $('[name=display_type]').on('change', function(){
    var is_selected_lvuy = $("#display_type3")[0].checked;
    if (is_selected_lvuy) {
      $('.not-lvuy-display').addClass('hide');
      $('.lvuy-display').removeClass('hide');
      var comment_text_color = $('.comment_text_color input[type="text"]').val();

      if (!comment_text_color) comment_text_color = '#000000';
      $('.comment_text_color .col-picker-box').css('background-color', comment_text_color);
      $('.comment_text_color input[type="text"]').val(comment_text_color.replace('#',''));

      var border_color = $('.border_color input[type="text"]').val();

      if (!border_color) border_color = '#dddddd';

      $('.border_color .col-picker-box').css('background-color',border_color);
      $('.border_color input[type="text"]').val(border_color.replace('#',''));

      var background_color = $('.background_color input[type="text"]').val();

      if (!background_color) background_color = '#ffffff';

      $('.background_color .col-picker-box').css('background-color', background_color);
      $('.background_color input[type="text"]').val(background_color.replace('#',''));
    } else {
      $('.lvuy-display').addClass('hide');
      $('.not-lvuy-display').removeClass('hide');
      if (!$(".main-color input").val()) {
        $(".main-color input").val("#000000")
        $(".main-color .col-picker-box").css('background-color', 'black');
      }
    }
  })

  $('.sample-image').on('click', function(){
    var id = $(this).data('id');
    $('#'+id+"_radio").prop('checked',true);
  })

  $('.preview_button').on('click', function(){
    let qid = $(this).data('qid');
    location.href = `/ugc/group/test/${qid}`;
  });
  
  $('.dash_button').on('click', function(){
    let g_id = $(this).data('id');
    location.href = `/ugc/group/show/${g_id}`;
  });

  $('input[name="bottom_right_text_option"]').on('click', function(e){
    if (e.target.value == 4) {
      $('#bottom_right_text')[0].disabled = false;
    } else {
      $('#bottom_right_text')[0].disabled = true;
    }
  });

  // 初期状態の設定（ページロード時）
  var initial_chatbot_value = $('input[name="is_display_inside_chatbot"]:checked').val();
  if (initial_chatbot_value == 1) {
    $('#display_type3').closest('li').hide();
    if ($('#display_type3').is(':checked')) {
      $('#display_type2').prop('checked', true).trigger('change');
    }
  }

});
</script>
@endsection
