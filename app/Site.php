<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use App\MailSetting;
use Illuminate\Support\Facades\Log;

class Site extends Model
{
    protected $guarded = ['id'];

    public function get_new_review_link($email){
        $host = request()->getSchemeAndHttpHost();
        return $host . '/ugc/api/reviews/edit?site_id=' . $this->id . '&site_qid=' . $this->qid() . '&email=' . $email;
    }

    public function get_create_review_link(){
        $host = request()->getSchemeAndHttpHost();
        return $host . '/ugc/api/reviews/create';
    }

    public function qid(){
        $salt = 'Q2wKjvg9Ff6Gqbpv';
        return hash('SHA256', $this->id . $salt);
    }

    public function mail_setting(){
        return MailSetting::where('company_id', $this->company_id)->first();
    }

    public function logo_url(){
        return !empty($this->mail_setting()) ? $this->mail_setting()->logo_url : '';
    }

    public function logo($is_dynamic){
        if (empty($this->logo_url())) {
            return '';
        }
        if ($is_dynamic) {
            ini_set('user_agent','Mozilla/4.0 (compatible; MSIE 7.0b; Windows NT 6.0)');
            $arr = explode('/', $this->logo_url());
            $filename = end($arr);
            $size = getimagesize(storage_path() . "/app/public/logo_images/{$filename}");
            $html = "<amp-img src='{$this->logo_url()}' width='{$size[0]}' height='{$size[1]}' layout='responsive'></amp-img>";
            return $html;
        } else {
            return "<img src='{$this->logo_url()}' class='store_logo' style='width: 100%;'>";
        }
    }
}
