body {
    color: black;
}
.reviewSubmitButton {
    color: white;
    background-color: #3699FF;
    border: 1px solid #3699FF; 
    font-size: 14px;
    font-weight: normal;
    line-height: 1.5;
    position: relative;
    display: inline-block;
    padding: 8px 50px;
    cursor: pointer;
    text-align: center;
    vertical-align: middle;
    text-decoration: none;
    letter-spacing: 1px;
    border-radius: 3px; 
    cursor: pointer;
}
.reviewSubmitButtonWrapper{
    margin-top: 2em;
    width: 400px;
    height: 80px;
}
.inputForm{
    padding: 5px 7px;
    width: 100%;
}
.inputLabel{
    margin-top: 1.5rem;
}
.UgcCreativeReview-evaluation{
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}
.UgcCreativeReview-evaluation label{
    cursor: pointer;
    font-size: 26px;
    color: #ffcc00;
}
.reviewMailWrapper{
	padding:30px;
    border:1px solid #f0f0f0;
    background:#ffffff;
    max-width:450px;
    margin:0 auto;
    width:100%;
}
.productImageContain amp-img,
.productImageContain img {
   object-fit: contain;
}
.fixedContainer{
	position: relative;
    width: auto;
    height: 300px;
}