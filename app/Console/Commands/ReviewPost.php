<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Site;
use App\Review;
use App\ReviewSetting;
use App\ReviewNgWord;
use Aws\Comprehend\ComprehendClient;
use Aws;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

// Reviewレコードが作られるたびに実行する、呼び出し元はReviewObserver
class ReviewPost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'review:post {id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Post Review Automatically';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $id = $this->argument('id');

        $review = Review::where('has_auto_checked', 0)->where('id', $id)->first();
        $company_id = $review->company_id;
        $review_setting = (new ReviewSetting())->find_or_default($company_id);

        if (!$review_setting->auto_published) {
            return;
        }

        $credentials = new Aws\Credentials\Credentials(
            config('app.aws_access_key_id'),
            config('app.aws_secret_access_key')
        );

        $client = new ComprehendClient([
            'credentials' => $credentials,
            'region' => 'ap-northeast-1',
            'version' => '2017-11-27'
        ]);

        DB::transaction(function() use ($review, $company_id, $client, $review_setting) {
            $review->has_auto_checked = true;
            $review->save();

            // (1) 星が設定された値以上でのみ投稿する
            if ($review->ratings_of_review < $review_setting->star) {
                // $this->info(date('Y-m-d H:i:s') . ' [Scheduler] ratings_of_review is low review id:' . $r->id);
                return;
            }

            // (2) NGワード
            if ($review_setting->without_ng_word) {
                $ng_words = ReviewNgWord::where('published', 1)->where('company_id', $company_id)->get();
                foreach($ng_words as $w){
                    if (strpos($review->description_of_review, $w->word) !== false ||
                        strpos($review->title_of_review, $w->word) !== false ||
                        strpos($review->nick_name_of_reviewer, $w->word) !== false) {
                        // $this->info(date('Y-m-d H:i:s') . ' [Scheduler] ng word review id:' . $r->id);
                        return;
                    }
                }
            }

            // (3) AIスコア
            if (empty($review->ai_positive_score) || empty($review->ai_negative_score)) {
                $text_for_ai = $review->description_of_review . ' ' . $review->nick_name_of_reviewer;
                try {
                    $result = $client->detectSentiment([
                        'LanguageCode' => 'ja',
                        'Text' => $text_for_ai,
                    ]);
                } catch (Exception $e) {
                    Log::info(date('Y-m-d H:i:s') . ' [ReviewPost] ReviewPost ai error');
                    Log::info(date('Y-m-d H:i:s') . ' [ReviewPost] ReviewPost ' . $e->getMessage());
                    return;
                }

                $review->ai_positive_score = $result->get("SentimentScore")["Positive"];
                $review->ai_negative_score = $result->get("SentimentScore")["Negative"];
            }

            // total_scoreは0 ~ 1の間にある
            $total_score = $review->score;

            if ($total_score < ($review_setting->ai_score - 1) / 5) {
                // 自動投稿せずにスコアの記録のみする
                // $this->info(date('Y-m-d H:i:s') . ' [Scheduler] ai score is low review id:' . $r->id);
                $review->save();
                return;
            }

            // (1) ~ (3) を通過したレビューのみ自動投稿する
            $review->published = true;
            $review->save();
        });
    }

}
