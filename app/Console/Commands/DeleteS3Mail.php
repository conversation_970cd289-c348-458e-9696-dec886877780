<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DeleteS3Mail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'delete mail from S3';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] DeleteMail start');

        // mail-data-for-order-cpバケットのorder_mailsディレクトリごと削除
        Storage::disk('s3_mail')->deleteDirectory('order_mails');

        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] DeleteMail End');
    }
}
