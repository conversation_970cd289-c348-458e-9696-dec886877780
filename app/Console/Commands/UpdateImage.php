<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Instagram;
use App\User;
use Carbon\Carbon;
use Aws\S3\S3Client;
use Aws;

// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan igimage:update >> storage/logs/crontab.log 2>&1
class UpdateImage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'igimage:update';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Instagram thumbnail for URL signature expired';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $fv = config('app.facebook_version');
        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] UpdateImage start');
        $admin = '<EMAIL>'; // admin用のトークンを使用してAPIを使う
        $at = User::where('email', $admin)->first()->company_tied->access_token;
        $context = stream_context_create(['http' => ['ignore_errors' => true]]);
        $this->_refresh_token($admin);
        $error_count = 0;
        // $instagrams = Instagram::where('thumbnail_url', 'not like', '%ugc-thumbnail%')->get();
        $instagrams = Instagram::where('thumbnail_url', 'not like', '%ugc-%')->get();
        // $instagrams = Instagram::where('thumbnail_url', 'not like', '%ugc-creative.work%')->get();

        $credentials = new Aws\Credentials\Credentials(
            config('app.aws_r2_access_key_id'),
            config('app.aws_r2_secret_access_key')
        );

        $endpoint = config('app.CLOUDFLARE_R2_ENDPOINT');

        $s3_client = new S3Client([
            'credentials' => $credentials,
            'endpoint' => $endpoint,
            'region' => 'auto',
            'version' => 'latest'
        ]);

        // 一時ファイルを削除しておく
        array_map('unlink', glob(storage_path() . "/app/public/tmp/*.jpg"));

        foreach ($instagrams as $ig) {
            // サムネイルの更新
            $thumbnail_url = $ig->thumbnail_url;
            $permalink = $ig->permalink;

            $image_s3_status = $this->_image_s3_upload($thumbnail_url, $permalink, $s3_client, $ig->ig_id, $fv, $at, $context);
            var_dump($ig->ig_id);
            if ($image_s3_status['status']) {

                // 画像再生成したかどうかで分岐
                if($image_s3_status['is_reCreate_result']){
                    $r2_url = config('app.CLOUDFLARE_R2_URL').$image_s3_status['filename'];
                    $ig->update(['thumbnail_url'=> $r2_url]);
                    $this->info('[Success] Update ReCreate. ' . $image_s3_status['message']);
                }else{
                    $r2_url = config('app.CLOUDFLARE_R2_URL').$image_s3_status['filename'];
                    $ig->update(['thumbnail_url'=> $r2_url]);
                    $this->info('[Success] Update. ' . $image_s3_status['message']);
                }
            } else {
                $ig->update(['thumbnail_url'=> $thumbnail_url]);
                $this->info('[Scheduler] Update Error. ' . $image_s3_status['message']);
                $error_count ++;
            }

            // 動画もS3に保存
            if (strpos($ig->image, '.mp4') !== false) {
                $s3_status = $this->_image_s3_upload($ig->image, $permalink, $s3_client, $ig->ig_id, $fv, $at, $context);
                if ($s3_status['status']) {

                    // 再生成したかどうかで分岐
                    if($s3_status['is_reCreate_result']){
                        $s3_url = $s3_status['tmp'];
                        $ig->update(['thumbnail_url'=> $s3_url]);
                        $this->info('[Success] Update ReCreate. ' . $s3_status['message']);
                    }else{
                        $s3_url = $s3_status['result']['ObjectURL'];
                        $ig->update(['thumbnail_url'=> $s3_url]);
                        $this->info('[Success] Update mp4. ' . $s3_status['message']);
                    }
                } else {
                    $this->info('[Scheduler] Update mp4 Error. ' . $s3_status['message']);
                    $error_count ++;
                }
            }
        }

        $this->info('[Scheduler] update thumnail count: ' . $instagrams->count());
        $this->info('[Scheduler] error count: ' . $error_count);
    }

    public function _get_status($url) {
        if (!$url) {
            return false;
        }

        if(($fp=fopen($url,'r',false,stream_context_create(['http'=>['ignore_errors'=>true]])))!==false) {
            fclose($fp);
            return preg_match('#^HTTP/\d\.\d (\d+) .+$#',$http_response_header[0],$matches)===1?(int)$matches[1]:false;
        } else {
            return false;
        }
    }

    public function _refresh_token($email) {
        $user = User::where('email', $email)->first();
        $at = $user->company_tied->access_token;
        $client_secret = config('services.ig')['app_secret'];
        $client_id = config('services.ig')['app_id'];
        $access_token_url = "https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&set_token_expires_in_60_days=true&client_id={$client_id}&client_secret={$client_secret}&fb_exchange_token={$at}";
        $contents = file_get_contents($access_token_url);
        $result = json_decode($contents, true);

        // 長期アクセストークンをテーブルに格納
        $now = new Carbon('now');
        $company = $user->company_tied;
        $company->access_token = $result['access_token'];
        if (array_key_exists('expires_in', $result) === true) {
            $expires_in = $now->addSeconds($result['expires_in']);
            $company->access_token_expires_in = $expires_in;
        }
        $company->save();
    }

    public function _image_s3_upload($url, $permalink, $s3_client, $ig_id, $fv, $at, $context, $postfix='') {
        $ext = 'jpg';
        $is_reCreate_result = false;

        if (strpos($url, '.mp4') !== false) {
            $ext = 'mp4';
        }

        $filename = $ig_id.'.'.$ext;

        $local_filepath = storage_path() . "/app/public/tmp/{$ig_id}.{$ext}";
        $tmp = @file_get_contents($url);

        // すでに画像のURL有効期限切れの場合、permalinkから画像再生成する
        if(!$tmp){
            $new_url = "https://graph.facebook.com/v{$fv}/instagram_oembed?url={$permalink}&fields=thumbnail_url&access_token={$at}";
            $new_tmp = file_get_contents($new_url, false, $context);
            $new_tmp = mb_convert_encoding($new_tmp, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
            $json_decoded_new_tmp = json_decode($new_tmp, true);
            if(isset($json_decoded_new_tmp['error'])){
                return ['status' => false, 'message' => "{$permalink} is not exist"];
            }
            $new_thumbnail_url = str_replace('\\', '', $json_decoded_new_tmp);
            if(!isset($new_thumbnail_url['thumbnail_url'])){
                return ['status' => false, 'message' => "{$permalink} is not exist no thumbnail_url"];
            }
            $tmp = $new_thumbnail_url['thumbnail_url'];
            $url = $tmp;
            $is_reCreate_result = TRUE;
        }
        if (!$tmp){
            return ['status' => false, 'message' => "{$url} couldnt download"];
        }
        $fp = fopen($local_filepath, 'w');
        fwrite($fp, $tmp);
        fclose($fp);

        // S3にアップロード
        try {
            $result = $s3_client->putObject([
                'Bucket' => config('app.aws_r2_bucket'),
                'Key' => $ig_id . $postfix . '.' . $ext,
                'SourceFile' => $local_filepath,
            ]);
        } catch (\Exception $e) {
            return ['status' => false, 'message' => $e->getMessage()];
        }
        if($is_reCreate_result){
            return ['status' => true, 'result' => $result, 'message' => $url, 'is_reCreate_result' => $is_reCreate_result, 'tmp' => $tmp, 'filename' => $filename];
        }

        return ['status' => true, 'result' => $result, 'message' => $url, 'is_reCreate_result' => $is_reCreate_result, 'filename' => $filename];
    }
}
