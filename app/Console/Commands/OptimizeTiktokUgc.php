<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Aws\S3\S3Client;
use Aws;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Process\Process;
use App\Tiktok;
use App\TiktokGroupRelationship;

class OptimizeTiktokUgc extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ugc:optimize {setId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    protected $s3_client;
    protected $bucketName;

    protected $ffmpegPath = '/usr/bin/ffmpeg';

    public function __construct()
    {
        parent::__construct();

        $credentials = new Aws\Credentials\Credentials(
            config('app.aws_r2_access_key_id'),
            config('app.aws_r2_secret_access_key')
        );

        $endpoint = config('app.CLOUDFLARE_R2_ENDPOINT');

        $this->s3_client = new S3Client([
            'credentials' => $credentials,
            'endpoint' => $endpoint,
            'region' => 'auto',
            'version' => 'latest'
        ]);
        $this->bucketName = env('CLOUDFLARE_R2_BUCKET');
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $setId = $this->argument('setId');

        $this->info("UGC最適化処理を開始します。セットID: {$setId}");

        $TiktokGroupRelationshipData = TiktokGroupRelationship::where('tiktok_group_id', $setId)
            ->select('tiktok_id')
            ->get();

        if ($TiktokGroupRelationshipData->isEmpty()) {
            $this->error("UGC最適化処理の対象データが見つかりません。セットID: {$setId}");
            return 1; // エラー終了
        }else{
            foreach ($TiktokGroupRelationshipData as $i => $ugc) {
                $this->optimizeAndUpload($ugc);
            }
            $this->info('最適化処理件数: ' . ($i + 1));
        }

        $this->info('UGC最適化処理が完了しました。');
        return 0; // 正常終了
    }

    protected function optimizeAndUpload($ugc)
    {
        $tid = $ugc->tiktok_id;
        $tiktok_video_url = Tiktok::where('id', $tid)->select('video_url')->first();
        if (!$tiktok_video_url) {
            Log::warning("UGC最適化処理の対象データが見つかりません。UGC ID: {$ugc->id}");
        }

        // $r2ObjectKey = ltrim($parsedUrl['path'], '/');
        
        // $outputPath = storage_path('app/temp/' . pathinfo($localPath, PATHINFO_FILENAME) . '.webm');
        // $newExtension = strtolower(pathinfo($localPath, PATHINFO_EXTENSION));

        try {
            // 動画URLを取得し、R2からデータ情報を取得
            $remoteVideoUrl = $tiktok_video_url;
            $parsedUrl = parse_url($remoteVideoUrl);
            $videoUrlJson = $parsedUrl['path'];
            $videoUrlData = json_decode($videoUrlJson, true);
            if (isset($videoUrlData['video_url'])) {
                $videoUrl = $videoUrlData['video_url'];
            } else {
                Log::error("JSON データに video_url が含まれていません: {$videoUrlJson}");
                return null;
            }

            $parsedVideoUrl = parse_url($videoUrl);
            $r2ObjectKey = ltrim($parsedVideoUrl['path'], '/');
            $localPath = storage_path('app/temp/' . basename($r2ObjectKey));
            if (file_exists($localPath)) {
                unlink($localPath);
            }
            $outputPath = storage_path('app/temp/' . pathinfo($localPath, PATHINFO_FILENAME) . '_compressed.mp4');
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }

            $result = $this->s3_client->getObject([
                'Bucket' => $this->bucketName,
                'Key' => $r2ObjectKey,
            ]);
            Storage::put('temp/' . basename($r2ObjectKey), $result['Body']);

            $width = 720;
            $height = 1280;

            $ffmpegCommand = [
                $this->ffmpegPath,
                '-y',
                '-i', $localPath, // 入力ファイルは $localPath を使う
                '-c:v', 'libx264', // ★H.264コーデックを使用
                '-crf', '40', // ★CRF値を40に設定 (動画の品質とファイルサイズのバランス)
                '-preset', 'medium', // ★エンコード速度と圧縮率のバランス (例: 'medium', 'slow', 'veryfast')
                '-vf', "scale={$width}:{$height}", // ★解像度を720x1280に設定（変数を埋め込む）
                '-c:a', 'aac', // ★オーディオコーデックをAACに設定
                '-b:a', '128k', // ★オーディオビットレートを128kbpsに設定
                '-movflags', 'faststart', // ★Web最適化（メタデータを先頭に移動）
                $outputPath,
            ];

            $process = new Process($ffmpegCommand);
            $process->setTimeout(3600);
            $process->run();

            if ($process->isSuccessful()) {
                unlink($localPath);
                $localPath = $outputPath;
                $extension = 'mp4'; // 新しい拡張子を指定
            } else {
                Log::error("動画形式の変換に失敗しました: {$process->getErrorOutput()} (ファイル: {$localPath}, ID: {$ugc->id})");
                return;
            }

            Log::info("動画の圧縮に成功しました: {$outputPath}");
            $newR2ObjectKey = pathinfo($r2ObjectKey, PATHINFO_DIRNAME) . '/' . pathinfo($r2ObjectKey, PATHINFO_FILENAME) . '-converted.' . $extension;
            $newR2VideoUrl = env('CLOUDFLARE_R2_URL') . $newR2ObjectKey;

            $this->s3_client->putObject([
                'Bucket' => $this->bucketName,
                'Key' => $newR2ObjectKey,
                'Body' => fopen($localPath, 'r'),
                'ContentType' => 'video/' . $extension,
            ]);

            $tiktok = Tiktok::find($tid);
            $tiktok->video_url = $newR2VideoUrl;
            $tiktok->save();

            Log::info("UGCの video_url を更新しました (新しいURL: {$newR2VideoUrl})");

        } catch (\Aws\S3\Exception\S3Exception $e) {
            Log::error("R2処理中にエラーが発生しました: {$e->getMessage()} (URL: {$remoteVideoUrl}, ID: {$ugc->id})");
        } finally {
            if (file_exists($localPath)) {
                unlink($localPath);
            }
            if (file_exists($outputPath)) {
                unlink($outputPath);
            }
        }
    }
}
