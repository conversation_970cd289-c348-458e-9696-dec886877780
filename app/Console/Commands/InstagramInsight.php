<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Instagram;
use App\User;
use Carbon\Carbon;

// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan insta:insight >> storage/logs/crontab.log 2>&1
class InstagramInsight extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insta:insight';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get Instagram Insight';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $fv = config('app.facebook_version');
        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] UpdateImage start');
        $admin = '<EMAIL>'; // admin用のトークンを使用してAPIを使う

        $at = User::where('email', $admin)->first()->company_tied->access_token;
        $context = stream_context_create(['http' => ['ignore_errors' => true]]);
        $instagrams = Instagram::get();

        $url = "https://graph.facebook.com/v{$fv}/17861151281326575/insights?metric=engagement,impressions,reach,saved&access_token={$at}";
        $json = file_get_contents($url, false, $context);
        $json = mb_convert_encoding($json, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
        $result = json_decode($json, true);
        $this->info($json);

        if (array_key_exists('error', $result)) {
            $this->info('[Scheduler] No insight. ' . $result['error']['message']);
        }

        // engagement,impressions,reach,saved
        $insight = [];
        foreach($result['data'] as $d){
            $name = $d['name'];
            $instagram->$name = $d['values']['value'];
        }
        $instagram->save();

        return;

        foreach ($instagrams as $ig) {
            $code = $this->_get_status($ig->thumbnail_url);
            if ($code == '403' || !$code) {
                $this->info('[Scheduler] UpdateImage url:' . $ig->thumbnail_url . ' : ' . $code);

                // サムネイルを更新する
                $ig_url = $ig->permalink;
                if (!$ig_url) {
                    $this->info('[Scheduler] No permalink. Skipped');
                    continue;
                }

                $url = "https://graph.facebook.com/v{$fv}/instagram_oembed?url={$ig_url}&fields=thumbnail_url&access_token={$at}";
                $json = file_get_contents($url, false, $context);
                $json = mb_convert_encoding($json, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
                $result = json_decode($json, true);

                if (array_key_exists('error', $result)) {
                    $this->info('[Scheduler] No thumnail. ' . $result['error']['message']);
                    continue;
                }

                $thumbnail_url = $result['thumbnail_url'];
                $ig->update(['thumbnail_url'=> $thumbnail_url]);
            }
        }
    }

    public function _get_status($url) {
        if (!$url) {
            return false;
        }
        if(($fp=fopen($url,'r',false,stream_context_create(['http'=>['ignore_errors'=>true]])))!==false) {
            fclose($fp);
            return preg_match('#^HTTP/\d\.\d (\d+) .+$#',$http_response_header[0],$matches)===1?(int)$matches[1]:false;
        } else {
            return false;
        }
    }

}
