<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\library\LineSender;
use App\LineMember;
use App\LineSetting;
use App\LineAutoSetting;
use Carbon\Carbon;


// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan line:send >> storage/logs/crontab.log 2>&1
class LineSend extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'line:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Line auto send';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] LineSend start');

        $line_members = LineMember::where('follow_status', 'follow')->get();
        $target_company_ids = $line_members->pluck('company_id');
        $line_settings = LineSetting::whereIn('company_id', $target_company_ids)->get();
        $line_auto_settings = LineAutoSetting::whereIn('company_id', $target_company_ids)->get();

        foreach($line_members as $line_member){
            $line_setting = $line_settings->where('company_id', $line_member->company_id)->first();
            $line_auto_setting = $line_auto_settings->where('company_id', $line_member->company_id)->first();

            $datediff = (new Carbon($line_member->follow_datetime))->diffInDays(new Carbon());

            if ($line_member->line_send_count > 0) {
                // 既存フォローユーザーの「送信する」にチェックが入っていない場合送らない
                if (!$line_auto_setting->is_send_from_date_follow) { continue; }

                // 設定した日付以後になっていた場合のみ送信
                if ($datediff < $line_auto_setting->send_from_date_follow) { continue; }
            }

            if ($line_member->type == 'qr') {
                // 送信するにチェックが入ってない場合送らない
                if (!$line_auto_setting->is_send_from_date_qr) { continue; }

                // 設定した日付以後になっていた場合のみ送信
                if ($datediff < $line_auto_setting->send_from_date_qr) { continue; }
            }

            $res = LineSender::text_and_image([$line_member], $line_setting, $line_auto_setting);

            if (!$res['status']) {
                $this->info(date('Y-m-d H:i:s') . ' [Scheduler] LineSend error id:' . $line_member->id);
            }
        }

        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] LineSend end');
    }

}
