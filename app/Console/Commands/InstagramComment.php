<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Instagram\SDK\Instagram;



// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan ins:com >> storage/logs/crontab.log 2>&1
class InstagramComment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ins:com';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send instagram comment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

    }

}
