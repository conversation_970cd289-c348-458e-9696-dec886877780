<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Company;
use App\Site;
use App\User;
use App\Order;
use App\Review;
use App\MailLog;
use App\MailSetting;
use App\MailBlacklist;
use App\ProductBlacklist;
use App\Mail\MailHandler;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan mail:send >> storage/logs/crontab.log 2>&1
// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan mail:send --is-admin >> storage/logs/crontab.log 2>&1
class MailSend2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:send {--is-admin} {--is-multithread-mode}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send request review mail to pending order';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $start_time = microtime(true);
        $is_admin = $this->option("is-admin");
        $is_multithread_mode = $this->option("is-multithread-mode");
        $is_sendgrid = explode(',', env('SENDGRID_IDS', ''));
        $company_ids = (new User())->admins()->pluck('company_id');

        $orders = Order::select('orders.*')
                ->where('order_type', 'pending')
                ->join(env('DB_DATABASE2') . '.companies', 'orders.company_id', '=', env('DB_DATABASE2') . '.companies.id')
                ->whereNotIn(env('DB_DATABASE2') . '.companies.status_id', [2, 3])
                ->whereNotIn('orders.company_id', $is_sendgrid)
                ->orderBy('orders.id', 'asc');

        if ($is_admin) {
            $orders = $orders->whereIn('company_id', $company_ids);
        } else {
            $orders = $orders->whereNotIn('company_id', $company_ids);
        }

        $orders =  $orders->get();

        if($is_multithread_mode){
            $orders_count = $orders->count();
            $half_order = ceil($orders_count / 2);

            $orders_A = $orders->slice(0, $half_order);
            $orders_B = $orders->slice($half_order);

            $this->info("送信数は全:{$orders_count}件");
            $this->info("WorkerAの送信件数: " . $orders_A->count() . " orders");
            $this->info("WorkerBの送信件数: " . $orders_B->count() . " orders");

            $tasks = [
                'ses_worker_A' => $orders_A,
                'ses_worker_B' => $orders_B,
            ];

            $worker_pids = []; // **子プロセスの PID を保存する配列**
            $parent_pid = getmypid();
            $this->info(date('Y-m-d H:i:s') . " 親プロセス (PID: {$parent_pid}) がワーカーを起動");

            foreach ($tasks as $worker_name => $task_orders) {
                $pid = pcntl_fork();

                if ($pid === -1) {
                    // **フォーク失敗**
                    $this->info(date('Y-m-d H:i:s') ."{$worker_name} のプロセスフォークに失敗しました。");
                    exit(1);
                } elseif ($pid === 0) {
                    $start_time = microtime(true);
                    $this->info(date('Y-m-d H:i:s') . " [{$worker_name}] プロセス開始 (PID: " . getmypid() . ")");

                    DB::disconnect();
                    DB::reconnect();

                    $this->info(date('Y-m-d H:i:s') . " [{$worker_name}] 処理開始 - 件数: " . $task_orders->count() . " 件");
                    $processed_count = 0;

                    foreach ($task_orders as $order) {
                        $company_id = $order->company_id;

                        DB::beginTransaction();
                        try {
                            $this->_mail_send($order, $company_id);
                            DB::commit();
                            $processed_count++; // **処理成功件数カウント**
                        } catch (\Throwable $ex) {
                            DB::rollBack();
                            $this->info(date('Y-m-d H:i:s') . " [{$worker_name}] メール送信エラー: " . $ex->getMessage());
                        }
                    }

                    $end_time = microtime(true);
                    $elapsed_time = round($end_time - $start_time, 2);
                    $this->info(date('Y-m-d H:i:s') . " [{$worker_name}] 完了 (処理時間: {$elapsed_time} 秒, 処理件数: {$processed_count} 件)");
                    exit(0);
                }

                // **親プロセスで子プロセスの PID を保存**
                $worker_pids[$worker_name] = $pid;
            }

            // **親プロセスで子プロセスの終了を個別に待機**
            foreach ($worker_pids as $worker_name => $worker_pid) {
                $this->info("[{$worker_name}] プロセス (PID: {$worker_pid}) の終了を待機中");
                pcntl_waitpid($worker_pid, $status);
                $this->info(date('Y-m-d H:i:s') ."[{$worker_name}] プロセス (PID: {$worker_pid}) 完了");
            }

            $end_time = microtime(true);
            $total_seconds = round($end_time - $start_time, 2);
            $total_hours = round($total_seconds / 3600, 2);
            $this->info(date('Y-m-d H:i:s') . "全体の処理時間は {$total_seconds} 秒 (約 {$total_hours} 時間) でした。");
        } else {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] SendMail start');

            foreach ($orders as $o) {
                $company_id = $o->company_id;

                DB::transaction(function() use ($o, $company_id) {
                    try {
                        $this->_mail_send($o, $company_id);
                    } catch ( \Throwable $ex ) {
                        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] Error mail send skip:' . $ex->getMessage());
                    }
                });
            }
        }
    }

    public function _mail_send($o, $company_id)
    {

        // Site情報の取得
        $site = $this->measureExecutionTime(function() use ($company_id) {
            return Site::where('company_id', $company_id)->first();
        }, 'Siteクエリ');

        // MailSettingの取得
        $mail_setting = $this->measureExecutionTime(function() use ($company_id) {
            return (new MailSetting())->find_or_default($company_id);
        }, 'MailSetting取得');

        // ウェブサイト設定が存在しない場合
        if (!$site) {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] No website Setting company_id:' . $company_id);
        }

        // 設定した日時ではない場合
        if ($mail_setting->mail_send_time != (new Carbon())->hour) {
            return;
        }

        // ブラックリストメールチェック
        $blacklist_count = $this->measureExecutionTime(function() use ($company_id, $o) {
            return MailBlacklist::where('company_id', $company_id)
                        ->where('email', $o->email)
                        ->count();
        }, 'MailBlacklistカウント');
    
        if ($blacklist_count > 0) {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] Black list mail:' . $o->id);
            return;
        }
    
        // 注文日から経過日数チェック
        $datediff = (new Carbon($o->order_date))->diffInDays(new Carbon());
        if ($datediff < $mail_setting->review_date_from_order) {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] Not over review_date_from_order order_id:' . $o->id);
            return;
        }
    
        // メール処理制限時間のチェック
        if ($mail_setting->mail_send_limit_time <= (new Carbon($o->order_date))->hour && $datediff == $mail_setting->review_date_from_order) {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] Not over mail_send_limit_time order_id:' . $o->id);
            return;
        }
    
        // このユーザーに送信した最終日時を取得
        $last_send_date = $this->measureExecutionTime(function() use ($o) {
            return MailLog::where('email', $o->email)->max('created_at');
        }, 'MailLog最終送信日取得');
    
        $datediff = (new Carbon($last_send_date))->diffInDays(new Carbon());
        if ($last_send_date && $datediff < $mail_setting->mail_between_date_count) {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] Not over mail_between_date_count order_id:' . $o->id);
            return;
        }
    
        // 注文内の商品の取得とブラックリスト商品除外
        if ($o->products->count() >= 1) {
            $pb = $this->measureExecutionTime(function() use ($company_id) {
                return ProductBlacklist::where('company_id', $company_id)->pluck('product_id');
            }, 'ProductBlacklist取得');

            if ($pb->count() >= 1) {
                $query = $o->products->whereNotIn('id', $pb->toArray());
            } else {
                $query = $o->products;
            }
    
            if ($mail_setting->priority == 'review_count_row') {
                $sorted = $query->sortBy(function($p) {
                    return $p->reviews->count();
                });
            } else if ($mail_setting->priority == 'price_high') {
                $sorted = $query->sortByDesc(function($p) {
                    return $p->price;
                });
            }
            $products = $sorted->take($mail_setting->review_count);
        } else {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] No product order_id:' . $o->id);
            return;
        }
    
        // メール送信準備
        $mh = new MailHandler($company_id);
        if (mt_rand(1, 100) <= $mail_setting->review_ratio) {
            $mail_type = 'product_review';
        } else {
            $mail_type = 'site_review';
        }
    
        if (count($products) == 0) {
            $this->info(date('Y-m-d H:i:s') . ' [Scheduler] No target product order_id:' . $o->id);
            return;
        }
    
        // 各商品に対してメール送信処理
        foreach ($products as $product) {
            $mail_logs_count = $this->measureExecutionTime(function() use ($o, $product) {
                return MailLog::where('email', $o->email)
                            ->where('order_id', $o->id)
                            ->where('product_id', $product->id)
                            ->count();
            }, 'MailLogs 件数');
    
            if ($mail_logs_count == 0) {
                $last_reminder_count = -1;
            } else {
                $last_reminder_count = $this->measureExecutionTime(function() use ($o, $product) {
                    return MailLog::where('email', $o->email)
                                ->where('order_id', $o->id)
                                ->where('product_id', $product->id)
                                ->max('remind_count');
                }, 'MailLogs max(remind_count)');
            }
    
            if ($last_reminder_count >= $mail_setting->reminder_count) {
                $this->info(date('Y-m-d H:i:s') . " [Scheduler] Over reminder count for order_id: {$o->id}, product_id: {$product->id}");
                continue;
            }
    
            $first_mail_sent = $this->measureExecutionTime(function() use ($o) {
                return MailLog::where('order_id', $o->id)
                            ->where('remind_count', 0)
                            ->max('created_at');
            }, '最初のMailLog取得');
    
            if ($first_mail_sent) {
                if ($mail_type == 'product_review') {
                    $reviewed = $this->measureExecutionTime(function() use ($site, $o, $product, $first_mail_sent) {
                        return Review::where('site_id', $site->id)
                                    ->where('email_of_reviewer', $o->email)
                                    ->where('product_id', $product->id)
                                    ->where('created_at', '>=', $first_mail_sent)
                                    ->get();
                    }, 'product_review: Review取得');
                } else if ($mail_type == 'site_review') {
                    $reviewed = $this->measureExecutionTime(function() use ($site, $o, $first_mail_sent) {
                        return Review::where('site_id', $site->id)
                                    ->where('email_of_reviewer', $o->email)
                                    ->where('created_at', '>=', $first_mail_sent)
                                    ->get();
                    }, 'site_review: Review取得');
                }

                if ($reviewed->count() >= 1) {
                    $this->info(date('Y-m-d H:i:s') . " [Scheduler] Review already exists for order_id: {$o->id}, site_id: {$site->id}, email: {$o->email}");
                    $o->order_type = 'completed';
                    $o->save();
                    continue;
                }
            }

            $send_reminder_count = $last_reminder_count + 1;

            $mail_server = 'ses';

            // メール送信処理
            if ($mail_type == 'product_review') {
                $mh->product_review($product, $o, $send_reminder_count, $mail_server);
            } else if ($mail_type == 'site_review') {
                $mh->site_review($o, $send_reminder_count, $mail_server);
            }

            if ($send_reminder_count >= $mail_setting->reminder_count) {
                $this->info(date('Y-m-d H:i:s') . " [Scheduler] Over reminder count now for order_id: {$o->id}, product_id: {$product->id}");
                $o->order_type = 'completed';
                $o->save();
            }

            // MailLog への記録
            $ml = new MailLog();
            $ml->order_id = $o->id;
            $ml->product_id = $product->id;
            $ml->email = $o->email;
            $ml->mail_type = $mail_type;
            $ml->remind_count = $send_reminder_count;
            $ml->save();
        }
    }

    /**
     * 実行時間を計測し、開始・完了のタイミングと所要時間をログ出力するヘルパーメソッド
     *
     * @param callable $callback    実行する処理
     * @param string   $description 処理の説明
     * @return mixed 処理結果
     */
    private function measureExecutionTime(callable $callback, string $description)
    {
        // $this->info("SES_開始: {$description}");
        $start = microtime(true);
        $result = $callback();
        $duration = microtime(true) - $start;
        $formattedDuration = number_format($duration, 4);
        // $this->info("SES_完了: {$description} - 所要時間: {$formattedDuration} 秒");
        return $result;
    }


}
