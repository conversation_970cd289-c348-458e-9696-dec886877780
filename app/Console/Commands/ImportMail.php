<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Aws\S3\S3Client;
use Aws;
use App\Company;
use App\Product;
use App\Order;
use App\OrderProduct;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ImportMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mail:import';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import mail to S3 for create order';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] MailImport start');

        // S3のファイル一覧を取得
        $files = Storage::disk('s3_mail')->listContents('order_mails/', true);

        $is_error = false;

        DB::transaction(function () use ($files,$is_error) {
            try {
                foreach ($files as $i => $file) {
                    $company_uid = explode("-",$file['filename'])[1];

                    if ($file['type'] === 'file') {
                        $cart_system = explode("-",$file['filename'])[0];

                        if($cart_system == 'subscriptionstore') {
                            // カートシステム：サブスクストアの場合の処理
                            $result = $this->analysis_mail_subscriptionstore($file,$company_uid,$is_error);
                        }elseif($cart_system == 'tamago') {
                            // カートシステム：たまごリピートの場合の処理
                            $result = $this->analysis_mail_tamago($file,$company_uid,$is_error);
                        }

                        if($result){
                            throw new \Exception('ImportError: '.$file['basename']);
                        }

                        // 処理が成功したらファイルを削除
                        Storage::disk('s3_mail')->delete($file['path']);
                    }
                }
            } catch (\Exception $e) {
                $this->error($e->getMessage());
            }
        });

        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] MailImport End');
    }

    /**
     * サブスクストアのメール解析
     * @param $file
     */
    public function analysis_mail_subscriptionstore($file,$company_uid) {

        // 会社IDを取得
        $company_id = Company::where('uid', $company_uid)->first()->id;

        // メールの内容を行に分割
        $lines = $this->create_lines_from_mail_contents($file);

        $extracted_data = [];
        foreach ($lines as $n => $line) {

            // 「 様」の行を取得し、前の文字列を名前として取得する
            if(strpos($line,' 様') !== false){
                $extracted_data['name'] = str_replace(' 様', '', $line); 
            }

            // 「品番 : 」の行を取得し、後ろの文字列を品番として取得する
            if(strpos($line,'品番') !== false){
                $extracted_data['product_ids'][] = str_replace('品番 : ', '', $line); 
            }

            // 「メールアドレス : 」の行を取得し、後ろの文字列をメールアドレスとして取得する
            if(strpos($line,'メールアドレス') !== false){
                $extracted_data['email'] = str_replace('メールアドレス : ', '', $line); 
            }

            // 「電話番号 : 」の行を取得し、後ろの文字列を電話番号として取得する
            if(strpos($line,'電話番号') !== false){
                $extracted_data['tel'] = str_replace('電話番号 : ', '', $line); 
            }

            // 「注文日時 : 」の行を取得し、後ろの文字列を注文日時として取得する
            if(strpos($line,'注文日 : ') !== false){
                $extracted_data['order_date'] = str_replace('注文日 : ', '', $line); 
            }
        }

        // display_order_idを設定
        $extracted_data['display_order_id'] = Str::random(10);

        // 電話番号の取得ができなかった場合、デフォルトの電話番号を設定する
        if(empty($extracted_data['tel'])){
            $extracted_data['tel'] = "00012345678";
        }

        // nicknameは固定で設定
        $extracted_data['nickname'] = "ニックネーム";

        // currencyは固定で設定
        $extracted_data['currency'] = "JPY";

        // 必須項目のチェック
        $requiredFields = ['name', 'product_ids', 'email', 'tel', 'order_date'];
        foreach ($requiredFields as $field) {
            if (empty($extracted_data[$field])) {
                $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] :'.$field. ' is required :' . $file['basename']);
                $this->put_s3_failed_mail($file);
                $is_error = true;
                return $is_error;
            }
        }

        // 商品IDを取得
        if(!empty($extracted_data['product_ids'])){
            $pids = $this->get_product_info($extracted_data['product_ids'],$company_id);
        }else{
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] no exist extracted product ids :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }

        // 取得したIDに紐づく商品が取得できない場合エラー
        if ($pids[0] == []){
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] no product :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }

        // 取得したIDと抽出したIDの数が一致しない場合エラー
        if(count($pids[0]) != count($extracted_data['product_ids'])){
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] not match product count :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }

        $this->create_order_orderProduct($company_id, $extracted_data, $pids);

    }

    /**
     * たまごリピートのメール解析
     * @param $file
     */
    public function analysis_mail_tamago($file,$company_uid,$is_error){

        // 会社IDを取得
        $company_id = Company::where('uid', $company_uid)->first()->id;

        if(!$company_id){
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] no exist company_id :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }
        
        // メールの内容を行に分割
        $lines = $this->create_lines_from_mail_contents($file);

        $extracted_data = [];
        $tel_flag = false;
        foreach ($lines as $n => $line) {
            if(strpos($line,'注文者') !== false){
                $tel_flag = true; 
            }

            // 「 様」の行を取得し、前の文字列を名前として取得する
            if(strpos($line,' 様') !== false && empty($extracted_data['name'])){
                $extracted_data['name'] = str_replace(' 様', '', $line); 
            }

            // 「品番   ： 」の行を取得し、後ろの文字列を品番として取得する
            if(strpos($line,'品番') !== false){
                $extracted_data['product_ids'][] = trim(str_replace('品番   ： ', '', $line)); 
            }

            // 「メールアドレス : 」の行を取得し、後ろの文字列をメールアドレスとして取得する
            if(strpos($line,'メールアドレス') !== false){
                $extracted_data['email'] = trim(str_replace('メールアドレス： ', '', $line)); 
            }

            // 「注文者」というワード確認後に1つめの「お電話番号   ： 」の行を取得し、後ろの文字列を電話番号として取得する
            if(strpos($line,'電話番号') !== false && $tel_flag == true && empty($extracted_data['tel'])){
                $extracted_data['tel'] = trim(str_replace('お電話番号   ： ', '', $line)); 
            }

            // 「注文日時 : 」の行を取得し、後ろの文字列を注文日時として取得する
            if(strpos($line,'注文日時') !== false){
                $extracted_data['order_date'] = trim(str_replace('注文日時 ： ', '', $line)); 
            }
        }

        // display_order_idを設定
        $extracted_data['display_order_id'] = Str::random(10);

        // 電話番号の取得ができなかった場合、デフォルトの電話番号を設定する
        if(empty($extracted_data['tel'])){
            $extracted_data['tel'] = "00012345678";
        }

        // nicknameは固定で設定
        $extracted_data['nickname'] = "ニックネーム";

        // currencyは固定で設定
        $extracted_data['currency'] = "JPY";

        // 必須項目のチェック
        $requiredFields = ['name', 'product_ids', 'email', 'tel', 'order_date'];
        foreach ($requiredFields as $field) {
            if (empty($extracted_data[$field])) {
                $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] :'.$field. ' is required :' . $file['basename']);
                $this->put_s3_failed_mail($file);
                $is_error = true;
                return $is_error;
            }
        }

        // 商品IDを取得
        if(!empty($extracted_data['product_ids'])){
            $pids = $this->get_product_info($extracted_data['product_ids'],$company_id);
        }else{
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] no exist extracted product ids :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }

        // 取得したIDに紐づく商品が取得できない場合エラー
        if ($pids[0] == []){
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] no product :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }

        // 取得したIDと抽出したIDの数が一致しない場合エラー
        if(count($pids[0]) != count($extracted_data['product_ids'])){
            $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] not match product count :' . $file['basename']);
            $this->put_s3_failed_mail($file);
            $is_error = true;
            return $is_error;
        }

        $this->create_order_orderProduct($company_id, $extracted_data, $pids);
    }


    /**
     * 注文情報を登録
     * @param $company_id
     * @param $extracted_data
     * @param $pids
     */
    public function create_order_orderProduct($company_id, $extracted_data, $pids){
        try{
            DB::transaction(function() use ($company_id, $extracted_data, $pids) {

                // DBに登録されている中にあるかどうかチェック
                if (Order::where(['display_order_id'=> $extracted_data['display_order_id'], 'company_id' => $company_id])->count() > 0) {
                    $this->info(date('Y-m-d H:i:s') . ' [FailedImportMail] already exist order :' . $extracted_data['display_order_id']);
                    $is_error = true;
                    return;
                }
    
                $insert = [];
    
                $insert['name'] = $extracted_data['name'];
                $insert['nickname'] = $extracted_data['nickname'];
                $insert['currency'] = $extracted_data['currency'];
                $insert['email'] = $extracted_data['email'];
                $insert['phone'] = $extracted_data['tel'];
                $insert['order_date'] = $extracted_data['order_date'];
                $insert['display_order_id'] = $extracted_data['display_order_id'];
                $insert['company_id'] = $company_id;
                $insert['order_type'] = 'pending';
                $insert['created_at'] = date("Y-m-d H:i:s");
                $insert['updated_at'] = date("Y-m-d H:i:s");
    
                $inserts[] = $insert;
    
                // バルクインサート
                DB::table('orders')->insert($inserts);
                $new_order = Order::where('company_id', $company_id)->orderBy('id', 'desc')->first();
    
                // 商品紐づきの登録
                $orders = Order::where('company_id', $company_id)->get();
                $ods = [];
                foreach($orders as $o){
                    $ods[$o->display_order_id] = $o->id;
                }
    
                $inserts2 = [];
                foreach($pids[0] as $pid){
                    
                    // もしすでに存在していたらスキップ
                    if (OrderProduct::where(['company_id' => $company_id, 'order_id' => $new_order->id, 'product_id' => $pid['id']])->count() > 0) {
                        continue;
                    }
    
                    $inserts2[] = [
                        'company_id' => $company_id,
                        'order_id' => $new_order->id,
                        'product_id' => $pid['id'],
                        'created_at' => date("Y-m-d H:i:s"),
                        'updated_at' => date("Y-m-d H:i:s")
                    ];
                }
                DB::table('order_products')->insert($inserts2);
    
            });
        }catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * メールの内容を行に分割
     * @param $file
     * @return array
     */
    public function create_lines_from_mail_contents($file){
        $contents = Storage::disk('s3_mail')->get($file['path']);
        $decode_contents = json_decode($contents, true);
        $lines = preg_split("/[\r\n;]/", $decode_contents['body_text']);

        return $lines;
    }

    /**
     * 商品IDを取得
     * @param $product_ids
     * @param $company_id
     * @return array
     */
    public function get_product_info($product_ids, $company_id) {
        $pids[] = Product::whereIn('product_display_id', $product_ids)->where('company_id',$company_id)->select('id')->get()->toArray();
        return $pids;
    }


    /**
     * S3に失敗したメールを移動
     * @param $file
     */
    public function put_s3_failed_mail($file) {
        $source_path = $file['path'];
        $destination_path = 'failed_order_mails/' . $file['basename'];
        $contents = Storage::disk('s3_mail')->get($source_path);
        Storage::disk('s3_failed')->put($destination_path, $contents);
    }
}
