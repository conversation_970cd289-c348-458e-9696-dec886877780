<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Instagram;
use App\User;
use Carbon\Carbon;
use Aws\S3\S3Client;

// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan delete:ugc >> storage/logs/crontab.log 2>&1
class DeleteUgc extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:ugc';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete UGC if original instagram post is deleted';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $fv = config('app.facebook_version');
        $this->info(date('Y-m-d H:i:s') . ' [Scheduler] DeleteUgc start');
        $admin = '<EMAIL>'; // admin用のトークンを使用してAPIを使う

        $at = User::where('email', $admin)->first()->company_tied->access_token;
        $context = stream_context_create(['http' => ['ignore_errors' => true]]);
        $instagrams = Instagram::get();

        foreach ($instagrams as $ig) {
            // 投稿が存在するかどうかのチェック
            $ig_url = $ig->permalink;
            $url = "https://graph.facebook.com/v{$fv}/instagram_oembed?url={$ig_url}&fields=thumbnail_url&access_token={$at}";
            $json = file_get_contents($url, false, $context);
            $json = mb_convert_encoding($json, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
            $result = json_decode($json, true);

            if (array_key_exists('error', $result)) {
                $this->info('[Scheduler] No thumnail. ' . $ig->ig_id . ': ' . $result['error']['message']);
                $ig->delete();
            }

            // 0.5s待つ
            usleep(500000);
        }
    }

}
