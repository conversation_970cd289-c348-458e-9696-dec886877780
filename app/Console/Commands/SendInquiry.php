<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Inquiry;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

// * * * * * cd /home/<USER>/adtech_ugc/ && php artisan inquiry:send >> storage/logs/crontab.log 2>&1
class SendInquiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inquiry:send';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send mail inquiry';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $targets = Inquiry::where('send_email', 0)->orWhereNull('send_email')->get();
        
        foreach($targets as $t){
            $subject = "お問い合わせがありました。";
            $content = "";
            $content .= "お問い合わせがありました。\r\n";
            $content .= "=================================\r\n";
            $content .= "会社名          " . $t->company_name."\r\n";
            $content .= "ご担当者名      " . $t->person_in_charge."\r\n";
            $content .= "役職            " . $t->position."\r\n";
            $content .= "メールアドレス   " . $t->email."\r\n";
            $content .= "電話番号　 　　  " . $t->phone_code."\r\n";
            $content .= "企業URL　 　　  " . $t->url."\r\n";
            $content .= "利用形態　 　　  " . $t->usage."\r\n";
            $content .= "お問い合わせ内容 " . $t->content."\r\n";;
            $content .= "お問い合わせ日時 " . strftime("%Y年%m月%d日 %H時%M分%S秒", $t->created_at->timestamp)."\r\n";
            $content .= "================================="."\r\n";

            Mail::raw($content, function($message) use ($subject) {
                $message->to('<EMAIL>')->subject($subject);
            });

            $t->send_email = 1;
            $t->save();
        }
    }

}
