<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use App\MailTemplate;
use Mehedi\AmpMail\Mimes\Amp;
use App\Review;

class ReviewMail extends Mailable
{
    use Queueable, SerializesModels, Amp;

    public $replaced_html;
    public $replaced_html_amp;
    public $replaced_text;
    public $header;
    public $footer;
    public $escape_link;
    public $dynamic_css;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    // 必須：mailtype, mail_template, mail_setting
    // new ReviewMail(['mailtype' => '...', ...])のように連想配列の形を引数にとる
    public function __construct($args)
    {
        foreach($args as $key => $value){
            $this->$key = $value;
        }

        $this->email = $this->email ??
            (isset($this->order) ? $this->order->email : null) ??
            (isset($this->review) ? $this->review->email_of_reviewer : null);

        // HTMLメールの上部にある「正しく表示されない方はこちら」のリンク先
        if ($this->mailtype == 'product_review') {
            $this->escape_link = $this->product->get_edit_review_link_for_test_mail($this->order);
        } else if ($this->mailtype == 'site_review') {
            $this->escape_link = $this->site->get_new_review_link($this->email);
        } else if ($this->mailtype == 'comment_notify') {
            $this->escape_link = $this->review->show_link();
        } else if ($this->mailtype == 'qa_notify') {
            $this->escape_link = '';
        } else if ($this->mailtype == 'address_confirm') {
            $this->escape_link = $this->review->verify_link();
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if (!$this->email) {Log::info('no email');return;}
        if (!$this->mailtype) {Log::info('no mailtype');return;}
        if (!$this->mail_template) {Log::info('no mail_template');return;}
        if (!$this->mail_setting) {Log::info('no mail_setting');return;}
        if (!$this->site) {Log::info('no site');return;}

        $mailtype = $this->mailtype;

        $postfix = (isset($this->reminder_count) && $this->reminder_count >= 1) ? '_r' . $this->reminder_count : '';
        $htmlmail_column = $mailtype . $postfix;
        $subject_column = $mailtype . '_title' . $postfix;
        $textmail_column = $mailtype . '_textmail' . $postfix;

        $replaced_subject = $this->_replace_special($this->mail_template->$subject_column, false);
        $this->replaced_html = $this->_replace_special($this->mail_template->$htmlmail_column, false);
        $this->replaced_text = $this->_replace_special($this->mail_template->$textmail_column, false);

        $this->header = $this->mail_template->header;
        $this->footer = $this->mail_template->footer;

        $order_id = isset($this->order) ? $this->order->id : 0;
        $company_id = $this->site->company_id;
        $product_id = isset($this->product) ? $this->product->id : 0;
        $reminder_count = isset($this->reminder_count) ? $this->reminder_count : 0;

        $mail = $this->to($this->email)
            ->from($this->mail_setting->from_address, $this->mail_setting->from_name)
            ->replyTo($this->mail_setting->reply_address)
            ->subject($replaced_subject);

        // AMPメール送信
        if ($this->mail_setting->is_dynamic && ($mailtype == 'product_review' || $mailtype == 'site_review')) {
            $this->replaced_html_amp = $this->_replace_special($this->mail_template->$htmlmail_column, true);
            $this->dynamic_css = $this->mail_template->stylesheet;
            $mail = $mail->amp('mail.common_dynamic');
        }

        $mail = $mail->view('mail.common')
        ->text('mail.common_text');

        if ($mailtype == 'product_review' || $mailtype == 'site_review') {
            if($this->mail_server == 'sendgrid') {
                // 開封などの指標をSendgrid経由で取得する
                $mail = $mail->withSwiftMessage(function ($message) use ($order_id, $company_id, $product_id, $reminder_count) {
                    $headers = $message->getHeaders();
                    $headers->addTextHeader('X-SMTPAPI', json_encode([
                        'unique_args' => [
                            'order_id' => $order_id,
                            'product_id' => $product_id,
                            'company_id' => $company_id,
                            'reminder_count' => $reminder_count
                        ]
                    ]));
                });
            }elseif($this->mail_server == 'ses') {
                // 開封などの指標をSES経由で取得する
                $mail = $mail->withSwiftMessage(function ($message) use ($order_id, $company_id, $product_id, $reminder_count) {
                    $headers = $message->getHeaders();
                    $headers->addTextHeader('X-SES-CONFIGURATION-SET', 'ugcc-ses-log');
                    $headers->addTextHeader('X-SES-MESSAGE-TAGS', 'order_id=' . $order_id . ',company_id=' . $company_id . ',product_id=' . $product_id . ',reminder_count=' . $reminder_count);
                });
            }
        }

        return $mail;
    }

    // マーカーをパラメータで置換する
    public function _replace_special($text, $is_dynamic)
    {
        $hash = [
            '{% store_logo %}' => isset($this->site) ? $this->site->logo($is_dynamic) : null,
            '{% store_name %}' => isset($this->site) ? $this->site->name : null,
            '{% store_url %}' => isset($this->site) ? $this->site->url : null,
            '{% customer_name %}' => $this->_customer_name(),
            '{% product_name %}' => isset($this->product) ? $this->product->name : null,
            '{% product_image %}' => $this->_product_image($is_dynamic),
            '{% product_url %}' => isset($this->product) ? $this->product->product_url : null,
            '{% product_stars %}' => $this->_product_star(),
            '{% review_form %}' => $this->_review_form(),
            '{% review_link %}' => isset($this->product) && isset($this->order) ? $this->product->get_edit_review_link_for_test_mail($this->order) : null,
            '{% review_text %}' => isset($this->review) ? $this->review->description_of_review : null,
            '{% review_title %}' => isset($this->review) ? $this->review->title_of_review : null,
            '{% review_verify_link %}' => isset($this->review) ? $this->review->verify_link() : null,
            '{% review_date %}' => isset($this->review) ? $this->review->review_date_time : null,
            '{% review_reply %}' => isset($this->review) ? $this->review->description_of_reply : null,
            '{% site_review_link %}' => $this->site->get_new_review_link($this->email),
            '{% site_review_form %}' => $this->_review_form(true),
            '{% star_rating %}' => null,
            '{% questioner_name %}' => null,
            '{% question %}' => null,
            '{% admin_answer %}' => null,
            '{% question_link %}' => null,
        ];
        return str_replace(
            array_keys($hash),
            array_values($hash),
            $text
        );
    }

    public function _customer_name()
    {
        if ($this->mail_setting->is_honorific) {
            $postfix = $this->mail_setting->honorific_name;
        } else {
            $postfix = '';
        }

        $name = (isset($this->order) ? $this->order->name : null) ?? (isset($this->review) ? $this->review->name_of_reviewer : null);
        return $name . $postfix;
    }

    public function _product_image($is_dynamic)
    {
        if (empty($this->product)) { return null; }

        if ($is_dynamic) {
            return '<div class="fixedContainer">
            <amp-img src="' . $this->product->image_url . '" class="productImageContain" layout="fill"></amp-img>
            </div>';
        } else {
            return '<img src="' . $this->product->image_url . '" style="width:100%;">';
        }
    }

    public function _product_star()
    {
        if (empty($this->product)) { return null; }

        return '星５つ中の' . $this->product->star_disp();
    }


    public function _review_form($is_site_review=false)
    {
        if ($is_site_review) {
            if ($this->mail_setting->is_dynamic) {
                $link = $this->site->get_create_review_link();
            } else {
                $link = $this->site->get_new_review_link($this->email);
            }
        } else {
            // productがセットされてない場合レビューフォームはメールに出力しない
            if (!isset($this->product) || !isset($this->order)) {
                return null;
            }
            if ($this->mail_setting->is_dynamic) {
                $link = $this->product->get_create_review_link();
            } else {
                $link = $this->product->get_edit_review_link_for_test_mail($this->order);
            }
        }

        if ($is_site_review) {
            $hidden = '<input type="hidden" name="site_id" value="' . $this->site->id . '">
            <input type="hidden" name="site_qid" value="' . $this->site->qid() . '">
            <input type="hidden" name="email" value="' . $this->email . '">';
        } else {
            $hidden = '<input type="hidden" name="order_id" value="' . $this->order->id . '">
            <input type="hidden" name="order_qid" value="' . $this->order->qid() . '">
            <input type="hidden" name="product_id" value="' . $this->product->id . '">';
        }
        $mail_setting = $this->mail_setting;
        if ($this->mail_setting->is_dynamic) {
            return view('mail/review_form_amp', compact('link', 'hidden', 'mail_setting'));
        } else {
            return view('mail/review_form', compact('link', 'hidden', 'mail_setting'));
        }

    }
}
