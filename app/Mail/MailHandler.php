<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\ReviewMail;
use App\MailSetting;
use App\MailTemplate;
use App\Site;
use App\Review;
use App\Order;


class MailHandler extends Mailable
{
    // 共通メール送信処理
    public function __construct($company_id, $is_sendgrid=false)
    {
        $this->config_mail_server($company_id,$is_sendgrid);
        $this->mail_setting = MailSetting::where('company_id', $company_id)->first();
        if (!$this->mail_setting){
            $this->mail_setting = new MailSetting();
            $this->mail_setting->set_default();
        }

        $this->mail_template = MailTemplate::where('company_id', $company_id)->first();
        if (!$this->mail_template){
            $this->mail_template = new MailTemplate();
            $this->mail_template->set_default();
        }

        $this->site = Site::where('company_id', $company_id)->first();
        if (!$this->site){
            $this->site = new Site();
        }
    }


    public function config_mail_server($company_id,$is_sendgrid)
    {
        if ($is_sendgrid) {
            config([
                'mail.driver' => env('MAIL_DRIVER_SENDGRID'),
                'mail.host' => env('MAIL_HOST_SENDGRID'),
                'mail.port' => env('MAIL_PORT_SENDGRID'),
                'mail.username' => env('MAIL_USERNAME_SENDGRID'),
                'mail.password' => env('MAIL_PASSWORD_SENDGRID'),
                'mail.encryption' => env('MAIL_ENCRYPTION_SENDGRID'),
                'mail.from.address' => env('MAIL_FROM_ADDRESS_SENDGRID'),
                'mail.from.name' => env('MAIL_FROM_NAME_SENDGRID'),
            ]);
        } else {
            config([
                'mail.driver' => env('MAIL_DRIVER'),
                'mail.host' => env('MAIL_HOST'),
                'mail.port' => env('MAIL_PORT'),
                'mail.encryption' => env('MAIL_ENCRYPTION'),
                'mail.from.address' => env('MAIL_FROM_ADDRESS'),
                'mail.from.name' => env('MAIL_FROM_NAME'),
                'services.ses.key' => env('SES_KEY'),
                'services.ses.secret' => env('SES_SECRET'),
                'services.ses.region' => env('SES_REGION'),
            ]);
        }
    }
    public function product_review($product, $order, $reminder_count, $mail_server, $email=null)
    {
        if($mail_server == 'ses'){
            Mail::send(
                new ReviewMail([
                    'mail_setting' => $this->mail_setting,
                    'mail_template' => $this->mail_template,
                    'site' => $this->site,
                    'product' => $product,
                    'order' => $order,
                    'mailtype' => 'product_review',
                    'email' => $email,
                    'reminder_count' => $reminder_count,
                    'mail_server' => $mail_server
                ])
            );
        }else{
            Mail::send(
                new ReviewMail([
                    'mail_setting' => $this->mail_setting,
                    'mail_template' => $this->mail_template,
                    'site' => $this->site,
                    'product' => $product,
                    'order' => $order,
                    'mailtype' => 'product_review',
                    'email' => $email,
                    'reminder_count' => $reminder_count,
                    'mail_server' => $mail_server
                ])
            );
        }
    }

    public function site_review($order, $reminder_count, $mail_server, $email=null)
    {
        if($mail_server == 'ses'){
            Mail::send(
                new ReviewMail([
                    'mail_setting' => $this->mail_setting,
                    'mail_template' => $this->mail_template,
                    'site' => $this->site,
                    'mailtype' => 'site_review',
                    'email' => $email,
                    'order' => $order,
                    'reminder_count' => $reminder_count,
                    'mail_server' => $mail_server
                ])

            );
        }else{
            Mail::send(
                new ReviewMail([
                    'mail_setting' => $this->mail_setting,
                    'mail_template' => $this->mail_template,
                    'site' => $this->site,
                    'mailtype' => 'site_review',
                    'email' => $email,
                    'order' => $order,
                    'reminder_count' => $reminder_count,
                    'mail_server' => $mail_server
                ])
            );
        }
    }

    public function comment_notify($review, $email=null)
    {
        Mail::send(
            new ReviewMail([
                'mail_setting' => $this->mail_setting,
                'mail_template' => $this->mail_template,
                'site' => $this->site,
                'review' => $review,
                'product' => $review->product,
                'mailtype' => 'comment_notify',
                'email' => $email
            ])
        );
    }

    public function qa_notify($order, $email)
    {
        Mail::send(
            new ReviewMail([
                'mail_setting' => $this->mail_setting,
                'mail_template' => $this->mail_template,
                'site' => $this->site,
                'order' => $order,
                'mailtype' => 'qa_notify',
                'email' => $email
            ])
        );
    }

    public function address_confirm($review, $email)
    {
        Mail::send(
            new ReviewMail([
                'mail_setting' => $this->mail_setting,
                'mail_template' => $this->mail_template,
                'site' => $this->site,
                'review' => $review,
                'mailtype' => 'address_confirm',
                'email' => $email
            ])
        );
        if (Mail::failures()) {
            return false;
        }
    }

}
