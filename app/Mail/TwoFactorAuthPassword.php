<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;

class TwoFactorAuthPassword extends Mailable
{
    use Queueable, SerializesModels;

        private $login_token;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($login_token)
    {
        $this->login_token = $login_token;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->from(config('mail.from.address'), config('mail.from.name'))
            ->subject('２段階認証のパスワード')
            ->text('mail.two_factor_auth.password')
            ->with('login_token', $this->login_token);
    }
}
