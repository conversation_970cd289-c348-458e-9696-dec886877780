#!/usr/bin/env python

import pandas as panda
import time
import os
from TikTokApi import TikTokApi

current_directory = os.path.dirname(__file__)
videos = []
data_csv = panda.read_csv(current_directory + '/../../public/keyword_search_video.csv')
video_id = data_csv.video_id[0]
with TikTokApi() as api:
    video = api.video(id = video_id)

    # video_data = video.bytes()
    # file_name = current_directory + "/../../public/storage/tmp/tiktok_video/" + str(video_id) + ".mp4"
    # with open(file_name, "wb") as out_file:
    #    out_file.write(video_data)

    # try:
    #     print(video.info())
    # except:
    #     errors = []
    #     errors.append({
    #         'error_code': 404
    #     })
    #     error_data = panda.DataFrame(errors)
    #     error_data.to_csv('C:/xampp/htdocs/ugc-creative/public/tiktok_video.csv')

    data = video.info_full()
    data_stats = data['itemInfo']['itemStruct']['stats']

    videos.append({
        'comment_count': data_stats['commentCount'],
        'digg_count': data_stats['diggCount'],
        'play_count': data_stats['playCount'],
        'share_count': data_stats['shareCount'],
        'content': data['itemInfo']['itemStruct']['desc'],
        'thumbnail_url': data['itemInfo']['itemStruct']['video']['zoomCover']['240'],
        'author_name': data['itemInfo']['itemStruct']['author']['nickname'],
        'image': data['itemInfo']['itemStruct']['video']['cover'],
        'video_url': data['itemInfo']['itemStruct']['video']['playAddr']
    })

    output_data = panda.DataFrame(videos)
    output_data.to_csv(current_directory + '/../../public/tiktok_video.csv')
