from tiktokapipy.api import TikTokAPI
import pandas as panda
import os

with TikTokAPI() as api:
  current_directory = os.path.dirname(__file__)
  videos = []
  data_csv = panda.read_csv(current_directory + '/../../public/keyword_search_video.csv')
  video_id = data_csv.video_id[0]

  video = api.video("https://m.tiktok.com/v/" + str(video_id))
  videos.append({
    'comment_count': video.stats.comment_count,
    'digg_count': video.stats.digg_count,
    'play_count': video.stats.play_count,
    'share_count': video.stats.share_count,
    'content': video.desc,
    'thumbnail_url': video.video.cover,
    'author_name': video.author.unique_id,
    'image': video.video.cover,
    'video_url': video.url
  })

  output_data = panda.DataFrame(videos)
  output_data.to_csv(current_directory + '/../../public/tiktok_video.csv')
