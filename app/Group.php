<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Group extends Model
{
    protected $fillable = ['name', 'company_id', 'qid', 'is_active', 'display_type',
        'is_display_comment', 'header_image', 'main_color', 'sd_short', 'sd_wide',
        'pc_short', 'pc_wide', 'more_text', 'modal_background_color', 'display_order', 'abtest_group_id', 'url',
        'is_auto_play_video', 'comment_text_color', 'is_display_date', 'border_style', 'border_color',
        'is_use_background_color', 'background_color', 'bottom_right_text', 'bottom_right_text_option',
        'is_display_report_mark', 'is_use_border', 'instagram_mark_style', 'bottom_text_align',
        'display_style', 'is_fixed_display', 'is_display_inside_chatbot'
    ];

    public function instagram_groups()
    {
        return $this->hasMany('App\InstagramGroup');
    }

    public function instagrams()
    {
        return $this->belongsToMany('App\Instagram', 'instagram_groups');
    }

    public function get_modal_background_class()
    {
        if ($this->display_type == 'lvuy') return 'lvuy-content';

        return $this->modal_background_color == 'black' ? 'ugc-black-grad' : 'ugc-white-grad';
    }

    public function get_bottom_right_text()
    {
        switch($this->bottom_right_text_option)
        {
            case 1: return '※薬事法を鑑みてレビュー内の一部表現については修正を行っております。';
            case 2: return '※お客様の感想であり、商品の効果・効能を表すものではございません。';
            case 3: return '※個人の感想です。';
            case 4: return $this->bottom_right_text;
            case 5: return '<div class="pr-block" style="text-align: ' . $this->bottom_text_align . ';">PR</div><div class="pr-text" style="text-align: ' . $this->bottom_text_align . ';">※Instagram投稿からお喜びの声を抜粋しております。</div><div class="pr-text" style="text-align: ' . $this->bottom_text_align . ';">※個人の感想であり、商品の効果・効能を表すものではございません。</div>';
            default: return '';
        }
    }

    public function get_play_btn_style()
    {
        if ($this->display_type != 'lvuy') return 'display: none;';

        return $this->is_auto_play_video ? 'display: none;' : '';
    }

    public function get_border_color()
    {
        if ($this->display_type == 'lvuy') {
            if ($this->is_use_border)
                return 'border: 1px solid '.$this->border_color;
            else
                return '';
        }

        return 'border: 1px solid rgb(221, 221, 221)';
    }

    public function get_logo_margin_top()
    {
        switch ($this->display_type) 
        {
            case 'lvuy': return  'margin-top: 40px;';
            default: return  'margin-top: 40px;';
        }
    }

    public function get_small_icon_size()
    {
        switch ($this->display_type) 
        {
            case 'lvuy': return 11;
            default: return 15;
        }
    }

    public function get_ig_from_group_id($id)
    {
        $myself = $this->find($id);

        if ($myself->display_order == 'ugc') {
            $result = InstagramGroup::select('*')
                ->leftJoin('groups', 'instagram_groups.group_id', '=', 'groups.id')
                ->leftJoin('instagrams', 'instagram_groups.instagram_id', '=', 'instagrams.id')
                ->where('group_id', $id)
                ->orderBy('instagrams.created_at', 'DESC')
                ->get();
        } else if ($myself->display_order == 'instagram') {
            $result = InstagramGroup::select('*')
                ->leftJoin('groups', 'instagram_groups.group_id', '=', 'groups.id')
                ->leftJoin('instagrams', 'instagram_groups.instagram_id', '=', 'instagrams.id')
                ->where('group_id', $id)
                ->orderBy('instagrams.time_stamp', 'DESC')
                ->get();
        } else {
            $record = InstagramGroup::where('group_id', $id)
                ->leftJoin('groups', 'instagram_groups.group_id', '=', 'groups.id')
                ->leftJoin('instagrams', 'instagram_groups.instagram_id', '=', 'instagrams.id')
                ->get();

            foreach ($record as $row) {
                $rec = GroupCustomOrder::where('group_id', $row->group_id)
                    ->where('instagram_id', $row->instagram_id);
                if ($rec->count() === 1) {
                    $row->setAttribute('sort_id', $rec->first()->sort_id);
                } else {
                    $row->setAttribute('sort_id', 0);
                }
            }
    
            $result = $record->sortBy('sort_id');
        }
        return $result;
    }

    public function set_default()
    {
        $this->is_active = 1;
        $this->display_type = 'slider';
        $this->is_display_comment = 1;
        $this->bottom_text_align = 'left';
        $this->is_display_report_mark = 1;
        $this->instagram_mark_style = 'default';
        $this->is_use_border = 1;
        $this->header_image = 'sample1';
        $this->main_color = '#000000';
        $this->sd_short = 1;
        $this->sd_wide = 2;
        $this->pc_short = 1;
        $this->pc_wide = 2;
        $this->more_text = 'もっと見る';
        $this->modal_background_color = 'white';
        $this->display_order = 'ugc';
        $this->is_display_inside_chatbot = 0;
    }

    public function header_url()
    {
        if (str_starts_with($this->header_image, 'sample')) {
            $i = str_replace('sample', '', $this->header_image);
            return request()->getSchemeAndHttpHost() . "/ugc/images/ugc_banner{$i}.png";
        } else {
            return request()->getSchemeAndHttpHost() . '/ugc/storage/header_images/' . $this->header_image;
        }
    }

    public function text_color()
    {
        if ($this->display_type != 'lvuy') 
            return $this->modal_background_color == 'black' ? 'white' : 'black';

        return $this->comment_text_color; 
    }

    public function get_main_color()
    {
        return $this->main_color;
    }

    public function get_grad_item_height()
    {
        if ($this->display_type != 'lvuy') return '';

        if ($this->display_style == 'square') {
            switch ($this->pc_wide)
            {
                case 1: return 'height: 7%';
                case 2: return 'height: 13%';
                case 3: return 'height: 16%';
                case 4: return 'height: 20%';
                case 5: return 'height: 17%';
                case 6: return 'height: 19%';
            }
        } else {
            switch ($this->pc_wide)
            {
                case 1: return 'height: 5%';
                case 2: return 'height: 9%';
                case 3: return 'height: 13%';
                case 4: return 'height: 16%';
                case 5: return 'height: 20%';
                case 6: return 'height: 19%';
            }
        }
    }

    public function get_icon_area_width()
    {
        if ($this->display_type != 'lvuy') return 'width: calc(100% - 20px)';
        if ($this->pc_wide == 6) return 'width: calc(100% - 10px)';
        return 'width: calc(100% - 20px)';
    }

    public function append_text_color()
    {
        return $this->modal_background_color == 'black' ? 'white' : 'gray';
    }

    public function background_color_rgba($opacity)
    {
        return $this->hex2rgba($this->background_color, $opacity);
    }

    public function hex2rgba($color, $opacity)
    {
 
        $default = 'rgb(0,0,0,0)';
     
        //Return default if no color provided
        if(empty($color))
              return $default; 
     
        //Sanitize $color if "#" is provided 
            if ($color[0] == '#' ) {
                $color = substr( $color, 1 );
            }
     
            //Check if color has 6 or 3 characters and get values
            if (strlen($color) == 6) {
                    $hex = array( $color[0] . $color[1], $color[2] . $color[3], $color[4] . $color[5] );
            } elseif ( strlen( $color ) == 3 ) {
                    $hex = array( $color[0] . $color[0], $color[1] . $color[1], $color[2] . $color[2] );
            } else {
                    return $default;
            }
     
            //Convert hexadec to rgb
            $rgb =  array_map('hexdec', $hex);
     
            //Check if opacity is set(rgba or rgb)
            $output = 'rgba('.implode(",",$rgb).','.$opacity.')';
     
            //Return rgb(a) color string
            return $output;
    }
}
