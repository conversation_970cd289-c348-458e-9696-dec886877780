<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class TiktokGroup extends Model
{
    protected $fillable = ['name', 'company_id', 'qid', 'is_active', 'display_type',
    'is_display_comment', 'header_image', 'main_color', 'border_color' ,'sd_short', 'sd_wide', 'pc_short', 'pc_wide',
    'more_text', 'modal_background_color', 'display_order', 'abtest_group_id', 'url', 'is_border_type','bottom_right_text_option', 'bottom_right_text',
    'bottom_text_align'];

    public function tiktok_group_relationship()
    {
        return $this->hasMany(TiktokGroupRelationship::class);
    }

    public function tiktoks()
    {
        return $this->belongsToMany(Tiktok::class, 'tiktok_group_relationship');
    }

    public function get_tiktoks_from_group_id($id)
    {
        $myself = $this->find($id);

        if ($myself->display_order == 'ugc') {
            $result = TiktokGroupRelationship::select('*')
                ->leftJoin('tiktok_groups', 'tiktok_group_relationship.tiktok_group_id', '=', 'tiktok_groups.id')
                ->leftJoin('tiktoks', 'tiktok_group_relationship.tiktok_id', '=', 'tiktoks.id')
                ->where('tiktok_group_id', $id)
                ->orderBy('tiktoks.created_at', 'DESC')
                ->get();
        } else if ($myself->display_order == 'tiktok') {
            $result = TiktokGroupRelationship::select('*')
                ->leftJoin('tiktok_groups', 'tiktok_group_relationship.tiktok_group_id', '=', 'tiktok_groups.id')
                ->leftJoin('tiktoks', 'tiktok_group_relationship.tiktok_id', '=', 'tiktoks.id')
                ->where('tiktok_group_id', $id)
                ->orderBy('tiktoks.time_stamp', 'DESC')
                ->get();
        } else {
            $record = TiktokGroupRelationship::where('tiktok_group_id', $id)
                ->leftJoin('tiktok_groups', 'tiktok_group_relationship.tiktok_group_id', '=', 'tiktok_groups.id')
                ->leftJoin('tiktoks', 'tiktok_group_relationship.tiktok_id', '=', 'tiktoks.id')
                ->get();

            foreach ($record as $row) {
                $rec = TiktokGroupCustomOrder::where('tiktok_group_id', $row->tiktok_group_id)
                    ->where('tiktok_id', $row->tiktok_id);
                if ($rec->count() === 1) {
                    $row->setAttribute('sort_id', $rec->first()->sort_id);
                } else {
                    $row->setAttribute('sort_id', 0);
                }
            }

            $result = $record->sortBy('sort_id');
        }
        return $result;
    }

    public function set_default()
    {
        $this->is_active = 1;
        $this->display_type = 'slider';
        $this->is_display_comment = 1;
        $this->header_image = 'sample';
        $this->main_color = '#000000';
        $this->border_color = '#dddddd';
        $this->sd_short = 1;
        $this->sd_wide = 2;
        $this->pc_short = 1;
        $this->pc_wide = 2;
        $this->more_text = 'もっと見る';
        $this->modal_background_color = 'white';
        $this->display_order = 'ugc';
        $this->is_border_type = 0;
        $this->bottom_text_align = 'left';
    }

    public function header_url()
    {
        if (str_starts_with($this->header_image, 'sample')) {
            $i = substr($this->header_image, -1);
            return request()->getSchemeAndHttpHost() . "/ugc/images/ugc_tiktok_banner{$i}.png";
        } else {
            return request()->getSchemeAndHttpHost() . '/ugc/storage/header_images/' . $this->header_image;
        }
    }

    public function text_color()
    {
        return $this->modal_background_color == 'black' ? 'white' : 'black';
    }

    public function append_text_color()
    {
        return $this->modal_background_color == 'black' ? 'white' : 'gray';
    }

    public function get_bottom_right_text()
    {
        switch($this->bottom_right_text_option)
        {
            case 1: return '※薬事法を鑑みてレビュー内の一部表現については修正を行っております。';
            case 2: return '※お客様の感想であり、商品の効果・効能を表すものではございません。';
            case 3: return '※個人の感想です。';
            case 4: return '<div class="pr-block" style="text-align: ' . $this->bottom_text_align . ';">PR</div><div class="pr-text" style="text-align: ' . $this->bottom_text_align . ';">※TikTok投稿からお喜びの声を抜粋しております。</div><div class="pr-text" style="text-align: ' . $this->bottom_text_align . ';">※個人の感想であり、商品の効果・効能を表すものではございません。</div>';
            case 5: return $this->bottom_right_text;
            default: return '';
        }
    }
}
