<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\MailTrackLog;
use App\LineMember;
use App\LineSetting;

class WebhookController extends Controller
{
    // Amazon SES -> Amazon SNS からメールの送信、開封、クリック毎にこちらのwebhookにデータがPOSTされます
    // https://ap-northeast-1.console.aws.amazon.com/sns/v3/home?region=ap-northeast-1#/topics
    public function mail (Request $request)
    {
        $body = $request->getContent();
        $json = mb_convert_encoding($body, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
        $res = json_decode($json, true);

        if ($res !== null && $res['Type'] == 'Notification') {
            $message = $res['Message'];
            $json = mb_convert_encoding($message, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
            $res = json_decode($json, true);
            $type = mb_strtolower($res['eventType']);

            $tags = $res['mail']['tags'];
            $company_id = $tags['company_id'][0];
            $order_id = $tags['order_id'][0];
            $product_id = $tags['product_id'][0];
            $reminder_count = isset($tags['reminder_count']) ? $tags['reminder_count'][0] : 0;

            MailTrackLog::firstOrCreate([
                'company_id' => $company_id,
                'order_id' => $order_id,
                'product_id' => $product_id,
                'type' => $type,
                'reminder_count' => $reminder_count,
            ]);
        } else if ($res !== null) {
            Log::info($json);
        }

        return 'OK';
    }

    public function line (Request $request)
    {
        $ls = LineSetting::where('access_secret', $request->channel_secret)->first();
        if (!$ls) {
            return 'NO Line Setting';
        }

        $httpClient = new \LINE\LINEBot\HTTPClient\CurlHTTPClient($ls->access_token);
        $bot = new \LINE\LINEBot($httpClient, ['channelSecret' => $ls->access_secret]);

        $body = $request->getContent();
        $json = mb_convert_encoding($body, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
        $res = json_decode($json, true);

        if (!array_key_exists(0, $res['events'])) {
            return 'test';
        }
        $event = $res['events'][0];

        if ($event['type'] == 'follow' || $event['type'] == 'unfollow') {
            $follow_datetime = date('Y-m-d H:i:s', floor($event['timestamp']/1000));
            $line_company_id = $event['source']['companyId'];
            $query = ['company_id' => $ls->company_id, 'site_id' => $ls->site_id, 'member_id' => $line_company_id];
        }

        if ($event['type'] == 'follow') {
            if (LineMember::where($query)->count() == 0){
                // company_idから表示名を取得する
                $response = $bot->getProfile($line_company_id);
                if ($response->isSucceeded()) {
                    $profile = $response->getJSONDecodedBody();
                    $name = $profile['displayName'];
                } else {
                    Log::error($response->getHTTPStatus());
                    Log::error($response->getJSONDecodedBody());
                    Log::error('[WebhookController@line] LINE表示名の取得に失敗しました');
                    return 'Cannot get displayName';
                }

                LineMember::create([
                    'company_id' => $ls->company_id,
                    'site_id' => $ls->site_id,
                    'name' => $name,
                    'follow_datetime' => $follow_datetime,
                    'type' => 'web',
                    'follow_status' => 'follow',
                    'member_id' => $line_company_id
                ]);
            } else {
                $lm = LineMember::where($query)->update(['follow_status' => 'follow']);
            }
        } else if ($event['type'] == 'unfollow') {
            LineMember::where($query)->update(['follow_status' => 'unfollow']);
        }

        return 'OK';
    }

}
