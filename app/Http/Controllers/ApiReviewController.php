<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;
use App\User;
use App\Site;
use App\Order;
use App\OrderProduct;
use App\Review;
use App\ReviewSetting;
use App\ReviewWidget;
use App\ReviewWidgetTag;
use App\ReviewWidgetReview;
use App\SiteReview;
use App\MailSetting;
use App\Mail\MailHandler;
use App\Company;
use App\ReviewWidgetInclude;
use App\ReviewWidgetExclude;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Response;

class ApiReviewController extends Controller
{

    public function _common_auth($company_id, $request) {
        $user = User::find($request->user_id);

        if (!$user) {
            return ['status' => false, 'msg' => 'Bad Authority(user)'];
        }

        if (!empty($request->product_display_id)) {
            $product = Product::where('company_id', $company_id)->where('product_display_id', $request->product_display_id)->first();
        } else {
            $product = new Product();
        }
        if (!$product) {
            return ['status' => false, 'msg' => 'Bad Authority(no product)'];
        }

        if (!empty($request->review_widget_id)) {
            $review_widget = ReviewWidget::where('company_id', $company_id)->where('id', $request->review_widget_id)->first();
        } else {
            $review_widget = (new ReviewWidget())->find_or_default($company_id);
        }
        if (!$review_widget) {
            return ['status' => false, 'msg' => 'Bad Authority(no review widget)'];
        }

        $site = Site::where('company_id', $company_id)->first();
        if (!$site) {
            return ['status' => false, 'msg' => 'Bad Authority(no site setting)'];
        }

        return ['status' => true, 'product' => $product, 'review_widget' => $review_widget];
    }

    // 他の人のコメントなどを取得し埋め込む内容を決める
    public function index (Request $request)
    {
        $res = $this->_get_review($request);
        if (!$res['status']) { return $res['message']; }
        $reviews = $res['data'][0];
        $review_setting = $res['data'][1];
        $review_widget = $res['data'][2];
        $product = $res['data'][3];
        $mail_setting = $res['data'][4];
        $mail_setting_profiles = $res['data'][5];
        $review_widget_tags = $res['data'][6];
        $total_review_count = $res['data'][7] ?? 0;

        return view('api_review/index', compact('product', 'reviews', 'review_setting', 'review_widget', 'mail_setting', 'mail_setting_profiles', 'review_widget_tags', 'total_review_count'))->render();
    }

    // コメント用API
    public function review_data (Request $request)
    {
        $res = $this->_get_review($request);
        if (!$res['status']) { return ['status' => false, 'message' => $res['message']]; }
        $reviews = $res['data'][0];
        $review_setting = $res['data'][1];
        $review_widget = $res['data'][2];
        $product = $res['data'][3];
        $mail_setting = $res['data'][4];
        $mail_setting_profiles = $res['data'][5];
        $review_widget_tags = $res['data'][6];
        $total_review_count = $res['data'][7] ?? 0;

        $company_name = !empty(User::find($request->user_id)->company_tied) ?
            User::find($request->user_id)->company_tied->name : 'カスタマーサポート';

        $data = [];
        foreach($reviews as $r){
            $hash = $r->toArray();
            // 個人情報系は取り除く
            unset($hash['name_of_reviewer']);
            unset($hash['email_of_reviewer']);
            unset($hash['phone_of_reviewer']);

            $hash['display_review_name'] = $r->display_review_name($review_widget);
            $hash['display_review_date'] = $r->display_review_date($review_widget);
            $hash['display_age'] = !empty($r->age) ? Review::AGE[$r->age] : '';
            $hash['display_gender'] = !empty($r->gender) ? Review::GENDER[$r->gender] : '';
            $hash['company_name'] = $company_name;
            $data[] = $hash;
        }
        
        $setting = [];
        $setting['age'] = Review::AGE;
        $setting['gender'] = Review::GENDER;
        $setting['app_url'] = str_replace('st.', '', config('app.url'));
        $setting['logo_url'] = asset('/images/slider_logo.png');

        return [
            'status' => true,
            'data' => $data,
            'review_setting' => $review_setting,
            'review_widget' => $review_widget,
            'product' => $product,
            'mail_setting' => $mail_setting,
            'total_review_count' => $total_review_count,
            'is_review_count' => $review_widget->is_review_count ?? false,
            'mail_setting_profiles' => $mail_setting_profiles,
            'review_widget_tags' => $review_widget_tags,
            'setting' => $setting
        ];
    }

    // コメントタグ用API
    public function review_tag (Request $request)
    {
        $user = User::find($request->user_id);
        $tags = ReviewWidgetTag::where('company_id', $user->company_id);
        if (!empty($request->review_widget_id)) {
            $tags = $tags->where('review_widget_id', $request->review_widget_id);
        }
        $tags = $tags->orderBy('sort_order')->get();

        return $tags;
    }

    public function _get_review(Request $request)
    {
        $user = User::find($request->user_id);
        $company_id = $user->company_id;
        $rw_id = $request->review_widget_id;

        $res = $this->_common_auth($company_id, $request);
        if (!$res['status']) { return ['status' => false, 'message' => $res['msg']]; }

        $product = $res['product'];
        $review_widget = $res['review_widget'];

        if (!$review_widget->is_public) {
            return ['status' => false, 'message' => 'レビューセットは公開されていません'];
        }

        $review_ids = [];

        $reviews = Review::where('is_verified', 1)->where('company_id', $company_id);

        if (!empty($request->review_id)) {
            $reviews = $reviews->where('id', $request->review_id);
        } else {
            if (!$request->get_all_review) {
                // product_display_idには依存しないのでコメントアウト
                /*
                if ($request->product_display_id == 'site' || empty($request->product_display_id)) {
                    // サイトレビューだったら
                    $reviews = $reviews->whereNull('product_id');
                } else if (!is_null($product->id)) {
                    // 商品が指定されていたら
                    $reviews = $reviews->where('product_id', $product->id);
                }*/

                if (!empty($request->review_widget_id)) {
                    // レビューウィジェットが指定されていたら
                    $review_ids = $review_widget->review_widget_reviews->sortBy('sort_order')->pluck('review_id');
                    $reviews = $reviews->whereIn('id', $review_ids);
                }
            }

            if($request->ignore_published == 0 || empty($request->ignore_published)) {
                $reviews = $reviews->where('published', 1);
            }
        }

        // ソート
        if($review_widget->is_sort_latest == 1){
            $reviews = $reviews->orderByRaw('review_date_time desc');
        }elseif (count($review_ids) > 0) {
            // レビューウィジェットが指定されていたら
            $reviews = $reviews->orderByRaw('FIELD(id, ' . implode(',', $review_ids->toArray()) . '), review_date_time desc');
        }

        $reviews = $reviews->get();

        // 全レビュー件数を取得（公開/非公開に関わらず）
        $total_review_count = 0;
        if (!empty($request->review_widget_id) && $review_widget->is_review_count) {
            $total_review_count = Review::where('is_verified', 1)
                ->where('company_id', $company_id)
                ->whereIn('id', $review_widget->review_widget_reviews->pluck('review_id'))
                ->count();
        }

        $review_setting = (new ReviewSetting())->find_or_default($company_id);
        $mail_setting = (new MailSetting())->find_or_default($company_id);
        
        $mail_setting_profiles = $mail_setting->mail_setting_profiles()->orderBy('profile_id')->orderBy('sort_order')->get();
        
        $tags = ReviewWidgetTag::where('company_id', $user->company_id);
        if (!empty($request->review_widget_id)) {
            $tags = $tags->where('review_widget_id', $request->review_widget_id);
        }
        $tags = $tags->orderBy('sort_order')->get();
        
        return ['status' => true, 'data' => [$reviews, $review_setting, $review_widget, $product, $mail_setting, $mail_setting_profiles, $tags, $total_review_count]];
    }

    // サイトからレビュー → メール送信
    public function create_mail_send (Request $request)
    {
        $user = User::find($request->user_id);
        $company_id = $user->company_id;

        $res = $this->_common_auth($company_id, $request);
        if (!$res['status']) { return $res['msg']; }

        $product = $res['product'];

        $site = Site::where('company_id', $user->company_id)->first();

        // レビューに未承認状態のレコードを作る
        $review = new Review();
        $review->fill_params($request);
        $review->company_id = $user->company_id;
        $review->product_id = $product->id;
        $review->is_verified = 0;
        $review->verification_token = Str::random(40);
        $review->site_id = $site->id;
        $review->channel = 'site';
        $review->review_widget_id_auto_fill = $request->review_widget_id;

        if ($request->file('image_file')) {
            $path = $request->file('image_file')->store('public/review_images');
            $path = explode('/', $path)[2];
            $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/review_images/' . $path;
            $review->image_path = $image_url;
        }

        // メール送信
        try {
            $is_sendgrid = true;
            $mh = new MailHandler($user->company_id, $is_sendgrid);
            $res = $mh->address_confirm($review, $request->email);
        } catch ( Exception $ex ) {
            return ['status' => false, 'msg' => 'メール送信エラーが発生しました'];
        }

        if ($res !== false) {
            $review->save();
            return ['status' => true, 'msg' => ''];
        } else {
            return ['status' => false, 'msg' => 'メール送信エラーが発生しました'];
        }
    }

    // サイトからレビュー投稿 → メール送信後の本人の承認リンクを押した後
    // 一度押すとURLは無効になる
    public function verify (Request $request)
    {
        $token = $request->verification_token;
        $review = Review::where('verification_token', $token)->where('is_verified', 0)->first();
        $msg = NULL;

        if (!$review) {
            $msg = 'URLは無効です';
            return view('review/create', compact('msg'))->render();
        }

        $site = Site::where('company_id', $review->company_id)->first();
        $review_setting = (new ReviewSetting())->find_or_default($review->company_id);

        if (!$site) {
            $msg = 'URLは無効です';
            return view('review/create', compact('msg'))->render();
        }
        $review->is_verified = 1;
        $review->save();

        if ($review->review_widget_id_auto_fill) {
            ReviewWidgetReview::create([
                'company_id' => $review->company_id,
                'site_id' => $site->id,
                'review_widget_id' => $review->review_widget_id_auto_fill,
                'review_id' => $review->id,
                'sort_order' => 1
            ]);
        }

        return view('review/create', compact('msg', 'site', 'review_setting'))->render();
    }

    // like, badカウントボタン
    public function update_count (Request $request)
    {
        $id = $request->id;
        $r = Review::find($id);
        $tmp = $request->value == 'plus' ? 1 : -1;
        if ($request->type == 'like') {
            $r->like_counts = $r->like_counts + $tmp;
            $r->like_counts = max($r->like_counts, 0);
        } else {
            $r->bad_counts = $r->bad_counts + $tmp;
            $r->bad_counts = max($r->bad_counts, 0);
        }
        $r->save();
    }

    // メールから「投稿」ボタンを押したとき、直リンクでレビューを書くときに実行する
    // order_id,order_qid,product_idまたはsite_id,site_qid,emailが必要
    public function edit (Request $request)
    {
        $msg = NULL;
        $company_id = NULL;
        $product_id = NULL;
        $email = NULL;
        $order = NULL;
        $site = NULL;
        $type = $request->input('type');

        if ($request->has(['order_id', 'order_qid', 'product_id'])) {
            $order_id = $request->order_id;
            $product_id = $request->product_id;
            $order = Order::where('id', $order_id)->first();

            if (!$order) {
                $msg = 'URLは無効です';
                return view('api_review/review_form', compact('msg'))->render();
            }

            if ($request->order_qid != $order->qid()) {
                $msg = '注文IDは無効です';
                return view('api_review/review_form', compact('msg'))->render();
            }

            $company_id = $order->company_id;
            $email = $order->email;
            $is_site_review = false;
        } else if ($request->has(['site_id', 'site_qid', 'email'])) {
            $site = Site::find($request->site_id);

            if (!$site || $request->site_qid != $site->qid()) {
                $msg = 'URLは無効です';
                return view('api_review/review_form', compact('msg'))->render();
            }

            $company_id = $site->company_id;
            $email = $request->email;
            $is_site_review = true;
        } else {
            $msg = 'URLは無効です';
            return view('api_review/review_form', compact('msg'))->render();
        }

        $review = new Review();
        $review->ratings_of_review = $request->star;
        $review->title_of_review = $request->title;
        $review->description_of_review = $request->review;
        $review->name_of_reviewer = $request->name_of_reviewer;
        $review->nick_name_of_reviewer = $request->nick_name_of_reviewer;
        $review->channel = 'mail';
        $review->age = $request->age;
        $review->gender = $request->gender;
        $review->profile1 = $request->profile1;
        $review->profile2 = $request->profile2;
        $review->profile3 = $request->profile3;
        $review->profile4 = $request->profile4;
        $review->profile5 = $request->profile5;
        $review->item_rating1 = $request->item_rating1;
        $review->item_rating2 = $request->item_rating2;
        $review->item_rating3 = $request->item_rating3;
        $review->item_rating4 = $request->item_rating4;
        $review->item_rating5 = $request->item_rating5;
        $review->item_rating_name = $request->item_rating_name;

        $review_setting = (new ReviewSetting())->find_or_default($company_id);
        $review_widget = (new ReviewWidget())->find_or_default($company_id);
        $mail_setting = (new MailSetting())->find_or_default($company_id);

        return view('api_review/review_form', compact('msg', 'order', 'review', 'product_id', 'site', 'email',
        'review_setting', 'review_widget', 'mail_setting', 'is_site_review', 'type'))->render();
    }

    // 直リンク or メール後サイトから来たものを保存する
    public function create (Request $request)
    {
        $return_json = !empty($request->return_json);
        $msg = NULL;
        $company_id = NULL;
        $product_id = NULL;
        $email = NULL;
        $order = NULL;

        if ($request->has(['order_id', 'order_qid', 'product_id'])) {
            $product_id = $request->product_id;
            $order_id = $request->order_id;
            $order = Order::find($order_id);

            if (!$order) {
                $msg = 'URLは無効です';
                return $return_json ? Response::json(['status' => false, 'msg' => $msg], 400) : view('review/create', compact('msg'))->render();
            }
            if ($request->order_qid != $order->qid()) {
                $msg = '注文IDは無効です';
                return $return_json ? Response::json(['status' => false, 'msg' => $msg], 400) : view('review/create', compact('msg'))->render();
            }

            // orderとproductの整合性が取れているかチェック
            $c = OrderProduct::where('order_id', $order_id)->where('product_id', $product_id)->count();
            if ($c == 0){
                $msg = '商品IDまたは注文IDは無効です';
                return $return_json ? Response::json(['status' => false, 'msg' => $msg], 400) : view('review/create', compact('msg'))->render();
            }

            $company_id = $order->company_id;
            $email = $order->email;
        } else if ($request->has(['site_id', 'site_qid', 'email'])) {
            $site = Site::find($request->site_id);

            if (!$site || $request->site_qid != $site->qid()) {
                $msg = 'URLは無効です';
                return $return_json ? Response::json(['status' => false, 'msg' => $msg], 400) : view('review/create', compact('msg'))->render();
            }


            $company_id = $site->company_id;
            $email = $request->email;
        } else {
            $msg = 'URLは無効です';
            return $return_json ? Response::json(['status' => false, 'msg' => $msg], 400) : view('review/create', compact('msg'))->render();
        }

        $review = new Review();
        $site = Site::where('company_id', $company_id)->first();

        $review->company_id = $company_id;
        $review->site_id = $site->id;
        $review->product_id = $product_id;
        $review->name_of_reviewer = $request->name_of_reviewer;
        $review->title_of_review = $request->title ?? "";
        $review->nick_name_of_reviewer = $request->nickname;
        $review->description_of_review = $request->review;
        $review->ratings_of_review = $request->star;
        $review->review_date_time = date("Y-m-d H:i:s");
        $review->published = 0;
        $review->is_verified = 1;
        $review->email_of_reviewer = $email;
        $review->channel = 'mail';
        $review->display_order_id = !is_null($order) ? $order->display_order_id : '';
        $review->age = $request->age != '' ? $request->age : 0;
        $review->gender = $request->gender != '' ? $request->gender : 0;
        $review->profile1 = $request->profile1;
        $review->profile2 = $request->profile2;
        $review->profile3 = $request->profile3;
        $review->profile4 = $request->profile4;
        $review->profile5 = $request->profile5;
        $review->item_rating1 = $request->item_rating1;
        $review->item_rating2 = $request->item_rating2;
        $review->item_rating3 = $request->item_rating3;
        $review->item_rating4 = $request->item_rating4;
        $review->item_rating5 = $request->item_rating5;
        $review->item_rating_name = $request->item_rating_name;

        if (!empty($request->file('image_file'))) {
            $path = $request->file('image_file')->store('public/review_images');
            $path = explode('/', $path)[2];
            $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/review_images/' . $path;
            $review->image_path = $image_url;
        }

        $review->save();

        $review_setting = (new ReviewSetting())->find_or_default($company_id);
        $review_widget = (new ReviewWidget())->find_or_default($company_id);

        return $return_json ? ['status' => true] : view('review/create', compact('msg', 'review_setting', 'review_widget', 'site'));
    }

    // コメント一つだけを参照するページ
    public function show (Request $request)
    {
        $review = Review::where('id', $request->review_id)->first();
        if (!$review || $review->qid() != $request->review_qid) {
            $msg = 'レビューが不正です';
            return view('api_review/show', compact('msg'))->render();
        }
        $product = $review->product;
        $company_id = $review->company_id;
        $user_qid = User::where('company_id', $company_id)->first()->qid();

        return view('api_review/show', compact('product', 'review', 'company_id', 'user_qid'))->render();
    }

    //リッチスニペット自動更新
    public function auto_update_rich_snippet($id)
    {
        $widget = ReviewWidget::with('review_widget_reviews.review')
            ->find($id);

        if (!$widget || $widget->is_rich_snippet != 1 || $widget->is_auto_update_snippet != 1) {
            return response()->json(['error' => 'No snippet available'], 404);
        }

        $review_count = 0;  // 公開レビュー件数
        $total_review_count = 0;  // 全レビュー件数（公開/非公開問わず）
        $star_total = 0;

        foreach ($widget->review_widget_reviews as $rwr) {
            $review = $rwr->review;
            // 評価平均値と公開レビュー件数の計算
            if ($review && $review->published == 1 && $review->is_verified == 1) {
                $star_total += $review->ratings_of_review;
                $review_count++;
            }
            
            // 全体のレビュー件数のカウント（検証済みのみ）
            if ($review && $review->is_verified == 1) {
                $total_review_count++;
            }
        }

        $rating = $review_count > 0 ? floor(($star_total / $review_count) * 10) / 10 : 0;

        // レビュー件数表示設定がONの場合は全レビュー数、OFFの場合は公開レビュー数を表示
        $display_review_count = ($widget->is_review_count && $total_review_count > 0) ? $total_review_count : $review_count;

        $company = Company::find($widget->company_id);

        return response()->json([
            'name' => $widget->product_name,
            'brand' => $company->name,
            'ratingValue' => $rating,
            'reviewCount' => $display_review_count,
            'is_auto_update_snippet' => (int) $widget->is_auto_update_snippet
        ]);
    }

    public function review_widget_company_id(Request $request)
    {
        $company_id = $request->route('company_id');
        if (!$company_id) {
            return ['status' => false, 'msg' => 'IDが指定されていません'];
        }

        $review_widgets = ReviewWidget::where('company_id', $company_id)->orderBy('is_public', 'desc')->orderBy('url_setting_type', 'desc')->pluck('id');
        if (!$review_widgets) {
            return ['status' => false, 'msg' => 'Bad Authority(no review widget)'];
        }
        return ['status' => true, 'data' => $review_widgets];
    }

    public function review_widget(Request $request)
    {
$startTime0 = microtime(true);
        $rwid = $request->route('id');
        $company_id = $request->company_id;
        if (!$rwid) {
            return ['status' => false, 'msg' => 'IDが指定されていません'];
        }

$startTime1 = microtime(true);
        $review_widget = ReviewWidget::where('company_id', $company_id)->where('id', $rwid)->first();
if($company_id == 13 || $company_id == 141){
    Log::info('1 - review_widget 計算時間: ' . (microtime(true) - $startTime1) . '秒');
}
        if (!$review_widget) {
            return ['status' => false, 'msg' => 'Bad Authority(no review widget)'];
        }

        if($review_widget->is_rich_snippet == 1){
            $company_name = Company::select('name')->where('id', $company_id)->first();
$startTime2 = microtime(true);
            $review_widgets_richsnippet = ReviewWidget::with('review_widget_reviews.review')->where('company_id', $company_id);
if($company_id == 13 || $company_id == 141){
    Log::info('2 - review_widgets_richsnippet 計算時間: ' . (microtime(true) - $startTime2) . '秒');
}

            $review_widgets_richsnippet = $review_widgets_richsnippet->get();

            $data = $review_widgets_richsnippet->toArray();
            $res_data = [];

$startTime3 = microtime(true);
            foreach($review_widgets_richsnippet as $i => $rw){
                if($rw->id == $rwid){
                    $star_count = 0;
                    $rating = 0;
                    $review_count = $rw->review->where('is_verified', 1)->where('published', 1)->count();
                    if($review_count > 0 && $rw->is_rich_snippet == 1){
                        for($n=0; $n<count($rw->review_widget_reviews); $n++){
                            if ($rw->review_widget_reviews[$n]->review->published == 1) {
                                $star_count += $rw->review_widget_reviews[$n]->review->ratings_of_review;
                            }
                        }
                        $rating = $star_count > 0 ? $star_count / $review_count : 0;
                        if($rating > 0){
                            $rating = (floor($rating * 10) / 10);
                        }
                    }
                    $review_widget['reviewCount'] = $review_count;
                    $review_widget['rating'] = $rating;
                    $review_widget['company_name'] = $company_name->name;
                    $review_widget['is_auto_update_snippet'] = $rw->is_auto_update_snippet;
                }
            }
if($company_id == 13 || $company_id == 141){
    Log::info('3 - review_widgets_richsnippet 計算時間: ' . (microtime(true) - $startTime3) . '秒');
}
        }
        
if($company_id == 13 || $company_id == 141){
    Log::info('0 - review_widget 計算時間: ' . (microtime(true) - $startTime0) . '秒');
}
        return ['status' => true, 'data' => $review_widget];
    }

    public function review_widget_include(Request $request)
    {
        $rwid = $request->route('id');
        if (!$rwid) {
            return ['status' => false, 'msg' => 'IDが指定されていません'];
        }

        $review_widget_include = ReviewWidgetInclude::where('company_id', $request->company_id)->where('review_widget_id', $rwid)->pluck('include_url')->toArray();

        if (!$review_widget_include) {
            return ['status' => false, 'msg' => 'Bad Authority(no review widget include)'];
        }
        return ['status' => true, 'data' => $review_widget_include];
    }

    public function review_widget_exclude(Request $request)
    {
        $rwid = $request->route('id');
        if (!$rwid) {
            return ['status' => false, 'msg' => 'IDが指定されていません'];
        }

        $review_widget_exclude = ReviewWidgetExclude::where('company_id', $request->company_id)->where('review_widget_id', $rwid)->pluck('exclude_url')->toArray();

        if (!$review_widget_exclude) {
            return ['status' => false, 'msg' => 'Bad Authority(no review widget include)'];
        }
        return ['status' => true, 'data' => $review_widget_exclude];
    }

    public function richsnippet_info(Request $request)
    {
        $company_id = $request->route('company_id');
        $company_name = Company::select('name')->where('id', $company_id)->first();
        $review_widgets = ReviewWidget::with('review_widget_reviews.review')->where('company_id', $company_id);

        $review_widgets = $review_widgets->get();

        $data = $review_widgets->toArray();
        $res_data = [];
        foreach($review_widgets as $i => $rw){
            $star_count = 0;
            $rating = 0;
            $review_count = $rw->review->where('is_verified', 1)->where('published', 1)->count();
            $total_review_count = $rw->review->where('is_verified', 1)->count();
            
            if($review_count > 0 && $rw->is_rich_snippet == 1){
                for($n=0; $n<count($rw->review_widget_reviews); $n++){
                    if ($rw->review_widget_reviews[$n]->review->published == 1) {
                        $star_count += $rw->review_widget_reviews[$n]->review->ratings_of_review;
                    }
                }
                $rating = $star_count > 0 ? $star_count / $review_count : 0;
                if($rating > 0){
                    $rating = (floor($rating * 10) / 10);
                }
            }
            
            // レビュー件数表示設定がONの場合は全レビュー数、OFFの場合は公開レビュー数を表示
            $display_review_count = ($rw->is_review_count && $total_review_count > 0) ? $total_review_count : $review_count;
            
            $tmp = [
                'reviewCount' => $display_review_count,
                'rating' => $rating,
                'company_name' => $company_name->name,
                'is_auto_update_snippet' => $rw->is_auto_update_snippet,
            ];
            $res_data[] = array_merge($data[$i], $tmp);
        }
        return ['data' => $res_data];
    }
}
