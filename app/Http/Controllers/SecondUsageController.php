<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\SecondUsage;
use App\Instagram;
use Session;

class SecondUsageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function new (Request $request)
    {
        // Instagramをhiddenで引き回ししたくないのでセッションに入れる
        Session::put('second_usage_instagram', $request->instagram);
        $instagrams = $request->instagram;

        $su = new SecondUsage();
        $su->set_default();

        return view('second_usage/index', compact('su', 'instagrams'));
    }

    public function confirm (Request $request)
    {
        if (Session::exists('second_usage_instagram')) {
            $instagrams = Session::get('second_usage_instagram');
        } else {
            return redirect('instagram')->with('flash_message', '既にリクエスト登録しています');
        }

        $arr = $request->all();
        $su = new SecondUsage($arr);

        return view('second_usage/confirm', compact('su', 'instagrams'));
    }

    public function create (Request $request)
    {
        $instagrams = Session::get('second_usage_instagram');

        DB::transaction(function() use ($instagrams, $request) {
            foreach($instagrams as $ig){
                // ig_id, company_idで重複していた場合更新される
                $su = SecondUsage::where('company_id', Auth::user()->company_id)->where('ig_id', $ig['id'])->first();
                if (!$su) {
                    $su = new SecondUsage();
                }

                $su->company_id = Auth::user()->company_id;
                $su->comment = $request->comment;
                $su->answer_hashtag = $request->answer_hashtag;
                $su->account_name = $request->account_name;
                $su->ig_id = $ig['id'];
                $su->is_applying = 0;
                $su->is_setting = 0;
                $su->permalink = $ig['permalink'];
                $su->image = $ig['image'];
                $su->content = $ig['content'];
                $su->save();
            }

            Session::forget('second_usage_instagram');
        });

        return redirect('instagram')->with('flash_message', '更新しました');
    }

    public function delete ($id)
    {
        $su = SecondUsage::where('company_id', Auth::user()->company_id)->where('id', $id);
        $su->delete();

        return redirect('second_usage/oauth')->with('flash_message', '削除しました');
    }

    public function _target_usage() {
        // 1日10件まで
        $limit = 10;
        $today_send_count = SecondUsage::where('company_id', Auth::user()->company_id)->whereRaw('apply_datetime >= CURRENT_DATE')->count();
        if ($limit - $today_send_count >= 1) {
            $sus = SecondUsage::where('company_id', Auth::user()->company_id)
                ->whereRaw('apply_datetime is null')->orderBy('created_at')->limit($limit - $today_send_count)->get();
            return $sus;
        } else {
            return collect([]);
        }
    }

    public function oauth ()
    {
        $sus = $this->_target_usage();

        return view('second_usage/oauth', compact('sus'));
    }

    public function reply (Request $request)
    {
        $sus = $this->_target_usage();

        $at = Auth::user()->company_tied->access_token;
        $id = Auth::user()->company_tied->ig_business_account_id;
        //$at = 'EAAVJo4WsMZBcBAI6klRyap0zjEnucNhPKZCn9RH1NVgzEvG67u5pbbJnGm16BxYT3fsk8YytwospZBHhFbwWMZCFofxAs7tmiVFvGqBjBm6E79YevR5qoo8z1X6CapxE0iZBQoF9JhR30iSpZBSTryoFcRPMHhkzRrNhne5mpMngpkbvJI5ZBhZAKBNwmfYPDecclo5mhcPy6ybdladh6tsF';
        $fv = config('app.facebook_version');
        $host = "https://graph.facebook.com/v{$fv}/";

        $result = DB::transaction(function() use ($at, $id, $fv, $host, $sus) {
            foreach($sus as $su){
                $message = $su->replaced_comment();
                $ig_id = $su->ig_id;
                $url = $host . $ig_id . '/comments?message=' . urlencode($message) . '&access_token=' . $at;
                $options = [
                    'http' => [
                        'method'=> 'POST',
                        'ignore_errors' => true
                    ]
                ];

                $context = stream_context_create($options);
                $json = file_get_contents($url, false, $context);
                $json = mb_convert_encoding($json, 'UTF8', 'ASCII,JIS,UTF-8,EUC-JP,SJIS-WIN');
                $result = json_decode($json, true);
                Log::info($url);
                Log::info(json_encode($result));

                if (array_key_exists('error', $result)) {
                    return false;
                }

                $su->is_applying = 1;
                $su->apply_datetime = date("Y-m-d H:i:s");
                $su->save();
            }
            return true;
        });

        if ($result) {
            return view('second_usage/reply', compact('sus'));
        } else {
            return redirect('second_usage/oauth')->with('flash_message', '送信に失敗しました');
        }

    }


}
