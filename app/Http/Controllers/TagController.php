<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TagController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $user_id = Auth::user()->id;
        $user_qid = Auth::user()->qid();
        return view('tag/index', compact('user_id', 'user_qid'))->render();
    }

}
