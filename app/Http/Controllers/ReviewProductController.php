<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;
use App\Review;
use App\ProductGroup;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ReviewProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $products = Product::where('company_id', Auth::user()->company_id);
        $new_product = new Product();
        $product_groups = ProductGroup::where('company_id', Auth::user()->company_id);
        $for_export = $this->_export($products);

        return view('review_product/index', compact('products', 'new_product', 'product_groups', 'for_export'));
    }

    public function search (Request $request)
    {
        $res = $request->all();
        $products = Product::where('company_id', Auth::user()->company_id);

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('daterange', $res2) && $res2['daterange'] != ''){
                $arr = explode('/', $res2['daterange']);
                $sd = trim($arr[0]);
                $ed = trim($arr[1]);
                $products = $products->whereBetween('created_at', [$sd, $ed . ' 23:59:59']);
            }
            if(array_key_exists('name', $res2) && $res2['name'] != ''){
                $word = $res2['name'];
                $products = $products->where('name', 'like', "%$word%");
            }
        }

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $products->count(),
            "sort" => "asc",
            "field" => "id"
        ];

        $products = $products->get();

        $data = $products->toArray();
        $res_data = [];
        foreach($products as $i => $p){
            $tmp = [
                'ratingAvg' => $p->reviews->avg('ratings_of_review'),
                'reviewCount' => $p->reviews->count(),
                'groupName' => $p->product_group ? $p->product_group->name : NULL,
            ];
            $res_data[] = array_merge($data[$i], $tmp);
        }

        return ['meta' => $meta, 'data' => $res_data];
    }

    public function create (Request $request)
    {
        $arr = $request->all();

        if ($request->id) {
            // update
            // 重複確認
            $product_display_ids = Product::where('company_id', Auth::user()->company_id)->whereNotIn('id', [$request->id])->pluck('product_display_id');
            if (in_array($request->product_display_id, $product_display_ids->all())) {
                return redirect('/reviews/products')->with('flash_message', ['type' => 'error', 'msg' => '商品IDが重複しています']);
            }
            $p = Product::where('company_id', Auth::user()->company_id)->where('id', $request->id)->first();
            $p->fill($arr)->save();

        } else {
            // create
            // 重複確認
            $product_display_ids = Product::where('company_id', Auth::user()->company_id)->pluck('product_display_id');
            if (in_array($request->product_display_id, $product_display_ids->all())) {
                return redirect('/reviews/products')->with('flash_message', ['type' => 'error', 'msg' => '商品IDが重複しています']);
            }
            $p = new Product($arr);
            $p->company_id = Auth::user()->company_id;
            $p->save();
        }


        return redirect('/reviews/products')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function delete (Request $request)
    {
        $p = Product::where('company_id', Auth::user()->company_id)->where('id', $request->id);
        if($p->count() == 1) {
            $p->delete();
            // 紐づくレビューを全て非公開にする
            Review::where('product_id', $request->id)->update(['published' => 0]);
        }

        return redirect('/reviews/products')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function delete_all()
    {
        $p = Product::where('company_id', Auth::user()->company_id);
        $p->delete();

        return redirect('/reviews/products')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function import (Request $request)
    {
        $data = CSVImport::import_csv($request, ['product_id', 'product_name', 'product_url']);
        if ($data == 'not_include_require_columns') {
            return redirect('/reviews/products')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：必須カラムが含まれていません']);
        } else if ($data == 'not_valid_type') {
            return redirect('/reviews/products')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：ファイルがUTF-8かShift-JISではありません']);
        }

        // 商品ID重複チェック
        $product_display_ids = Product::where('company_id', Auth::user()->company_id)->pluck('product_display_id')->all();
        $data_product_display_ids = array_map(function($d) { return $d['product_id']; }, $data);
        $arr = array_merge($product_display_ids, $data_product_display_ids);
        // 空文字は取り除く
        $arr = array_filter($arr, function($d){ return $d != ''; });

        if (count($arr) !== count(array_unique($arr))) {
            return redirect('/reviews/products')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：商品IDが重複しています']);
        }

        // 配列加工
        // product_id -> product_display_id, product_description -> detail, product_image_url -> image_url, product_name -> name,
        // product_price -> price, product_tags -> tag, spec_condition -> spec_status, spec_availability -> spec_stock_status,
        // group_name
        $inserts = [];
        foreach($data as $d) {
            $insert = [];
            foreach($d as $k => $v) {
                if ($k == 'product_description') {
                    $t = 'detail';
                } else if ($k == 'product_image_url') {
                    $t = 'image_url';
                } else if ($k == 'product_id') {
                    $t = 'product_display_id';
                } else if ($k == 'product_name') {
                    $t = 'name';
                } else if ($k == 'product_price') {
                    $t = 'price';
                } else if ($k == 'product_tags') {
                    $t = 'tag';
                } else if ($k == 'spec_condition') {
                    $t = 'spec_status';
                } else if ($k == 'spec_availability') {
                    $t = 'spec_stock_status';
                } else if ($k == 'group_name') {
                    continue;
                } else {
                    $t = $k;
                }
                $insert[$t] = $v;
            }
            // company_idの付与
            $insert['company_id'] = Auth::user()->company_id;

            $inserts[] = $insert;
        }

        // バルクインサート
        DB::table('products')->insert($inserts);

        return redirect('/reviews/products')->with('flash_message', ['type' => 'success', 'msg' => 'インポートしました']);
    }

    public function _export($products){
        $arr = $products->get()->map(function($p) {
            return
                $p->product_display_id . ',' .
                '"' . $p->name . '"' . ',' .
                '"' . $p->product_url . '"' . ',' .
                '"' . $p->image_url . '"' . ',' .
                '"' . $p->detail . '"' . ',' .
                $p->price . ',' .
                $p->tag . ',' .
                $p->product_group_id . ',' .
                $p->spec_status . ',' .
                $p->spec_stock_status . ',' .
                $p->spec_brand . ',' .
                $p->spec_sku . ',' .
                $p->spec_upc . ',' .
                $p->spec_mpn . ',' .
                $p->spec_isbn . ',' .
                $p->spec_ean . ',' .
                $p->spec_gtin
            ;
        })->all();
        array_unshift($arr, 'product_id,product_name,product_url,product_image_url,product_description,product_price,product_tags,group_name,spec_condition,spec_availability,spec_brand,spec_sku,spec_upc,spec_mpn,spec_isbn,spec_ean,spec_gtin');
        return implode("\n", $arr);
    }

}
