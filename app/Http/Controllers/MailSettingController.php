<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\MailSetting;
use App\MailSettingProfile;
use App\Site;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Aws\Ses\SesClient;
use Aws;

class MailSettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $ms = MailSetting::where('company_id', Auth::user()->company_id)->first();
        if (!$ms) {
            $ms = new MailSetting();
            $ms->set_default();
        }
        $item_ratings = array_map(function($i) use ($ms) { $key = 'item_rating'. $i .'_name'; return $ms->$key; }, [1,2,3,4,5]);
        $item_ratings = array_filter($item_ratings, function($v) { return !empty($v); });

        $ms_profiles = MailSettingProfile::where('company_id', Auth::user()->company_id)->get();

        return view('mail_setting/index', compact('ms', 'item_ratings', 'ms_profiles'));
    }

    public function create (Request $request)
    {
        $arr = $request->all();
        $site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;
        unset($arr['item_ratings']);
        foreach([1,2,3,4,5] as $i){
            unset($arr['mail_setting_profiles_' . $i]);
        }

        if ($request->id) {
            // 更新
            $ms = MailSetting::where('company_id', Auth::user()->company_id)->first();
            unset($arr['id']);
            $ms->fill($arr);
        } else {
            // 作成
            $ms = new MailSetting();
            $ms->set_default();
            $ms->fill($arr);
        }
        $ms->company_id = Auth::user()->company_id;
        $ms->site_id = $site_id;

        // ロゴ画像アップロード
        if ($request->file('logo_image') && $request->file('logo_image')->getSize() > 0) {
            // 画像アップロードをしていた場合
            $path = $request->file('logo_image')->store('public/logo_images');
            $path = explode('/', $path)[2];
            $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/logo_images/' . $path;
            $ms->logo_url = $image_url;
        }
        $ms->save();

        // item_ratingの保存
        if (!empty($request->item_ratings)) {
            $item_ratings = array_filter($request->item_ratings, function($v) { return !empty($v); });
            foreach([1,2,3,4,5] as $i){
                $key = 'item_rating' . $i. '_name';
                $ms->$key = NULL;
            }
            if (count($item_ratings) > 0) {
                foreach($item_ratings as $i => $r){
                    $key = 'item_rating' . ($i + 1) . '_name';
                    $ms->$key = $r['keyword'];
                }
                $ms->save();
            }
        }

        // profileの保存(delete insert)
        $result = [];
        foreach([1,2,3,4,5] as $j) {
            $index = 'mail_setting_profiles_' . $j;
            if (!isset($request->$index)) {
                continue;
            }else{
                $tags = array_filter($request->$index, function($v) { return !empty($v['keyword']); });
                if(empty($tags)){
                    $tags[0]['keyword'] = '';
                }
                foreach($tags as $i => $t) {
                    $word = $t['keyword'];
                    $tmp = [
                        'company_id' => Auth::user()->company_id,
                        'site_id' => $site_id,
                        'name' => $word,
                        'sort_order' => $i,
                        'created_at' => date("Y-m-d H:i:s"),
                        'updated_at' => date("Y-m-d H:i:s"),
                        'profile_id' => $j
                    ];
                    $result[] = $tmp;
                }
            }
        }
        if (count($result) > 0) {
            // バルクインサート
            MailSettingProfile::where('company_id', Auth::user()->company_id)->delete();
            DB::table('mail_setting_profiles')->insert($result);
        }

        return redirect('/reviews/mail_settings')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function verify(Request $request)
    {
        $email = $request->email;

        // 形式チェック
        if (filter_var($email, FILTER_VALIDATE_EMAIL) === false) {
            return ['type' => 'error', 'msg' => '正しいメールアドレス形式ではありません'];
        }

        $credentials = new Aws\Credentials\Credentials(
            config('app.aws_access_key_id'),
            config('app.aws_secret_access_key')
        );

        $client = new SesClient([
            'credentials' => $credentials,
            'region' => 'ap-northeast-1',
            'version' => '2010-12-01'
        ]);

        // 認証済みかどうか
        $result = $client->listVerifiedEmailAddresses([]);
        if (in_array($email, $result['VerifiedEmailAddresses'])) {
            return ['type' => 'error', 'msg' => 'すでに認証済みです'];
        }

        $result = $client->verifyEmailIdentity([
            'EmailAddress' => $email,
        ]);

        return ['type' => 'success', 'msg' => '送信しました'];
    }




}
