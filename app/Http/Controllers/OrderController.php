<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Review;
use App\Product;
use App\Order;
use App\OrderProduct;
use App\Company;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\library\Datatables;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('auth.master');
        // $this->middleware('site');
    }

    public function updateToken (Request $request)
    {
        $token = Str::random(50);
        $hashed_token = hash('sha256', $token);
        
        $request->user()->forceFill([
            'api_token' => $hashed_token,
            'expires_at' => now()->addDays(7),
        ])->save();

        return redirect()->back()->with('flash_message', ['type' => 'success', 'msg' => $hashed_token]);;
    }

    public function completed (Request $request)
    {
        $type = 'completed';
        $orders = Order::where('company_id', Auth::user()->company_id)->where('order_type', $type);
        $products = Product::where('company_id', Auth::user()->company_id)->get();

        return view('order/index', compact('orders', 'type', 'products'));
    }

    /**
     * @param Request $request
     * @return mixed
     * @subscribe 「未処理受注管理画面」と「直リンク受注管理画面」のページネーション処理
     */
    public function ajax(Request $request){
        $type = $request->input('type');
        $query = Order::query()->with('products')->where('company_id', Auth::user()->company_id)->where('order_type',$type);

        // 検索
        if($request->filled('search.value')) {

            $search_value = trim(
                mb_convert_kana($request->input('search.value'), 's')
            );
            $keywords = explode(' ', $search_value);

            foreach($keywords as $keyword) {
                $query->where(function ($query) use ($keyword) {
                    $query->where('name', 'LIKE', '%' . $keyword . '%')
                          ->orWhere('email', 'LIKE', '%' . $keyword . '%')
                          ->orWhere('display_order_id', 'LIKE', '%' . $keyword . '%')
                          ->orWhere('order_date', 'LIKE', '%' . $keyword . '%')
                          ->orWhere('created_at', 'LIKE', '%' . $keyword . '%');
                });
            }

        }

        // ソート
        if($request->filled('order.0.column')) {

            $order_column_index = $request->input('order.0.column');
            $order_column = $request->input('columns.'. $order_column_index .'.data');
            $order_direction = $request->input('order.0.dir');
            $query->orderBy($order_column, $order_direction);

        }

        // ページネーション用情報
        $start = intval($request->start);
        $per_page = intval($request->length);

        // 取得するカラム名
        $columns = [
            'id',
            'display_order_id',
            'name',
            'nickname',
            'email',
            'phone',
            'order_date',
            'created_at',
        ];
        $current_page = ($per_page === 0) ? 1 : $start / $per_page + 1;

        // 取得したデータに直リンクURLを追加
        $orders = $query->paginate($per_page, $columns, '', $current_page);
        $ordersArray = $orders->toArray();
        $decoded_ordersArray = json_decode(json_encode($ordersArray), true);

        foreach($decoded_ordersArray['data'] as $i => $o){
            $tmp2 = [];
            foreach($o['products'] as $n => $p){
                $tmp_product = Product::find($p['id']);
                $decoded_ordersArray['data'][$i]['products'][$n]['direct_link'] = $tmp_product->get_direct_link($o, $p['id']);
            }
        }
        
        return $decoded_ordersArray;
    }

    public function pending (Request $request)
    {
        $type = 'pending';
        $orders = Order::where('company_id', Auth::user()->company_id)->where('order_type', $type)->get();
        $new_order = new Order();
        $products = Product::where('company_id', Auth::user()->company_id)->get();

        $company_id = Auth::user()->company_id;
        $order_api_permission = Company::where('id', $company_id)->first()->order_api_permission;

        return view('order/index', compact('orders', 'type', 'new_order', 'products', 'order_api_permission'));
    }

    public function direct_link (Request $request)
    {
        $type = 'direct_link';
        $orders = Order::where('company_id', Auth::user()->company_id)->where('order_type', $type)->get();
        $new_order = new Order();
        $products = Product::where('company_id', Auth::user()->company_id)->get();

        return view('order/index', compact('orders', 'type', 'new_order', 'products'));
    }


    public function search (Request $request)
    {
        $res = $request->all();
        $orders = Order::where('company_id', Auth::user()->company_id)->where('order_type', $res['order_type']);

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('daterange', $res2) && $res2['daterange'] != ''){
                $arr = explode('/', $res2['daterange']);
                $sd = trim($arr[0]);
                $ed = trim($arr[1]);
                $orders = $orders->whereBetween('order_date', [$sd, $ed . ' 23:59:59']);
            }
            if(array_key_exists('freeword', $res2) && $res2['freeword'] != ''){
                $word = $res2['freeword'];
                $orders->where(function($orders) use ($word){
                    $orders->where('display_order_id', 'like', "%$word%")
                          ->orWhere('name', 'like', "%$word%")
                          ->orWhere('nickname', 'like', "%$word%")
                          ->orWhere('email', 'like', "%$word%");
                });
            }
        }

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $orders->count(),
            "sort" => "asc",
            "field" => "id"
        ];
        $orders = $orders->limit(2000)->get()->sortByDesc('created_at');

        $res_data = [];
        $data = $orders->toArray();
        foreach($orders as $i => $o){
            $tmp2 = [];
            foreach($o->products as $p){
                $tmp2[] = array_merge($p->toArray(), ['direct_link' => $p->get_direct_link($o)]);
            }
            $tmp = [
                'products' => $tmp2,
            ];
            $res_data[] = array_merge($data[$i], $tmp);
        }

        return ['meta' => $meta, 'data' => $res_data];
    }

    public function create (Request $request)
    {
        DB::transaction(function() use ($request) {
            // もし既存の注文IDが来たら上書き
            $order = Order::firstOrNew([
                'company_id'=> Auth::user()->company_id,
                'display_order_id'=> $request->display_order_id,
                'order_type'=> $request->order_type,
            ]);
            $order->fill_params($request);
            $order->company_id = Auth::user()->company_id;
            $order->order_type = $request->order_type;
            $order->save();

            // order_productのデータ作成
            $op_ids = $request->order_product_ids;
            $inserts = [];
            foreach($op_ids as $op_id){
                if (empty($op_id)) { continue; }
                // もしすでに存在していたらスキップ
                if (OrderProduct::where(['company_id' => Auth::user()->company_id, 'order_id' => $order->id, 'product_id' => $op_id])->count() > 0) {
                    continue;
                }
                $inserts[] = [
                    'company_id' => Auth::user()->company_id,
                    'order_id' => $order->id,
                    'product_id' => $op_id,
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s")
                ];
            }

            DB::table('order_products')->insert($inserts);
        });

        return redirect('/reviews/orders/' . $request->order_type)->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function delete (Request $request)
    {
        $order = Order::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$order) {
            return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'error', 'msg' => '受注が存在しません']);
        }

        DB::transaction(function() use ($request, $order) {
            // orderの削除
            $order->delete();

            // order_productの削除
            OrderProduct::where('order_id', $request->id)->delete();
        });

        return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function delete_all ()
    {
        $orders = Order::where('company_id', Auth::user()->company_id)->where('order_type', 'pending');
        if(!$orders) {
            return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'error', 'msg' => '受注が存在しません']);
        }

        DB::transaction(function() use ($orders) {
            // order_productの削除
            OrderProduct::where('company_id', Auth::user()->company_id)->whereIn('order_id', $orders->pluck('id')->toArray())->delete();

            // orderの削除
            $orders->delete();
        });

        return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function update (Request $request)
    {
        $order = Order::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$order) {
            return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'error', 'msg' => '受注が存在しません']);
        }

        // 編集するときには同じ注文IDに出来ない
        $order_display_ids = Order::where('company_id', Auth::user()->company_id)->whereNotIn('id', [$request->id])->pluck('display_order_id');
        if (in_array($request->display_order_id, $order_display_ids->all())) {
            return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'error', 'msg' => '注文IDが重複しています']);
        }

        DB::transaction(function() use ($request, $order) {
            // orderの更新
            $order->fill_params($request);
            $order->save();

            // 紐づくproductに変更があればorder_productの更新
            $retain_product_ids = OrderProduct::where('order_id', $order->id)->pluck('product_id')->all();
            $request_product_ids = $request->order_product_ids;
            sort($retain_product_ids);
            sort($request_product_ids);
            if ($request_product_ids != $retain_product_ids) {
                // delete insert
                OrderProduct::where('order_id', $request->id)->delete();

                $op_ids = $request->order_product_ids;
                $inserts = [];
                foreach($op_ids as $op_id){
                    if (empty($op_id)) { continue; }
                    $inserts[] = [
                        'company_id' => Auth::user()->company_id,
                        'order_id' => $order->id,
                        'product_id' => $op_id,
                        'created_at' => date("Y-m-d H:i:s"),
                        'updated_at' => date("Y-m-d H:i:s")
                    ];
                }
                DB::table('order_products')->insert($inserts);
            }
        });

        return redirect('/reviews/orders/pending')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function import (Request $request)
    {
        $order_type = $request->order_type;
        $data = CSVImport::import_csv($request, ['order_id', 'order_date', 'user_name', 'nickname', 'email', 'phone', 'currency', 'product_id']);
        if ($data == 'not_include_require_columns') {
            return redirect('/reviews/orders/' . $order_type)->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：必須カラムが含まれていません']);
        } else if ($data == 'not_valid_type') {
            return redirect('/reviews/orders/' . $order_type)->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：ファイルがUTF-8かShift-JISではありません']);
        }

        // order_id重複確認
        /*
        $data_order_ids = array_map(function($d) { return $d['order_id']; }, $data);
        $db_order_ids = Order::where('user_id', Auth::id())->pluck('display_order_id')->all();
        $arr = array_merge($db_order_ids, $data_order_ids);
        $arr = array_filter($arr, function($d){ return $d != ''; });
        if (count($arr) !== count(array_unique($arr))) {
            return redirect('/reviews/orders/' . $order_type)->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：注文IDが重複しています']);
        }*/

        // product_idをproduct_display_idに変換
        $data_product_display_ids = array_map(function($d) { return $d['product_id']; }, $data);
        $products = Product::where('company_id', Auth::user()->company_id)->get();
        $pds = [];
        foreach($products as $p){
            $pds[$p->product_display_id] = $p->id;
        }
        foreach($data_product_display_ids as $data_pdid){
            if(!array_key_exists($data_pdid, $pds)){
                return redirect('/reviews/orders/' . $order_type)->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：商品IDが存在しません']);
            }
        }


        DB::transaction(function() use ($data, $pds, $order_type) {
            // 配列加工
            // user_name -> name, order_id -> display_order_id
            $inserts = [];
            foreach($data as $d) {
                // DBに登録されている中にあるかどうか、あったらスキップ
                if (Order::where(['display_order_id'=> $d['order_id'], 'order_type' => $order_type ,'company_id' => Auth::user()->company_id])->count() > 0) {
                    continue;
                }

                $tmp = array_map(function($i) use ($inserts) { return $i['display_order_id'];}, $inserts);
                $search_index = array_search($d['order_id'], $tmp, true);
                if ($search_index !== false) {
                    // 前の方は削除
                    unset($inserts[$search_index]);
                }

                $insert = [];
                foreach($d as $k => $v) {
                    if ($k == 'user_name') {
                        $t = 'name';
                    } else if ($k == 'order_id') {
                        $t = 'display_order_id';
                    } else {
                        $t = $k;
                    }
                    if ($k != 'product_id') {
                        $insert[$t] = $v;
                    }
                }
                // company_idの付与
                $insert['company_id'] = Auth::user()->company_id;
                // typeの付与
                $insert['order_type'] = $order_type;

                $insert['created_at'] = date("Y-m-d H:i:s");
                $insert['updated_at'] = date("Y-m-d H:i:s");

                $inserts[] = $insert;
            }

            // バルクインサート
            DB::table('orders')->insert($inserts);

            // 商品紐づきの登録
            $ods = [];
            $orders = Order::where('company_id', Auth::user()->company_id)->chunk(100, function ($orders) use (&$ods) {
                foreach ($orders as $order) {
                    $ods[$order->display_order_id] = $order->id;
                }
            });

            $inserts2 = [];
            foreach($data as $d){
                $p_display_id = $d['product_id'];
                $product_id = $pds[$p_display_id];
                $o_display_id = $d['order_id'];
                $order_id = $ods[$o_display_id];

                // もしすでに存在していたらスキップ
                if (OrderProduct::where(['company_id' => Auth::user()->company_id, 'order_id' => $order_id, 'product_id' => $product_id])->count() > 0) {
                    continue;
                }

                $inserts2[] = [
                    'company_id' => Auth::user()->company_id,
                    'order_id' => $order_id,
                    'product_id' => $product_id,
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s")
                ];
            }
            DB::table('order_products')->insert($inserts2);

        });

        return redirect('/reviews/orders/' . $order_type)->with('flash_message', ['type' => 'success', 'msg' => 'インポートしました']);
    }

    public function _export($orders){
        $arr = $orders->map(function($o) {
            return $o->products->map(function($p) use ($o) {
                return
                    $o->display_order_id . ',' .
                    $o->order_date . ',' .
                    $o->name . ',' .
                    $o->nickname . ',' .
                    $o->email . ',' .
                    $o->phone . ',' .
                    $o->currency . ',' .
                    $p->product_display_id
                ;
            })->all();
        })->all();
        $v = [];
        array_walk_recursive($arr, function($e)use(&$v){$v[] = $e;});
        array_unshift($v, 'order_id,order_date,user_name,nickname,email,phone,currency,product_id');
        return implode("\n", $v);
    }

    public function getOrders(Request $request)
    {
        $type = $request->input('type');
        $encoding = request('export_type') === 'sjis' ? 'SJIS' : 'UTF-8';

        $orders = Order::query()
            ->with('products')
            ->where('company_id', Auth::user()->company_id)
            ->where('order_type', $type);

        $randomString = Str::random(10);
        $timestamp = now()->timestamp;
        $fileName = "orders-{$randomString}-{$timestamp}.csv";
        $filePath = 'public/tmp/' . $fileName;

        // ファイルを開く
        $file = fopen(Storage::path($filePath), 'w');

        // ヘッダー行の書き込み
        fputcsv($file, ['order_id', 'order_date', 'user_name', 'nickname', 'email', 'phone', 'currency', 'product_id']);

        // チャンク処理
        $orders->chunk(1000, function ($orders) use ($file) {
            foreach ($orders as $i => $order) {
                if(isset($order->products[0]) && !empty($order->products[0])){
                    foreach ($order->products as $product) {
                        fputcsv($file, [
                            $order->display_order_id,
                            $order->order_date,
                            $order->name,
                            $order->nickname,
                            $order->email,
                            $order->phone,
                            $order->currency,
                            $product->product_display_id,
                        ]);
                    }
                }else{
                    fputcsv($file, [
                        $order->display_order_id,
                        $order->order_date,
                        $order->name,
                        $order->nickname,
                        $order->email,
                        $order->phone,
                        $order->currency,
                        '',
                    ]);
                }
                
            }
        });

        // ファイルを閉じる
        fclose($file);

        // ファイルの文字コードを変換
        $fileContent = file_get_contents(Storage::path($filePath));
        $fileContent = mb_convert_encoding($fileContent, $encoding, 'UTF-8');
        file_put_contents(Storage::path($filePath), $fileContent);

        $downloadUrl = Storage::url($filePath);
        return response()->json(['download_url' => $downloadUrl]);
    }

    public function delete_csv(Request $request)
    {
        $downloadUrl = $request->input('download_url');

        $fileName = basename($downloadUrl);
        $filePath = 'public/tmp/' . $fileName;
    
        // ファイルを削除
        if (Storage::exists($filePath)) {
            Storage::delete($filePath);
            return response()->json(['message' => 'CSV file deleted successfully']);
        } else {
            return response()->json(['message' => 'File not found'], 404);
        }
    }
}