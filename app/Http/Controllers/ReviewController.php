<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Review;
use App\ReviewWidget;
use App\ReviewWidgetReview;
use App\Product;
use App\Order;
use App\Site;
use App\MailSetting;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Mail\MailHandler;

class ReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $reviews = Review::where('company_id', Auth::user()->company_id)->where('is_verified', 1);
        $review_widgets = ReviewWidget::where('company_id', Auth::user()->company_id)->get();
        $selected_review_widget_id = $request->review_widget_id;
        if ($selected_review_widget_id != '') {
            $rwr = ReviewWidgetReview::where('company_id', Auth::user()->company_id)->where('review_widget_id', $selected_review_widget_id)->pluck('review_id');
            $reviews = $reviews->whereIn('id', $rwr);
        }
        $products = Product::where('company_id', Auth::user()->company_id)->get();

        return view('review/index', compact('reviews', 'review_widgets', 'selected_review_widget_id', 'products'));
    }

    public function search (Request $request)
    {
        $res = $request->all();
        $reviews = Review::where('company_id', Auth::user()->company_id)->where('is_verified', 1);
        $rwrs = null;

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('status', $res2) && $res2['status'] != ''){
                if ($res2['status'] == '1') {
                    $reviews->where('published', 1);
                } else if ($res2['status'] == '0'){
                    $reviews->where('published', 0);
                }
            }

            if(array_key_exists('with_image', $res2) && $res2['with_image'] != ''){
                if ($res2['with_image'] == '1') {
                    $reviews->whereNotNull('image_path');
                } else if ($res2['with_image'] == '0') {
                    $reviews->whereNull('image_path');
                }
            }

            if(array_key_exists('product_id', $res2) && $res2['product_id'] != ''){
                $reviews->where('product_id', $res2['product_id']);
            }

            if(array_key_exists('daterange', $res2) && $res2['daterange'] != ''){
                $arr = explode('/', $res2['daterange']);
                $sd = trim($arr[0]);
                $ed = trim($arr[1]);
                $reviews = $reviews->whereBetween('review_date_time', [$sd, $ed . ' 23:59:59']);
            }

            if(array_key_exists('freeword', $res2) && $res2['freeword'] != ''){
                $word = $res2['freeword'];
                $reviews->where(function($reviews) use ($word){
                    $reviews->where('description_of_review', 'like', "%$word%")
                          ->orWhere('title_of_review', 'like', "%$word%")
                          ->orWhere('name_of_reviewer', 'like', "%$word%")
                          ->orWhere('nick_name_of_reviewer', 'like', "%$word%");
                });
            }

            if(array_key_exists('email', $res2) && $res2['email'] != ''){
                $word = $res2['email'];
                $reviews->where(function($reviews) use ($word){
                    $reviews->where('email_of_reviewer', 'like', "%$word%");
                });
            }

            if(array_key_exists('review_widget_id', $res2) && $res2['review_widget_id'] != ''){
                $id = $res2['review_widget_id'];
                $rwrs = ReviewWidgetReview::where('review_widget_id', $id)->get();
                $review_ids = $rwrs->pluck('review_id');
                $reviews = $reviews->whereIn('id', $review_ids);
            }
        }
        $reviews = $reviews->orderBy('review_date_time', 'desc');

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $reviews->count(),
            "sort" => "asc",
            "field" => "id"
        ];
        $data = $reviews->get()->toArray();

        $res_data = [];
        if ($rwrs) {
            foreach($data as $i => $d){
                $tmp = [
                    'sortOrder' => $rwrs->first(function($r) use ($d) { return $r->review_id == $d['id']; })->sort_order,
                ];
                $res_data[] = array_merge($data[$i], $tmp);
            }
        } else {
            $res_data = $data;
        }

        if($res_data){
            foreach($res_data as $i => $d){
                $cid = $d['company_id'];
                $site_id = $d['site_id'];
                $mail_setting = MailSetting::where('company_id', $cid)->where('site_id', $site_id)->first();
                $profiles = [];
                $another_profiles = [];
                for($j = 1; $j <= 5; $j++){
                    if($d['profile' . $j] != '' && $mail_setting['profile_name' . $j] != ''){
                        if($j < 4){
                            $profiles[] = $mail_setting['profile_name' . $j].' : '.$d['profile' . $j];
                        }else{
                            $another_profiles[] = $mail_setting['profile_name' . $j].' : '.$d['profile' . $j];
                        }
                        
                    }
                }
                $profileString_first = implode(' | ', $profiles);
                $profileString_second = implode(' | ', $another_profiles);
                $res_data[$i]['profileString_first'] = $profileString_first;
                $res_data[$i]['profileString_second'] = $profileString_second;

                $item_rating_names = explode("|", $d["item_rating_name"]);

                for($n=0; $n<5; $n++){
                    if(isset($item_rating_names[$n]) && !empty($item_rating_names[$n])){
                        $res_data[$i]["item_rating_name_" . $n] = $item_rating_names[$n];
                    }
                }
            }
        }

        return ['meta' => $meta, 'data' => $res_data];
    }

    public function update (Request $request)
    {
        $review = Review::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$review) {
            return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'レビューが存在しません']);
        }

        // 空の場合(新しくコメントを通知する場合)、コメント通知メールを送信
        $send_flag = empty($review->description_of_reply);

        $review->description_of_reply = $request->reply;
        $review->published_reply = $request->published == 'on' ? 1 : 0;
        $review->save();

        if ($send_flag) {
            $is_sendgrid = true;
            $mh = new MailHandler(Auth::user()->company_id, $is_sendgrid);
            $mh->comment_notify($review);
        }

        return redirect('/reviews')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function update_description_of_review (Request $request)
    {
        $review = Review::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$review) {
            return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'レビューが存在しません']);
        }

        $review->description_of_review = $request->review;
        $review->published = $request->published == 'on' ? 1 : 0;
        $review->save();

        return redirect('/reviews')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function update_published (Request $request)
    {
        $ids = explode(',', $request->id);
        $value = $request->published;
        $reviews = Review::where('company_id', Auth::user()->company_id)->whereIn('id', $ids);
        $reviews->update(['published' => $value]);

        return ['type' => 'success', 'msg' => '更新しました'];
    }

    public function update_review_widget_id (Request $request)
    {
        $ids = explode(',', $request->id);
        $rw_id = $request->review_widget_id;
        $ins_sort_order = ReviewWidgetReview::where('company_id', Auth::user()->company_id)->where('review_widget_id', $rw_id)->whereIn('id', $ids)->max('sort_order');
        $ins_sort_order = $ins_sort_order ?? 1;
        $site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;

        foreach ($ids as $id) {
            $igs = ReviewWidgetReview::firstOrCreate([
                'company_id' => Auth::user()->company_id,
                'site_id' => $site_id,
                'review_widget_id' => $rw_id,
                'review_id' => $id,
            ],
            [
                'sort_order' => $ins_sort_order
            ]);
        }

        return ['type' => 'success', 'msg' => '更新しました'];
    }

    public function update_sort_order (Request $request)
    {
        $id = $request->id;
        $rw_id = $request->review_widget_id;
        $rwr = ReviewWidgetReview::where('company_id', Auth::user()->company_id)->where('review_widget_id', $rw_id)->where('review_id', $id);
        $rwr->update(['sort_order' => $request->sort_order]);

        return ['type' => 'success', 'msg' => '更新しました'];
    }

    public function import (Request $request)
    {
        $data = CSVImport::import_csv($request, ['product_id', 'name_of_reviewer', 'title_of_review', 'description_of_review', 'ratings_of_review', 'review_date_time', 'published']);
        if ($data == 'not_include_require_columns') {
            return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：必須カラムが含まれていません']);
        } else if ($data == 'not_valid_type') {
            return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：ファイルがUTF-8かShift-JISではありません']);
        }

        // 商品IDがproductsに存在するかどうか
        $data_product_display_ids = array_map(function($d) { return $d['product_id']; }, $data);
        $products = Product::where('company_id', Auth::user()->company_id)->get();

        $pds = [];
        foreach($products as $p){
            $pds[$p->product_display_id] = $p->id;
        }

        foreach($data_product_display_ids as $data_pdid){
            if(!array_key_exists($data_pdid, $pds)){
                return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：商品IDが存在しません']);
            }
        }

        $site = Site::where('company_id', Auth::user()->company_id)->first();

        // 配列加工
        // product_display_id -> product_id
        $inserts = [];
        foreach($data as $d) {
            $insert = [];
            foreach($d as $k => $v) {
                if ($k == 'product_id') {
                    $t = 'product_id';
                    $insert[$t] = $pds[$v];
                } else {
                    $t = $k;
                    $insert[$t] = $v;
                }
            }
            // company_idの付与
            $insert['company_id'] = Auth::user()->company_id;
            // site_idの付与
            $insert['site_id'] = $site->id;

            $insert['channel'] = 'import';
            $insert['is_verified'] = 1;
            $insert['created_at'] = date("Y-m-d H:i:s");
            $insert['updated_at'] = date("Y-m-d H:i:s");

            $inserts[] = $insert;
        }

        // バルクインサート
        DB::table('reviews')->insert($inserts);
        return redirect('/reviews')->with('flash_message', ['type' => 'success', 'msg' => 'インポートしました']);
    }

    public function delete (Request $request)
    {
        $rwid = $request->review_widget_id;
        $rwr = ReviewWidgetReview::where('company_id', Auth::user()->company_id)->where('review_id', $request->id)->where('review_widget_id', $rwid);
        if ($rwr->count() == 1) {
            $rwr->delete();
        }

        return redirect('/reviews?review_widget_id=' . $rwid)->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function test (Request $request)
    {
        $product_id = $request->product_id;
        $product = Product::find($product_id);
        $user_qid = Auth::user()->qid();
        $company_id = Auth::user()->company_id;

        return view('review/test', compact('product', 'company_id', 'user_qid'))->render();
    }

}
