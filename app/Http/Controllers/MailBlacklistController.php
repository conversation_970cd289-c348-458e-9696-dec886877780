<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\MailBlacklist;
use App\Site;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class MailBlacklistController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $mails = MailBlacklist::where('company_id', Auth::user()->company_id);
        $for_export = $this->_export($mails);

        return view('mail_blacklist/index', compact('mails', 'for_export'));
    }

    public function search (Request $request)
    {
        $res = $request->all();
        $mails = MailBlacklist::where('company_id', Auth::user()->company_id);

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('daterange', $res2) && $res2['daterange'] != ''){
                $arr = explode('/', $res2['daterange']);
                $sd = trim($arr[0]);
                $ed = trim($arr[1]);
                $mails = $mails->whereBetween('created_at', [$sd, $ed . ' 23:59:59']);
            }

            if(array_key_exists('freeword', $res2) && $res2['freeword'] != ''){
                $word = $res2['freeword'];
                $mails->where('email', 'like', "%$word%");
            }
        }

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $mails->count(),
            "sort" => "asc",
            "field" => "id"
        ];
        $data = $mails->get()->toArray();

        return ['meta' => $meta, 'data' => $data];
    }

    public function create (Request $request)
    {
        $emails = $request->email;
        $email_arr = explode(',', $emails);
        $site = Site::where('company_id', Auth::user()->company_id)->first();

        // 重複を弾くためにすでに登録してある単語のリストを取得する
        $email_blacklists = MailBlacklist::where('company_id', Auth::user()->company_id)->pluck('email')->toArray();

        $inserts = [];
        $tmp = [];
        foreach($email_arr as $e){
            if (!in_array($e, $email_blacklists) && !in_array($e, $tmp)) {
                $inserts[] = [
                    'email' => $e,
                    'company_id' => Auth::user()->company_id,
                    'site_id' => $site->id,
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s"),
                ];
                $tmp[] = $e;
            }
        }

        DB::table('mail_blacklists')->insert($inserts);

        return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'success', 'msg' => '作成しました']);
    }

    public function delete (Request $request)
    {
        $mail_b = MailBlacklist::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$mail_b) {
            return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'error', 'msg' => 'ワードが存在しません']);
        }

        $mail_b->delete();

        return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function delete_all()
    {
        $mail_b = MailBlacklist::where('company_id', Auth::user()->company_id);
        $mail_b->delete();

        return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function import (Request $request)
    {
        $data = CSVImport::import_csv($request, ['email']);
        if ($data == 'not_include_require_columns') {
            return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：必須カラムが含まれていません']);
        } else if ($data == 'not_valid_type') {
            return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：ファイルがUTF-8かShift-JISではありません']);
        }

        $site = Site::where('company_id', Auth::user()->company_id)->first();

        // CSVファイル自体の重複と、既存の登録単語との重複を除く
        $emails = MailBlacklist::where('company_id', Auth::user()->company_id)->pluck('email')->toArray();
        $tmp = [];
        $unique_data = [];
        foreach ($data as $d){
            if (!in_array($d['email'], $tmp) && !in_array($d['email'], $emails)) {
                $tmp[] = $d['email'];
                $unique_data[] = $d;
            }
        }

        // 配列加工
        $inserts = [];
        foreach($unique_data as $d) {
            $insert = [];
            foreach($d as $k => $v) {
                $insert[$k] = $v;
            }
            $insert['company_id'] = Auth::user()->company_id;
            $insert['site_id'] = $site->id;
            $insert['created_at'] = date("Y-m-d H:i:s");
            $insert['updated_at'] = date("Y-m-d H:i:s");

            $inserts[] = $insert;
        }

        // バルクインサート
        DB::table('mail_blacklists')->insert($inserts);
        return redirect('/reviews/mails/blacklist')->with('flash_message', ['type' => 'success', 'msg' => 'インポートしました']);
    }

    public function _export ($mails)
    {
        $arr = $mails->get()->map(function($w) {return $w->email;})->all();
        array_unshift($arr, 'email');
        return implode("\n", $arr);
    }




}
