<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\ReviewSetting;
use App\Site;


class ReviewSettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $rs = ReviewSetting::where('company_id', Auth::user()->company_id)->first();
        if (!$rs) {
            $rs = new ReviewSetting();
            $rs->set_default();
        }

        return view('review_setting/index', compact('rs'));
    }

    public function create (Request $request)
    {
        $arr = $request->all();

        if ($request->id) {
            // 更新
            $rs = ReviewSetting::where('company_id', Auth::user()->company_id)->first();
            unset($arr['id']);
            $rs->fill($arr);
        } else {
            // 作成
            $arr = $request->all();
            $rs = new ReviewSetting();
            $rs->set_default();
            $rs->fill($arr);
        }
        $rs->company_id = Auth::user()->company_id;
        $rs->site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;
        $rs->save();

        return redirect('/reviews/settings')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }




}
