<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\InstagramSecondUsageRequest;
use App\Instagram;
use Session;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Aws\S3\S3Client;
use Aws;

class InstagramSecondUsageRequestController extends Controller
{
  public function __construct()
  {
    $this->middleware('auth');
  }

  public function index (Request $request)
  {
    // Update second usage request
    if ((int)$request->input('type') == 1) {
      $this->update_second_usage_request(1);
    } else {
      $this->update_second_usage_request(0);
    }

    // Get requests of current user without status is confirmed
    $second_usage_requests = InstagramSecondUsageRequest::where([
      ['company_id', '=', Auth::user()->company_id],
      ['user_id', '=', Auth::user()->id],
      ['is_expired', '=', 0],
      ['status', '!=', 4]
    ]);

    $data_status = $request->input('status');
    $status = -1;
    if ($data_status != null) {
      $status = (int)$data_status;
      $second_usage_requests = $second_usage_requests->where('status', (int)$status);
    }

    $instagram_second_usage_requests = $second_usage_requests->orderBy('created_at', 'desc')->paginate(10);

    return view('instagram_second_usage_request/index', compact('instagram_second_usage_requests', 'status'));
  }

  public function new (Request $request)
  {
    // Instagramをhiddenで引き回ししたくないのでセッションに入れる
    Session::put('instagram_second_usage_request', $request->instagram);
    $instagrams = $request->instagram;

    $su = new InstagramSecondUsageRequest();
    $su->set_default();

    return view('instagram_second_usage_request/new', compact('su', 'instagrams'));
  }

  public function confirm (Request $request)
  {
    if (Session::exists('instagram_second_usage_request')) {
      $instagrams = Session::get('instagram_second_usage_request');
    } else {
      return redirect('instagram')->with('flash_message', '既にリクエスト登録しています');
    }

    $arr = $request->all();
    $su = new InstagramSecondUsageRequest($arr);

    return view('instagram_second_usage_request/confirm', compact('su', 'instagrams'));
  }

  public function create (Request $request)
  {
    $instagrams = Session::get('instagram_second_usage_request');

    DB::transaction(function() use ($instagrams, $request) {
      foreach($instagrams as $ig){
        // ig_id, company_idで重複していた場合更新される
        $su = InstagramSecondUsageRequest::where('company_id', Auth::user()->company_id)
          ->where('user_id', Auth::user()->id)
          ->where('ig_id', $ig['id'])->first();
        if (!$su) {
          $su = new InstagramSecondUsageRequest();
        }

        $su->company_id = Auth::user()->company_id;
        $su->user_id = Auth::user()->id;
        $su->comment = $request->comment;
        $su->answer_hashtag = '#'. $request->answer_hashtag;
        $su->account_name = $request->account_name;
        $su->ig_id = $ig['id'];
        $su->permalink = $ig['permalink'];
        $su->image = $request->thumbnail_url;
        $su->content = $ig['content'];
        $su->caption = $ig['content'];
        $su->media_url = $request->thumbnail_url;
        $su->media_type = $ig['is_movie'] == "true" ? 'VIDEO' : 'IMAGE';
        $su->save();
      }

      Session::forget('instagram_second_usage_request');
    });

    return redirect('instagram_second_usage_requests')->with('flash_message', '更新しました');
  }

  public function update_status(Request $request)
  {
    $second_usage_request = InstagramSecondUsageRequest::where('id', $request['id'])->first();
    $second_usage_request->status = $request['status'];
    $second_usage_request->save();

    return ['result'=> 'success'];
  }

  private function update_second_usage_request ($type)
  {
    define('API_KEY', '0fb411b4b1mshde90ca43e809b06p1222e5jsncb2db690144e');
    define('API_HOST', 'instagram-api-20231.p.rapidapi.com');
    define('API_URL', 'https://instagram-api-20231.p.rapidapi.com/api/media_comments_from_shortcode/');
    define('CHILD_COMMENT_API_URL', 'https://instagram-api-20231.p.rapidapi.com/api/media_child_comments/3199683610357555036/');

    $opts = [
      "http" => [
        "method" => "GET",
        "header" => "X-RapidAPI-Key: ". API_KEY. "\r\n"."X-RapidAPI-Host: ". API_HOST. "\r\n"
      ]
    ];

    // DOCS: https://www.php.net/manual/en/function.stream-context-create.php
    $context = stream_context_create($opts);

    // Update status request greater than 3 days
    /*
      0: Not expired
      1: Expired
    */
    $valid_second_usage_requests = InstagramSecondUsageRequest::where('company_id', Auth::user()->company_id)
      ->where('user_id', Auth::user()->id)->where('is_expired', 0)->get();

    // fix for test.
    $before_three_days = date('Y-m-d H:i:s', strtotime('-30 day'));
    $before_12_hours = date('Y-m-d H:i:s', strtotime('-12 hour'));
    $expired_days = date('Y-m-d H:i:s', strtotime('-7 day'));

    foreach($valid_second_usage_requests as $request) {
      $created_date = date('Y-m-d H:i:s', strtotime($request->created_at));
      $last_requested_at = date('Y-m-d H:i:s', strtotime($request->last_requested_at));

      /*
        Type 0: Auto update
        Type 1: Manual update
      */
      if ($type == 0) {
        $is_expired = true;
        $excute_update = $last_requested_at < $before_12_hours;
      } elseif ($type == 1) {
        $excute_update = true;
        $is_expired = $created_date > $expired_days;
      }

      if ($created_date < $before_three_days) {
        // Check request expired, request created longer than 3 days ago
        $request->is_expired = 1;
        $request->status = 4;
        $request->save();
      } elseif ($request->status == 2 && $excute_update && $is_expired && $request->permalink != 'undefined') {
        /* Request status is commented and haven't requested within the last 12 hours
          Get comments from Instagrams and update request status if confirmed
          Lists request status:
          0: created
          1: cannot comment
          2: commented
          3: confirmed
          4: no reply
        */
        $permalink_data = explode('/p/', $request->permalink);
        $media_id = str_replace('/', '', $permalink_data[1]);

        // Open the file using the HTTP headers set above
        // DOCS: https://www.php.net/manual/en/function.file-get-contents.php
        $json_data = file_get_contents(API_URL. $media_id, false, $context);
        $json_decode_data = json_decode($json_data, true);
        if ($json_decode_data != null && $json_decode_data["status"] == "ok") {
          $data = $json_decode_data["data"];
          $comments = $data["comments"];
          $author_name = $data["caption"]["user"]["username"];
          if (count($comments) > 0) {
            foreach($comments as $comment) {
              if ($comment["user"]["username"] == $request->account_name && $comment["child_comment_count"] > 0) {
                $json_child_data = file_get_contents(CHILD_COMMENT_API_URL. $comment["pk"], false, $context);
                $json_decode_child_data = json_decode($json_child_data, true);

                $child_comments = $json_decode_child_data["data"]["child_comments"];
                foreach($child_comments as $child_comment) {
                  if ($child_comment["user"]["username"] == $author_name && Str::contains($child_comment["text"], $request->answer_hashtag)) {
                    $request->status = 3;
                  }
                }
              }
            }
            $request->last_requested_at = Carbon::now();
            $request->save();
          }
        }
      }
    }
  }

  public function s3_upload_request_data(Request $request) {
    $credentials = new Aws\Credentials\Credentials(
      config('app.aws_r2_access_key_id'),
      config('app.aws_r2_secret_access_key')
    );

    $endpoint = config('app.CLOUDFLARE_R2_ENDPOINT');

    $s3_client = new S3Client([
      'credentials' => $credentials,
      'endpoint' => $endpoint,
      'region' => 'auto',
      'version' => 'latest'
    ]);

    $ext = $request->is_movie == "true" ? 'mp4' : 'jpg';
    $local_filepath = storage_path() . "/app/public/tmp/{$request->ig_id}.{$ext}";
    $tmp = @file_get_contents($request->image_url);
    $time = time();
    $thumbnail_url = config('app.CLOUDFLARE_R2_URL').Auth::user()->company_id.'/'.$time.'.'.$ext;
    if (!$tmp){
      return ['status' => false, 'message' => "{$request->image_url} could not download"];
    }

    $fp = fopen($local_filepath, 'w');
    fwrite($fp, $tmp);
    fclose($fp);

    // S3にアップロード
    try {
      $result = $s3_client->putObject([
        'Bucket' => config('app.aws_r2_bucket'),
        'Key' => Auth::user()->company_id.'/'.$time.'.'.$ext,
        'SourceFile' => $local_filepath,
      ]);
    } catch (\Exception $e) {
      dd($e->getMessage());
      return ['status' => false, 'message' => $e->getMessage()];
    }

    return ['status' => true, 's3_image_url' => $result['ObjectURL'], 'thumbnail_url' => $thumbnail_url];
  }
}
