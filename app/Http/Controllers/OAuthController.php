<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class OAuthController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $app_id = config('services.ig')['app_id'];
        $fv = config('app.facebook_version');
        return view('oauth/index', compact('app_id', 'fv'));
    }

    public function redirect (Request $request)
    {
        $at = $request->input('access_token');
        $uid = $request->input('uid');
        $fv = config('app.facebook_version');

        $context = stream_context_create(['http' => ['ignore_errors' => true]]);

        // IG_BUSINESS_ACOUNT_IDまでの取得
        $access_token_url = "https://graph.facebook.com/v{$fv}/{$uid}/accounts?access_token={$at}";
        $contents = file_get_contents($access_token_url, false, $context);
        $result = json_decode($contents, true);

        if ((!array_key_exists('data', $result) && !array_key_exists('id', $result)) || count($result['data']) == 0) {
            Log::info('instagram-error1:' . $access_token_url);
            return redirect('oauth/index')->with(
                'flash_message', '認証エラーが発生しました：' . 'Facebookログインは出来ましたが、ページを取得できませんでした'
            );
        }

        if (array_key_exists('id', $result)) {
            $page_id = $result['id'];
        } else {
            $page_id = $result['data'][0]['id'];
        }

        $url = "https://graph.facebook.com/v{$fv}/{$page_id}?fields=instagram_business_account&access_token={$at}";
        $contents = file_get_contents($url, false, $context);
        $result = json_decode($contents, true);

        if (!array_key_exists('instagram_business_account', $result)) {
            Log::info('instagram-error2:' . $url);
            return redirect('oauth/index')->with(
                'flash_message', '認証エラーが発生しました：' . 'Facebookページを取得しましたが、インスタグラムビジネスアカウントを取得できませんでした'
            );
        }

        $ig_business_id = $result['instagram_business_account']['id'];

        // user_idはuserテーブルに格納
        $company = Auth::user()->company_tied;
        $company->ig_business_account_id = $ig_business_id;

        // 長期アクセストークンと交換
        $contents = $this->_get_long_at($at);
        if (!$contents) {
            return redirect('oauth/index')->with('flash_message', '認証エラーが発生しました：' . '長期アクセストークンの取得に失敗しました');
        }
        $result = json_decode($contents, true);

        // 長期アクセストークンをテーブルに格納
        $now = new Carbon('now');
        $company->access_token = $result['access_token'];
        if (array_key_exists('expires_in', $result) === true) {
            $expires_in = $now->addSeconds($result['expires_in']);
            $company->access_token_expires_in = $expires_in;
        }
        $company->save();

        return redirect('oauth/index')->with('flash_message', '認証に成功しました'. $url);

    }

    public function _get_long_at($at){
        $client_secret = config('services.ig')['app_secret'];
        $client_id = config('services.ig')['app_id'];
        $access_token_url = "https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&set_token_expires_in_60_days=true&client_id={$client_id}&client_secret={$client_secret}&fb_exchange_token={$at}";
        $contents = file_get_contents($access_token_url);
        return $contents;
    }

    public function refresh_token(Request $request) {
        $company = Auth::user()->company_tied;
        $at = $company->access_token;
        $client_secret = config('services.ig')['app_secret'];
        $client_id = config('services.ig')['app_id'];
        $access_token_url = "https://graph.facebook.com/oauth/access_token?grant_type=fb_exchange_token&set_token_expires_in_60_days=true&client_id={$client_id}&client_secret={$client_secret}&fb_exchange_token={$at}";
        $contents = file_get_contents($access_token_url);
        $result = json_decode($contents, true);

        // 長期アクセストークンをテーブルに格納
        $now = new Carbon('now');
        $company->access_token = $result['access_token'];
        if (array_key_exists('expires_in', $result) === true) {
            $expires_in = $now->addSeconds($result['expires_in']);
            $company->access_token_expires_in = $expires_in;
        }
        $company->save();

        return $result['access_token'];
    }
}
