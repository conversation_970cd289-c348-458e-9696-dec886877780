<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;
use App\Group;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Session;


class MyPageController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $company  = Auth::user()->company_tied;

        if(!$company){

            $rows = 0;

        }else{

            $rows = [
                'ロゴ' => $company->image_url,
                '企業名・ブランド名' => $company->name,
                '企業名・ブランド名（カナ）' => $company->name_kana,
                '部署' => $company->department_name,
                '役職' => $company->potision,
                '担当者名' => $company->person_in_charge,
                '担当者名（カナ）' => $company->person_in_charge_kana,
                '郵便番号' => $company->display_address()['post'],
                '住所' => $company->display_address()['city'],
                '企業サイトURL' => $company->url,
                'メールアドレス' => $company->email,
                '電話番号' => $company->phone_code,
            ];

        }


        return view('mypage/index', compact('rows'));
    }



}
