<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\ReviewWidget;
use App\ReviewWidgetTag;
use App\ReviewWidgetInclude;
use App\ReviewWidgetExclude;
use App\Site;
use App\Company;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReviewWidgetController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $review_widgets = ReviewWidget::where('company_id', Auth::user()->company_id);
        $user_id = Auth::user()->id;
        $user_qid = Auth::user()->qid();
        $company_id = Auth::user()->company_id;

        return view('review_widget/index', compact('review_widgets', 'user_id', 'user_qid', 'company_id'));
    }

    public function search (Request $request)
    {
        $company_id = Auth::user()->company_id;
        $company_name = Company::select('name')->where('id', $company_id)->first();
    
        // 必要な集計値だけをサブクエリで取得
        $review_widgets = ReviewWidget::where('company_id', $company_id)
            ->select('review_widgets.*')
            ->withCount(['review_widget_reviews as publishedReviewCount' => function($q) {
                $q->whereHas('review', function($q2) {
                    $q2->where('is_verified', 1)->where('published', 1);
                });
            }])
            ->withCount(['review_widget_reviews as totalReviewCount' => function($q) {
                $q->whereHas('review', function($q2) {
                    $q2->where('is_verified', 1);
                });
            }])
            ->with(['review_widget_reviews' => function($q) {
                $q->whereHas('review', function($q2) {
                    $q2->where('is_verified', 1)->where('published', 1);
                })->select('review_widget_id', 'review_id');
            }])
            ->get();
    
        $res_data = [];
        foreach($review_widgets as $rw){
            $published_review_count = $rw->publishedReviewCount;
            $total_review_count = $rw->totalReviewCount;

            // レビュー件数表示設定がONの場合は全レビュー数、OFFの場合は公開レビュー数を表示
            $display_review_count = ($rw->is_review_count && $total_review_count > 0) ? $total_review_count : $published_review_count;

            $rating = 0;
            if($published_review_count > 0 && $rw->is_rich_snippet == 1){
                // 平均評価は常に公開レビューのみで計算（非公開レビューは評価に影響させない）
                $star_sum = \App\Review::whereIn('id', $rw->review_widget_reviews->pluck('review_id'))
                    ->where('published', 1)
                    ->sum('ratings_of_review');
                $rating = $star_sum > 0 ? floor($star_sum / $published_review_count * 10) / 10 : 0;
            }
            $tmp = [
                'reviewCount' => $display_review_count,
                'rating' => $rating,
                'company_name' => $company_name->name,
                'is_auto_update_snippet' => $rw->is_auto_update_snippet,
            ];
            $res_data[] = array_merge($rw->toArray(), $tmp);
        }
    
        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => count($res_data),
            "sort" => "asc",
            "field" => "id"
        ];
    
        return ['meta' => $meta, 'data' => $res_data];
    }

    public function new (Request $request)
    {
        $rw = new ReviewWidget();
        $rw->set_default();
        // 空のコレクション作成
        $rw_tags = ReviewWidgetTag::where('company_id', 0)->get();
        $rw_includes = ReviewWidgetInclude::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->get();
        $rw_excludes = ReviewWidgetExclude::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->get();

        return view('review_widget/new', compact('rw', 'rw_tags', 'rw_includes', 'rw_excludes'));
    }

    public function edit (Request $request)
    {
        $rw = ReviewWidget::where('company_id', Auth::user()->company_id)->where('id', $request->id)->first();
        $rw_tags = ReviewWidgetTag::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->get()->sortBy('sort_order');
        $rw_includes = ReviewWidgetInclude::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->get();
        $rw_excludes = ReviewWidgetExclude::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->get();

        return view('review_widget/new', compact('rw', 'rw_tags', 'rw_includes', 'rw_excludes'));
    }

    public function create (Request $request)
    {
        $arr = $request->all();
        if ($request->has('bottom_right_text')) {
            $arr['bottom_right_text'] = $request->input('bottom_right_text');
        } else {
            unset($arr['bottom_right_text']);
        }
        $site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;

        if ($request->id) {
            // 更新
            $rw = ReviewWidget::where('company_id', Auth::user()->company_id)->where('id', $request->id)->first();
            
            // 「簡単設定」の場合、入力値をURLにセット、「複数設定」の場合はURLは引継ぎ
            if(isset($arr['url_setting_type']) && $arr['url_setting_type'] == 1){
                // URL設定が「簡単設定」の場合
                if(isset($arr["urls"]) && !empty($arr["urls"])){
                    $arr['url_easy'] = $arr['urls'][0]['include_url'];
                    unset($arr['urls']);
                    unset($arr['exclude_urls']);
                }else{
                    $arr['url_easy'] = null;
                }
            }elseif(isset($arr['url_setting_type']) && $arr['url_setting_type'] == 2){
                $arr['url_easy'] = $rw->url_easy;
                unset($arr['urls']);
                unset($arr['exclude_urls']);
            }else{
                unset($arr['urls']);
                unset($arr['exclude_urls']);
            }

            unset($arr['id']);
            unset($arr['tags']);
            $rw->fill($arr);
        } else {
            // 作成
            $arr = $request->all();
            $arr['is_auto_update_snippet'] = $request->input('is_auto_update_snippet', 0);

            // 「簡単設定」の場合、入力値をURLにセット、「複数設定」の場合はURLは引継ぎ
            if(isset($arr['url_setting_type']) && $arr['url_setting_type'] == 1){
                // URL設定が「簡単設定」の場合
                if(isset($arr["urls"]) && !empty($arr["urls"])){
                    $arr['url_easy'] = $arr['urls'][0]['include_url'];
                    unset($arr['urls']);
                    unset($arr['exclude_urls']);
                }else{
                    $arr['url'] = null;
                }
            }elseif(isset($arr['url_setting_type']) && $arr['url_setting_type'] == 2){
                unset($arr['urls']);
                unset($arr['exclude_urls']);
            }else{
                unset($arr['urls']);
                unset($arr['exclude_urls']);
            }
            $rw = new ReviewWidget();
            $rw->set_default();
            unset($arr['tags']);
            $rw->fill($arr);
        }
        $rw->company_id = Auth::user()->company_id;
        $rw->site_id = $site_id;
        $rw->custom_css = $rw->custom_css ?? '';
        $rw->save();

         // 「複数設定」の場合、URLをReviewWidgetIncludeに保存
         if($request['url_setting_type'] == 2){
            if(isset($request["urls"]) && !empty($request["urls"])){
                $review_widget_id = $rw->id;
                $company_id = Auth::user()->company_id;

                // 既存のURLを削除
                ReviewWidgetInclude::where('company_id', $company_id)
                            ->where('review_widget_id', $review_widget_id)
                            ->delete();

                foreach($request["urls"] as $i => $url){
                    if(isset($url['include_url']) && !empty($url['include_url'])){
                        $tmp = [
                            'company_id' => $company_id,
                            'review_widget_id' => $review_widget_id,
                            'include_url' => $url['include_url'],
                            'created_at' => date("Y-m-d H:i:s"),
                            'updated_at' => date("Y-m-d H:i:s"),
                        ];
                        ReviewWidgetInclude::create($tmp);
                    }
                }

            }else{
                $arr['url'] = null;
            }

            if(isset($request['exclude_urls']) && !empty($request['exclude_urls'])){
                $review_widget_id = $rw->id;
                $company_id = Auth::user()->company_id;

                // 既存のURLを削除
                ReviewWidgetExclude::where('company_id', $company_id)
                            ->where('review_widget_id', $review_widget_id)
                            ->delete();

                foreach($request["exclude_urls"] as $i => $exclude_url){
                    if(isset($exclude_url['exclude_url']) && !empty($exclude_url['exclude_url'])){
                        $tmp = [
                            'company_id' => $company_id,
                            'review_widget_id' => $review_widget_id,
                            'exclude_url' => $exclude_url['exclude_url'],
                            'created_at' => date("Y-m-d H:i:s"),
                            'updated_at' => date("Y-m-d H:i:s"),
                        ];
                        ReviewWidgetExclude::create($tmp);
                    }
                }
            }

        }

        // タグの保存(delete insert)
        if (!empty($request->tags)) {
            ReviewWidgetTag::where('company_id', Auth::user()->company_id)->where('review_widget_id', $rw->id)->delete();
            $result = [];
            $tags = array_filter($request->tags, function($v) { return !empty($v['keyword']); });
            foreach($tags as $i => $t) {
                $word = $t['keyword'];
                $tmp = [
                    'company_id' => Auth::user()->company_id,
                    'site_id' => $site_id,
                    'keyword' => $word,
                    'sort_order' => $i,
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s"),
                    'review_widget_id' => $rw->id
                ];
                $result[] = $tmp;
            }
            // バルクインサート
            DB::table('review_widget_tags')->insert($result);
        }

        return redirect('/reviews/widgets/edit/' . $rw->id)->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function delete (Request $request)
    {
        $rs = ReviewWidget::where('company_id', Auth::user()->company_id)->where('id', $request->id);
        if($rs->count() == 1) {
            $rs->delete();
        }
        ReviewWidgetTag::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->delete();

        ReviewWidgetInclude::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->delete();
        ReviewWidgetExclude::where('company_id', Auth::user()->company_id)->where('review_widget_id', $request->id)->delete();

        return redirect('/reviews/widgets')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }


}
