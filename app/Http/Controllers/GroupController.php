<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Group;
use App\Instagram;
use App\InstagramGroup;
use App\InstagramProduct;
use App\Product;
use App\Indicate;
use App\GroupCustomOrder;
use App\GroupAbTest;
use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;



class GroupController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $groups = Group::with('instagram_groups')->where('company_id', Auth::user()->company_id)->get();

        return view('group/index', compact('groups'));
    }

    public function new ()
    {
        $group = new Group();
        // デフォルト値の設定
        $group->set_default();
        $is_edit = false;
        $ab_target_groups = Group::where('company_id', Auth::user()->company_id)->where('is_active', 1)->get();

        return view('group/new', compact('group', 'is_edit', 'ab_target_groups'));
    }

    public function testpage (Request $request)
    {
        $device = $request->device ? $request->device : 'sd';
        $qid = $request->qid;
        $display_type = Group::where('company_id', Auth::user()->company_id)->where('qid', $qid)->first()->display_type;

        return view('group/test', compact('device', 'qid', 'display_type'));
    }

    public function edit (Request $request)
    {
        $group = Group::where('company_id', Auth::user()->company_id)->where('id', $request->id)->first();
        $is_edit = true;
        $ab_target_groups = Group::where('company_id', Auth::user()->company_id)->where('is_active', 1)->whereNotIn('id', [$group->id])->get();

        return view('group/new', compact('group', 'is_edit', 'ab_target_groups'));
    }

    // グループの作成
    public function create (Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
        ]);

        // 管理画面での設定を超過していないかチェック
        $group_count = Group::where('company_id', Auth::user()->company_id)->count();
        $limit = Auth::user()->company_tied ? Auth::user()->company_tied->get_ugc_group_limit() : INF;
        if ($limit < $group_count) {
            return redirect('/group')->with('flash_message', '登録出来ませんでした。制限を超えています(' . $group_count . '/' . $limit . ')');
        }

        if ($request->file('header_image')) {
            $path = $request->file('header_image')->store('public/header_images');
            $path = explode('/', $path)[2];
        } else {
            $path = $request['header_image'];
        }

        try {
            DB::transaction(function() use ($request, $path) {
                $bottom_right_text = '';
                if ($request->has('bottom_right_text')) {
                    $bottom_right_text = $request->input('bottom_right_text');
                }
                $group = Group::create([
                    'company_id' => Auth::user()->company_id,
                    'name' => $request['name'],
                    'url' => $request['url'],
                    'display_type' => $request['display_type'],
                    'display_style' => $request['display_style'],
                    'is_display_comment' => $request['is_display_comment'],
                    'bottom_text_align' => $request['bottom_text_align'],
                    'is_display_report_mark' => $request['is_display_report_mark'],
                    'instagram_mark_style' => $request['instagram_mark_style'],
                    'is_use_border' => $request['is_use_border'],
                    'header_image' => $path,
                    'main_color' => $request['main_color'],
                    'sd_short' => $request['sd_short'],
                    'sd_wide' => $request['sd_wide'],
                    'pc_short' => $request['pc_short'],
                    'pc_wide' => $request['pc_wide'],
                    'more_text' => $request['more_text'],
                    'modal_background_color' => $request['modal_background_color'],
                    'qid' => 'qid',
                    'is_auto_play_video' => $request['is_auto_play_video'],
                    'comment_text_color' => $request['comment_text_color'],
                    'is_display_date' => $request['is_display_date'],
                    'border_style' => $request['border_style'],
                    'border_color' => $request['border_color'],
                    'is_use_background_color' => $request['is_use_background_color'],
                    'background_color' => $request['background_color'],
                    'bottom_right_text' => $bottom_right_text,
                    'bottom_right_text_option' => $request['bottom_right_text_option'],
                    'is_fixed_display' => $request['is_fixed_display'],
                    'is_display_inside_chatbot' => $request['is_display_inside_chatbot'],
                ]);

                $group->update(['qid' => md5($group->id . 'group')]);
            });
        } catch (Throwable $e) {
            return redirect('/group')->with('flash_message', '作成に失敗しました');
        }

        return redirect('/group')->with('flash_message', '作成しました');
    }

    // グループの更新
    public function update (Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
        ]);
        if ($request->file('header_image')) {
            $path = $request->file('header_image')->store('public/header_images');
            $path = explode('/', $path)[2];
        } else {
            $path = $request['header_image'];
        }

        try {
            DB::transaction(function() use ($request, $path) {
                $group = Group::where('company_id', Auth::user()->company_id)->find($request->id);
                $prev_header_image = $group->header_image;
                $bottom_right_text = $group->bottom_right_text;
                if ($request->input('bottom_right_text_option') == 4 || !empty($request->input('bottom_right_text'))) {
                    $bottom_right_text = $request->input('bottom_right_text');
                }
                $group->update([
                    'name' => $request['name'],
                    'url' => $request['url'],
                    'display_type' => $request['display_type'],
                    'display_style' => $request['display_style'],
                    'is_display_comment' => $request['is_display_comment'],
                    'bottom_text_align' => $request['bottom_text_align'],
                    'is_display_report_mark' => $request['is_display_report_mark'],
                    'instagram_mark_style' => $request['instagram_mark_style'],
                    'is_use_border' => $request['is_use_border'],
                    'header_image' => $path,
                    'main_color' => $request['main_color'] == null ? "" : $request['main_color'],
                    'sd_short' => $request['sd_short'],
                    'sd_wide' => $request['sd_wide'],
                    'pc_short' => $request['pc_short'],
                    'pc_wide' => $request['pc_wide'],
                    'more_text' => $request['more_text'],
                    'modal_background_color' => $request['modal_background_color'],
                    'is_auto_play_video' => $request['is_auto_play_video'],
                    'comment_text_color' => $request['comment_text_color'],
                    'is_display_date' => $request['is_display_date'],
                    'border_style' => $request['border_style'],
                    'border_color' => $request['border_color'],
                    'is_use_background_color' => $request['is_use_background_color'],
                    'background_color' => $request['background_color'],
                    'bottom_right_text' => $bottom_right_text,
                    'bottom_right_text_option' => $request['bottom_right_text_option'],
                    'is_fixed_display' => $request['is_fixed_display'],
                    'is_display_inside_chatbot' => $request['is_display_inside_chatbot'],
                ]);

                if ($request->file('header_image')) {
                    Storage::delete('public/header_images/' . $prev_header_image);
                }
            });
        } catch (Throwable $e) {
            return redirect('/group')->with('flash_message', '更新に失敗しました');
        }

        return redirect('/group')->with('flash_message', '更新しました');
    }

    // グループのコピー
    public function copy ($id)
    {
        $group = Group::where('company_id', Auth::user()->company_id)->where('id', $id)->first();

        if (!$group) {
            return redirect('/group')->with('flash_message', '指定されたグループは存在しません');
        }

        try{
            $instagram_ids = [];

            $new_group = $group->replicate();
            $new_group->name = $new_group->name . 'のコピー';
            $new_group->save();
            $new_group->qid = md5($new_group->id . 'group');
            $new_group->save();

            $igs = $group->instagrams;
            foreach($igs as $ig) {
                $new_instagram = $ig->replicate();
                $new_instagram->save();

                $new_instagram->ig_id = 'cp-' . $ig->ig_id . '-' . time();
                $new_instagram->save();

                $gco = GroupCustomOrder::where('group_id', $group->id)->where('instagram_id', $ig->id)->first();
                $sort_id = $gco ? $gco->sort_id : 0;
                $instagram_ids[] = [
                    'id' => $new_instagram->id,
                    'sort_id' => $sort_id
                ];

                $instagram_group = new InstagramGroup();
                $instagram_group->group_copy($new_group->id, $new_instagram->id);
            }

            if($group->display_order == 'custom') {
                foreach($instagram_ids as $id) {
                    $new_gco = new GroupCustomOrder;
                    $new_gco->instagram_id = $id['id'];
                    $new_gco->group_id = $new_group->id;
                    $new_gco->sort_id = $id['sort_id'];
                    $new_gco->save();
                }
            }
        } catch (Throwable $e) {
            return redirect('/group')->with('flash_message', 'コピーに失敗しました');
        }


        return redirect('/group')->with('flash_message', 'コピーしました');
    }

    // グループの削除
    public function delete ($id)
    {
        // 削除対象の権限があるかチェック
        $group = Group::find($id);

        if ($group->company_id != Auth::user()->company_id) {
            return redirect('/group')->with('flash_message', '削除できませんでした（権限なし）');
        }

        $group->delete();

        return redirect('/group')->with('flash_message', '削除しました');
    }

    // 個別インスタグラム投稿の削除
    public function ig_delete ($instagram_id, $group_id)
    {
        // 削除対象の権限があるかチェック
        $group = Group::find($group_id);

        if ($group->company_id != Auth::user()->company_id) {
            return redirect('/group')->with('flash_message', '削除できませんでした（権限なし）');
        }

        $instagram = Instagram::where('id', $instagram_id)->where('company_id', Auth::user()->company_id)->first();
        if(!$instagram) {
            return redirect('/group')->with('flash_message', '削除できませんでした');
        }

        // グループとの紐づけ削除
        $ig = InstagramGroup::where('group_id', $group_id)->where('instagram_id', $instagram->id);
        $ig->delete();
        $gco = GroupCustomOrder::where('group_id', $group_id)->where('instagram_id', $instagram->id);
        $gco->delete();

        // 商品との紐づけ削除
        $igp = InstagramProduct::where('instagram_id', $instagram->id)->where('company_id', Auth::user()->company_id);
        $igp->delete();

        return redirect('/group/show/' . $group_id)->with('flash_message', '削除しました');
    }

    // ダッシュボード
    public function show ($id)
    {
        $group = Group::where('company_id', Auth::user()->company_id)->where('id', $id)->first();
        if (!$group) {
            return redirect('/group')->with('flash_message', '指定されたグループは存在しません');
        }

        $igs = (new Group())->get_ig_from_group_id($id);
        $ig_ids = $igs->map(function($v){ return $v->ig_id; });
        $products = Product::where('company_id', Auth::user()->company_id)->get();

        return view('group/show', compact('group', 'igs', 'products'));
    }

    // ダッシュボード（ABテスト）
    public function show_abtest ($id)
    {
        $group = Group::where('company_id', Auth::user()->company_id)->where('id', $id)->first();
        if (!$group) {
            return redirect('/group')->with('flash_message', '指定されたグループは存在しません');
        }
        $abtests = GroupAbTest::where('company_id', Auth::user()->company_id)->where('group_id', $id)->orderBy('created_at', 'desc')->get();
        $group_like_count[] = [];
        $group_like_count[0] = 0;
        $group_like_count[$id] = $group->instagrams->sum('like_count');

        $results = [];
        foreach($abtests as $abtest) {
            $tmp = DB::table('groups')
            ->select(DB::raw("groups.id as group_id, groups.name as group_name, sum(case when indicator_type = 'look' then 1 else 0 end) as look, sum(case when indicator_type = 'click' then 1 else 0 end) as click, sum(case when indicator_type = 'cv' then 1 else 0 end) as cv, sum(case when indicator_type = 'group_look' then 1 else 0 end) as group_look"))
            ->leftjoin('indicators', 'indicators.group_id', '=', 'groups.id')
            ->whereIn('groups.id', [$id, $abtest->target_group_id]);

            if (!empty($abtest->end_at)){
                $tmp = $tmp->whereBetween('indicators.created_at', [$abtest->start_at, $abtest->end_at]);
            } else {
                $tmp = $tmp->where('indicators.created_at', '>=', $abtest->start_at);
            }

            // 順番はAグループを必ず上に
            $tmp = $tmp->groupBy('groups.id')
                ->orderByRaw("FIELD(groups.id, {$id}, {$abtest->target_group_id})");
            $data = $tmp->get();

            $groups = [
                Group::where('id', $abtest->group_id)->first(),
                Group::where('id', $abtest->target_group_id)->first()
            ];
            if (isset($groups[1])) {
                $group_like_count[$abtest->target_group_id] = $groups[1]->instagrams->sum('like_count');
            } else {
                $group_like_count[$abtest->target_group_id] = 0;
            }

            // データが無かったら０で埋める
            $result = [];
            foreach([0, 1] as $i) {
                if (!$data->slice($i, 1)->isEmpty()) {
                    $result[$i] = [
                        'group_id' => $data[$i]->group_id,
                        'group_name' => $data[$i]->group_name,
                        'group_look' => $data[$i]->group_look,
                        'look' => $data[$i]->look,
                        'click' => $data[$i]->click,
                        'cv' => $data[$i]->cv,
                    ];
                } else {
                    $result[$i] = [
                        'group_id' => isset($groups[$i]) ? $groups[$i]->id : 0,
                        'group_name' => isset($groups[$i]) ? $groups[$i]->name : '-',
                        'group_look' => 0,
                        'look' => 0,
                        'click' => 0,
                        'cv' => 0,
                    ];
                }
            }

            $results[] = $result;
        }

        return view('group/show_abtest', compact('group', 'results', 'abtests', 'group_like_count'));
    }

    // 期間指定で指標データを返す
    public function indicate (Request $request)
    {
        $group_id = $request['group_id'];
        $date_start =  $request['date_start'];
        $date_end =  $request['date_end'];

        $tmp = DB::table('indicators')
            ->select(DB::raw('instagram_id, ig_id, indicator_type, count(*) as ind_count'))
            ->join('instagrams', 'indicators.instagram_id', '=', 'instagrams.id')
            ->where('instagrams.company_id', Auth::user()->company_id);

        if ($request['group_id']) {
            $tmp = $tmp->where('group_id', $group_id);
        }

        if ($date_start != "") {
            $tmp = $tmp->where('indicators.created_at', '>=', $date_start);
        }

        if ($date_end != "") {
            $tmp = $tmp->where('indicators.created_at', '<=', $date_end . ' 23:59:59');
        }

        $tmp->groupBy('indicator_type', 'ig_id', 'instagram_id');

        return $tmp->get();

    }

    public function bulk_import_ugcs_to_product(Request $request)
    {
        $product_id = $request['product_id'];
        $checked_igs =  $request['checked_igs'];

        $result = DB::transaction(function() use ($checked_igs, $product_id) {
            // instagramの詳細情報

            foreach($checked_igs as $ig){
                // instagram_productsにデータを格納
                $insta_group = InstagramProduct::updateOrCreate([
                    'company_id' => Auth::user()->company_id,
                    'instagram_id' => $ig,
                    'product_id' => $product_id
                ],[]);
            }
        });

        return ['status' => $result, 'msg' => '登録しました'];
    }

    // グループ一覧画面で保存ボタンを押したとき
    public function group_list_save(Request $request)
    {
        foreach($request['post_data'] as $a){
            $group = Group::where('id', $a['group_id'])->first();

            $prev_abtest_group_id = $group->abtest_group_id;

            $group->is_active = $a['is_active'];
            $group->abtest_group_id = $a['abtest'] == '' ? NULL : $a['abtest'];
            $group->save();

            // ABテストテーブルの更新
            if (!empty($group->abtest_group_id)) {
                if (empty($prev_abtest_group_id)) {
                    // 「指定なし」状態 -> 何か指定　にした場合
                    GroupAbTest::create([
                        'company_id' => Auth::user()->company_id,
                        'group_id' => $group->id,
                        'target_group_id' => $group->abtest_group_id,
                        'start_at' => date("Y-m-d H:i:s")
                    ]);
                } else {
                    // 何か指定 -> 別の何か指定　にした場合
                    if ($group->abtest_group_id != $prev_abtest_group_id) {
                        GroupAbTest::where([
                            'group_id' => $group->id,
                            'target_group_id' => $prev_abtest_group_id
                        ])->whereNull('end_at')->update([
                            'end_at' => date("Y-m-d H:i:s")
                        ]);
                        GroupAbTest::create([
                            'company_id' => Auth::user()->company_id,
                            'group_id' => $group->id,
                            'target_group_id' => $group->abtest_group_id,
                            'start_at' => date("Y-m-d H:i:s")
                        ]);
                    }
                }
            } else {
                // ABテストを「指定なし」にした場合
                if (!empty($prev_abtest_group_id)) {
                    // 何か指定されていた状態から「指定なし」にされていれば該当テーブルに修了時刻を入れる
                    GroupAbTest::where([
                        'group_id' => $group->id,
                        'target_group_id' => $prev_abtest_group_id
                    ])->whereNull('end_at')->update([
                        'end_at' => date("Y-m-d H:i:s")
                    ]);
                }
            }
        }

        return ['result'=> 'success'];
    }

    // UGCセット内ソート順の保存
    public function sort_save(Request $request)
    {
        $do = $request['display_order'];
        $group_id = $request['id'];

        $group = Group::find($group_id);
        $group->update(['display_order' => $do]);

        // 一度削除して更新
        if ($do == 'custom') {
            try {
                DB::transaction(function() use ($group, $request) {
                    GroupCustomOrder::where('group_id', $group->id)->delete();
                    foreach($request['sort_ids'] as $i => $sort_id){
                        if ($sort_id == '') continue;
                        $ins = Instagram::where('ig_id', $request['instagram_ids'][$i])->where('company_id', Auth::user()->company_id)->first();
                        $gco = new GroupCustomOrder();
                        $gco->sort_id = $sort_id;
                        $gco->group_id = $group->id;
                        $gco->instagram_id = $ins->id;
                        $gco->save();
                    }
                });
            } catch (Throwable $e) {
                return redirect('/group/show/' . $group_id)->with('flash_message', '更新に失敗しました');
            }
        }

        return redirect('/group/show/' . $group_id)->with('flash_message', '更新しました');
    }


}
