<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\LineAutoSetting;
use App\Site;
use Illuminate\Support\Facades\Log;

class LineScheduleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $ls = LineAutoSetting::where('company_id', Auth::user()->company_id)->first();
        if (!$ls) {
            $ls = new LineAutoSetting();
            $ls->set_default();
        }

        return view('line/schedule', compact('ls'));
    }

    public function create (Request $request)
    {
        $arr = $request->all();

        if ($request->id) {
            // 更新
            $ms = LineAutoSetting::where('company_id', Auth::user()->company_id)->first();
            unset($arr['id']);
            $ms->fill($arr);
        } else {
            // 作成
            $arr = $request->all();
            $ms = new LineAutoSetting();
            $ms->set_default();
            $ms->fill($arr);
        }

        // 画像アップロード
        foreach([1,2,3,4,5,6] as $i){
            $image_url_key = 'image_url' . $i;
            if (!empty($request['image_remove_' . $i])){
                $ms->$image_url_key = NULL;
            } else if ($request->file('image_' . $i)) {
                $path = $request->file('image_' . $i)->store('public/line_message_images');
                $path = explode('/', $path)[2];
                $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/line_message_images/' . $path;
                $ms->$image_url_key = $image_url;
            }
        }

        $ms->company_id = Auth::user()->company_id;
        $ms->site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;
        $ms->save();

        return redirect('/reviews/line/schedules')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }




}
