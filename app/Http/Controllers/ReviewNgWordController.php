<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Site;
use App\ReviewNgWord;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ReviewNgWordController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $ng_words = ReviewNgWord::where('company_id', Auth::user()->company_id);
        $for_export = $this->_export($ng_words);

        return view('review_ng_word/index', compact('ng_words', 'for_export'));
    }

    public function search (Request $request)
    {
        $res = $request->all();
        $ng_words = ReviewNgWord::where('company_id', Auth::user()->company_id);

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('status', $res2) && $res2['status'] != ''){
                if ($res2['status'] == '1') {
                    $ng_words->where('published', 1);
                } else if ($res2['status'] == '0'){
                    $ng_words->where('published', 0);
                }
            }

            if(array_key_exists('freeword', $res2) && $res2['freeword'] != ''){
                $word = $res2['freeword'];
                $ng_words->where('word', 'like', "%$word%");
            }
        }

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $ng_words->count(),
            "sort" => "asc",
            "field" => "id"
        ];
        $data = $ng_words->get()->toArray();

        return ['meta' => $meta, 'data' => $data];
    }

    public function create (Request $request)
    {
        $words = $request->words;
        $word_arr = explode(',', $words);
        $site = Site::where('company_id', Auth::user()->company_id)->first();

        // 重複を弾くためにすでに登録してある単語のリストを取得する
        $ng_words = ReviewNgWord::where('company_id', Auth::user()->company_id)->pluck('word')->toArray();

        $inserts = [];
        $tmp = [];
        foreach($word_arr as $w){
            if (!in_array($w, $ng_words) && !in_array($w, $tmp)) {
                $inserts[] = [
                    'word' => $w,
                    'published' => $request->published,
                    'company_id' => Auth::user()->company_id,
                    'site_id' => $site->id,
                    'created_at' => date("Y-m-d H:i:s"),
                    'updated_at' => date("Y-m-d H:i:s"),
                ];
                $tmp[] = $w;
            }
        }

        DB::table('review_ng_words')->insert($inserts);

        return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'success', 'msg' => '作成しました']);
    }

    public function update (Request $request)
    {
        $ng_word = ReviewNgWord::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$ng_word) {
            return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'error', 'msg' => 'ワードが存在しません']);
        }

        if (!empty($request->word)) {
            $ng_word->word = $request->word;
        }

        $ng_word->published = $request->published;
        $ng_word->save();

        return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function delete (Request $request)
    {
        $ng_word = ReviewNgWord::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$ng_word) {
            return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'error', 'msg' => 'ワードが存在しません']);
        }

        $ng_word->delete();

        return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function update_published (Request $request)
    {
        $ids = explode(',', $request->ids);
        $value = $request->published;
        $ng_words = ReviewNgWord::where('company_id', Auth::user()->company_id)->whereIn('id', $ids);
        $ng_words->update(['published' => $value]);

        return ['type' => 'success', 'msg' => '更新しました'];
    }

    public function delete_all()
    {
        $ng_words = ReviewNgWord::where('company_id', Auth::user()->company_id);
        $ng_words->delete();

        return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function import (Request $request)
    {
        $data = CSVImport::import_csv($request, ['word', 'published']);
        if ($data == 'not_include_require_columns') {
            return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：必須カラムが含まれていません']);
        } else if ($data == 'not_valid_type') {
            return redirect('/reviews')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：ファイルがUTF-8かShift-JISではありません']);
        }

        $site = Site::where('company_id', Auth::user()->company_id)->first();

        // CSVファイル自体の重複と、既存の登録単語との重複を除く
        $ng_words = ReviewNgWord::where('company_id', Auth::user()->company_id)->pluck('word')->toArray();
        $tmp = [];
        $unique_data = [];
        foreach ($data as $d){
            if (!in_array($d['word'], $tmp) && !in_array($d['word'], $ng_words)) {
                $tmp[] = $d['word'];
                $unique_data[] = $d;
            }
        }

        // 配列加工
        $inserts = [];
        foreach($unique_data as $d) {
            $insert = [];
            foreach($d as $k => $v) {
                $insert[$k] = $v;
            }
            $insert['company_id'] = Auth::user()->company_id;
            $insert['site_id'] = $site->id;
            $insert['created_at'] = date("Y-m-d H:i:s");
            $insert['updated_at'] = date("Y-m-d H:i:s");

            $inserts[] = $insert;
        }

        // バルクインサート
        DB::table('review_ng_words')->insert($inserts);
        return redirect('/reviews/ng_words')->with('flash_message', ['type' => 'success', 'msg' => 'インポートしました']);
    }

    public function _export ($ng_words)
    {
        $arr = $ng_words->get()->map(function($w) {return $w->word . ',' . $w->published;})->all();
        array_unshift($arr, 'word,published');
        return implode("\n", $arr);
    }

}
