<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Auth;
use Carbon\Carbon;

class TwoFactorAuthController extends Controller
{
    public function index ()
    {
        if (md5(Auth::user()->login_token) !== request()->login_token) {
            abort(404);
        }
        return view('auth.twofactorauth');
    }

    public function post ()
    {
        if (Auth::user()->login_token === request()->input('login_token')) {
            if (Auth::user()->login_expiration < Carbon::now()) {
                Auth::logout();

                return redirect('login')->with('error', '認証コードの期限が切れています。');
            } else {
                $user = Auth::user();
                $user->login_token = null;
                $user->login_expiration = null;
                $user->login_environment = request()->server->get('REMOTE_ADDR') . ':' . request()->server->get('HTTP_USER_AGENT');
                $user->save();

                return redirect('/')->with('success', 'ログイン環境を保存しました。');
            }
        } else {
            return redirect(route('auth.twofactorauth', md5(Auth::user()->login_token)))->with('error', '認証コードが間違っています。');
        }
    }
}
