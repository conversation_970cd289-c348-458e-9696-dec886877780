<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Auth;
use Mail;
use Config;
use App\User;
use App\Mail\TwoFactorAuthPassword;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    protected function authenticated(Request $request, $user)
    {
        Config::set('database.connections.mysql.database2', config('database.connections.mysql.database'));

        // ステータスが休止 or 解約の場合ログアウトする
        $company = $user->company_tied;
        if (!empty($company) && ($company->status_id == 2 || $company->status_id == 3)) {
            $this->logout($request);
        }

        // 二段階認証
        /*if (config('app.env') !== 'staging' && $user->is_admin() === false && $user->login_environment === null) {
            $random_password = '';

            for($i = 0 ; $i < 4 ; $i++) {
                $random_password .= strval(rand(0, 9));
            }

            $user = User::find(Auth::user()->id);
            $user->login_token = $random_password;
            $user->login_expiration = now()->addMinutes(10);
            $user->save();

            // メール送信
            Mail::to($user)->send(new TwoFactorAuthPassword($random_password));

            return redirect('/auth/twofactorauth/' . md5($random_password));
        }*/
        // メール障害により二段階認証ができないことによる代替措置(2022年10月5日)
        $user = User::find(Auth::user()->id);
        $user->login_token = null;
        $user->login_expiration = null;
        $user->login_environment = request()->server->get('REMOTE_ADDR') . ':' . request()->server->get('HTTP_USER_AGENT');
        $user->save();
    }
}
