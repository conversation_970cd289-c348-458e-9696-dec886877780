<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\ProductBlacklist;
use App\Product;
use App\Site;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ProductBlacklistController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $products = ProductBlacklist::where('company_id', Auth::user()->company_id);
        $product_master = Product::where('company_id', Auth::user()->company_id)
            ->whereNotIn('id', $products->pluck('product_id')->toArray())->get();
        $for_export = $this->_export($products);

        return view('product_blacklist/index', compact('products', 'for_export', 'product_master'));
    }

    public function search (Request $request)
    {
        $res = $request->all();
        $products = ProductBlacklist::where('company_id', Auth::user()->company_id);

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('daterange', $res2) && $res2['daterange'] != ''){
                $arr = explode('/', $res2['daterange']);
                $sd = trim($arr[0]);
                $ed = trim($arr[1]);
                $products = $products->whereBetween('created_at', [$sd, $ed . ' 23:59:59']);
            }

            if(array_key_exists('freeword', $res2) && $res2['freeword'] != ''){
                $word = $res2['freeword'];
                // 関連先の名前、商品IDで検索
                $products = $products->whereHas('product', function($query) use ($word) {
                    $query->where(function($query2) use ($word){
                        $query2->where('name', 'like', "%$word%")
                              ->orWhere('product_display_id', 'like', "%$word%");
                    });

                });
            }
        }

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $products->count(),
            "sort" => "asc",
            "field" => "id"
        ];
        $data = $products->get()->toArray();

        return ['meta' => $meta, 'data' => $data];
    }

    public function create (Request $request)
    {
        $site = Site::where('company_id', Auth::user()->company_id)->first();
        $pb = new ProductBlacklist();
        $pb->company_id = Auth::user()->company_id;
        $pb->site_id = $site->id;
        $pb->product_id = $request->product_id;
        $pb->save();

        return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'success', 'msg' => '作成しました']);
    }

    public function delete (Request $request)
    {
        $pb = ProductBlacklist::where('company_id', Auth::user()->company_id)->find($request->id);
        if(!$pb) {
            return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'error', 'msg' => 'ワードが存在しません']);
        }

        $pb->delete();

        return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function delete_all()
    {
        $pb = ProductBlacklist::where('company_id', Auth::user()->company_id);
        $pb->delete();

        return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'success', 'msg' => '削除しました']);
    }

    public function import (Request $request)
    {
        $data = CSVImport::import_csv($request, ['product_id']);
        if ($data == 'not_include_require_columns') {
            return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：必須カラムが含まれていません']);
        } else if ($data == 'not_valid_type') {
            return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'error', 'msg' => 'インポートに失敗しました：ファイルがUTF-8かShift-JISではありません']);
        }

        $site = Site::where('company_id', Auth::user()->company_id)->first();

        $product_master = Product::where('company_id', Auth::user()->company_id)->get();

        // 配列加工
        $inserts = [];
        foreach($data as $d) {
            $insert = [];
            $insert_flag = false;
            foreach($d as $k => $v) {
                $pm = $product_master->where('product_display_id', $v)->first();
                if ($pm) {
                    // 商品IDからproduct_idを引っ張る、無かったらスキップ
                    $insert['product_id'] = $pm->id;
                    $insert_flag = true;
                }
            }
            if ($insert_flag) {
                $insert['company_id'] = Auth::user()->company_id;
                $insert['site_id'] = $site->id;
                $insert['created_at'] = date("Y-m-d H:i:s");
                $insert['updated_at'] = date("Y-m-d H:i:s");

                $inserts[] = $insert;
            }
        }

        // CSVファイル自体の重複と、既存の商品IDとの重複を除く
        $ps = ProductBlacklist::where('company_id', Auth::user()->company_id)->pluck('product_id')->toArray();
        $tmp = [];
        $unique_data = [];
        foreach ($inserts as $d){
            if (!in_array($d['product_id'], $tmp) && !in_array($d['product_id'], $ps)) {
                $tmp[] = $d['product_id'];
                $unique_data[] = $d;
            }
        }


        // バルクインサート
        DB::table('product_blacklists')->insert($unique_data);
        return redirect('/reviews/products/blacklist')->with('flash_message', ['type' => 'success', 'msg' => 'インポートしました']);
    }

    public function _export ($products)
    {
        $arr = $products->get()->map(function($w) {return $w->product_display_id;})->all();
        array_unshift($arr, 'product_id');
        return implode("\n", $arr);
    }




}
