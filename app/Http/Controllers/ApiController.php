<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Group;
use App\Instagram;
use App\Product;
use App\User;
use App\InstagramGroup;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\MailTrackLog;

class ApiController extends Controller
{

    public function slider (Request $request)
    {
        $fixed_width = 0;
        $fixed_height = 0;
        $mobile_width = 0;
        $mobile_height = 0;

        // qidからgroup_idに変換
        $qid = $request->qid;
        $g = Group::where('qid', $qid)->first();
        if (!$g || !$g->is_active) {
            return '';
        }

        // 固定表示の場合はサイズを固定する
        if($g->is_fixed_display == 1){
            $fixed_width = 183;
            $fixed_height = 350;
            
            if($g->sd_wide == 1){
                $mobile_width = 143;
                $mobile_height = 320;
            }elseif($g->sd_wide == 2){
                $mobile_width = 185.85;
                $mobile_height = 346;
            }elseif($g->sd_wide == 3){
                $mobile_width = 115;
                $mobile_height = 320;
            }
        }
        $fixed_width = 'width: '.$fixed_width.'px';
        $fixed_height = 'height: '.$fixed_height.'px';

        // バージョンによって表示するスライダーを変更する
        $ver = $request->v;
        switch ($ver) {
            case 2:
                $css = 'slider/slider_v2.css';
                $js = 'slider/embed_v2.js';
                break;
            default:
                $css = 'slider.css';
                $js = 'embed.js';
                break;
        }

        // グループにABテストが設定されている場合50%の確率でそれに変更する
        if (!empty($g->abtest_group_id)) {
            if (mt_rand(1, 100) > 50) {
                $g_b = Group::where('id', $g->abtest_group_id)->first();

                // Bグループ表示にする
                // もしBグループが非アクティブだった場合何もしない
                if ($g_b && $g_b->is_active){
                    $g = $g_b;
                    $qid = $g_b->qid;
                }
            }
        }

        $igs = (new Group())->get_ig_from_group_id($g->id);
        $ig_ids = $igs->map(function($v){ return $v->ig_id; });

        $products = (new Product())->get_product_from_ig_ids($ig_ids, $g->company_id);
        $is_preview = $request->is_preview;

        return view('api/slider', compact('igs', 'products', 'qid', 'g','css','js', 'is_preview', 'fixed_width', 'fixed_height', 'mobile_width', 'mobile_height'))->render();
    }


    public function cv (Request $request)
    {
        $qid = $request->qid;
        return view('api/cv', compact('qid'))->render();
    }


    public function get_option (Request $request)
    {
        // qidからgroup_idに変換
        $qid = $request->qid;
        $g = Group::where('qid', $qid)->first();

        if ($g) {
            return $g;
        } else {
            return [];
        }
    }

    public function indicate (Request $request)
    {
        $ig_ids = $request->get('igs');
        $qid = $request->get('qid');
        $type = $request->get('type');

        // qidからどのgroup_idかを決定する
        $g = Group::where('qid', $qid)->first();

        if ($g) {
            // cvの場合はinstagram_idを使う、それ以外はig_idを使う
            if($type == 'cv'){
                $ig_ids = Instagram::whereIn('id', explode(',', $ig_ids))->pluck('id');
            }else{
                $ig_ids = Instagram::whereIn('ig_id', explode(',', $ig_ids))->pluck('id');
            }

            return DB::transaction(function() use ($ig_ids, $g, $type) {
                // インクリメント
                InstagramGroup::whereIn('instagram_id', $ig_ids)->where('group_id', $g->id)->increment($type);

                // indicatorにも入れる
                $data = [];
                foreach ($ig_ids as $ig_id) {
                    $data[] = [
                        'indicator_type' => $type,
                        'instagram_id' => $ig_id,
                        'group_id' => $g->id,
                        'created_at' => date("Y-m-d H:i:s")
                    ];
                }
                DB::table('indicators')->insert($data);
            });
        } else {
            return '';
        }
    }

    public function indicate_noig (Request $request)
    {
        $g = Group::where('qid', $request->qid)->first();
        if ($g) {
            // group_look追加
            DB::table('indicators')->insert([
                [
                    'indicator_type' => 'group_look',
                    'instagram_id' => 0,
                    'group_id' => $g->id,
                    'created_at' => date("Y-m-d H:i:s"),
                ]
            ]);
        }
    }

    public function indicate_product (Request $request)
    {
        $product_ids = $request->get('product_ids');
        $qid = $request->get('qid');
        $type = $request->get('type'); // click or cv

        // qidは使用しないが他userのproduct_idに影響を及ぼしにくくするため有効性のチェック
        $g = Group::where('qid', $qid)->first();

        if ($g) {
            $product_ids = explode(',', $product_ids);
            if (count($product_ids) == 0) {
                return '';
            }

            return DB::transaction(function() use ($product_ids, $type) {
                // インクリメント
                Product::whereIn('id', $product_ids)->increment($type);

                // indicatorにも入れる
                $data = [];
                foreach ($product_ids as $product_id) {
                    $data[] = [
                        'indicator_type' => $type,
                        'product_id' => $product_id,
                        'created_at' => date("Y-m-d H:i:s")
                    ];
                }
                DB::table('indicator_products')->insert($data);
            });
        } else {
            return '';
        }
    }

    // Sendgridのメール状況カウント
    public function count_mail(Request $request){
        if ($request !== null) {
            $type = $request[0]['event'] == 'delivered' ? 'send' : $request[0]['event'];

            // 必要なパラメータがなければエラーログ出力
            if(!isset($request[0]) || !isset($request[0]['company_id']) || !isset($request[0]['order_id']) || !isset($request[0]['product_id'])){
                return 'NG';
            }

            $company_id = $request[0]['company_id'];
            $order_id = $request[0]['order_id'];
            $product_id = $request[0]['product_id'];
            $reminder_count = isset($request[0]['reminder_count']) ? $request[0]['reminder_count'] : 0;

            // 企業IDと受注に紐づく企業IDが一致するか確認（STGと本番で混同しないよう）
            $order = DB::table('orders')->where('id', $order_id)->first();
            Log::info($request);
            if(!isset($order) || $order->company_id != $company_id){
                Log::info('company_id or order_id is not match');
                return 'NG';
            }

            MailTrackLog::firstOrCreate([
                'company_id' => $company_id,
                'order_id' => $order_id,
                'product_id' => $product_id,
                'type' => $type,
                'reminder_count' => $reminder_count,
            ]);
            Log::info('mail track log created');

        } else if ($request == null) {
            Log::info('no request');
        }

        return 'OK';
    }

}
