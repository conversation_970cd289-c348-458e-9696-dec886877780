<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;
use App\Group;
use App\Instagram;
use App\InstagramGroup;
use App\InstagramProduct;
use App\InstagramFavorite;
use App\SecondUsage;
use App\GroupCustomOrder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Session;
use App\InstagramSecondUsageRequest;
use Aws\S3\S3Client;
use Aws;
use Response;
use Carbon\Carbon;

class InstagramController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $at = Auth::user()->company_tied->access_token;
        $at_ex = Auth::user()->company_tied->access_token_expires_in;
        $id = Auth::user()->company_tied->ig_business_account_id;
        $fv = config('app.facebook_version');
        $host = "https://graph.facebook.com/v{$fv}";

        $groups = Group::where('company_id', Auth::user()->company_id)->get();
        $products = Product::where('company_id', Auth::user()->company_id)->get()->map(function($v){ return ['id' => $v->id, 'name' => $v->name]; });
        $ig_products = InstagramProduct::join('instagrams','instagram_products.instagram_id','=','instagrams.id')
            ->where('instagram_products.company_id', Auth::user()->company_id)->get()->map(function($v){ return [$v->instagram->ig_id, $v->product_id]; });
        $ig_favorites = InstagramFavorite::where('company_id', Auth::user()->company_id)->get();
        $ig_groups = InstagramGroup::join('instagrams','instagram_groups.instagram_id','=','instagrams.id')
            ->where('instagram_groups.company_id', Auth::user()->company_id)->get();
        $second_usages = SecondUsage::where('company_id', Auth::user()->company_id)->where('is_setting', 0)->get();

        $beginning_of_today = Carbon::now()->startOfDay();
        $end_of_today = Carbon::now()->endOfDay();
        $instagram_second_usage_requests = InstagramSecondUsageRequest::where('company_id', Auth::user()->company_id)
          ->where('user_id', Auth::user()->id)
          ->where('created_at', '>=', $beginning_of_today)
          ->where('created_at', '<=', $end_of_today)->get();
        $is_disabled_create_request = count($instagram_second_usage_requests) < 10 ? "false" : "true";

        return view('instagram/index', compact('at', 'at_ex', 'id', 'host', 'groups', 'products', 'ig_products', 'ig_favorites', 'ig_groups', 'second_usages', 'is_disabled_create_request'));
    }

    public function groups (Request $request)
    {
        $group_id = $request['group_id'];
        $checked_igs =  $request['checked_igs'];

        // 管理画面での設定を超過していないかチェック
        $old_ig_id = Instagram::where('company_id', Auth::user()->company_id)->pluck('ig_id')->toArray();
        $new_ig_id = array_map(function($c){ return $c['id']; }, $checked_igs);
        $limit = Auth::user()->company_tied ? Auth::user()->company_tied->get_ugc_limit() : INF;
        $count = count(array_unique(array_merge($old_ig_id, $new_ig_id)));
        if ($limit < $count) {
            return ['status' => false, 'msg' => '登録出来ませんでした。制限を超えています(' . $count . '/' . $limit . ')'];
        }
        
        foreach($checked_igs as $c){

            // ig_idを一意に生成するため、permalinkからInstagram投稿IDとパラメータ部分をそれぞれ取得
            $parts = explode('/', parse_url($c['permalink'], PHP_URL_PATH));
            $parts_query = explode('/', parse_url($c['permalink'], PHP_URL_QUERY));
            $ig_info = $parts[2];
            $ig_info_query = $parts_query[0];
            $timestamp = time();

            // ig_idに使用する文字列で「?」と「=」を「_」に変換
            $converted_ig_info_query = str_replace(['?', '='], '_', $ig_info_query);
            $converted_id = str_replace(['?', '='], '_', $c['id']);
            
            $result = DB::transaction(function() use ($group_id, $checked_igs, $c, $ig_info, $converted_ig_info_query,$converted_id,$timestamp) {
                // instagramテーブルに格納
                $igs = Instagram::updateOrCreate([
                    'company_id' => Auth::user()->company_id,
                    'ig_id' => $timestamp.'-'.$ig_info.'-'.$converted_ig_info_query.'-'.$converted_id.'-'.'cid'.Auth::user()->company_id,
                ],
                [
                    'image' => $c['image'],
                    'content' => is_null($c['content']) ? '' : $c['content'],
                    'time_stamp' => $c['time_stamp'],
                    'comments_count' => $c['comments_count'],
                    'like_count' => $c['like_count'],
                    'permalink' => $c['permalink'],
                    'parent_permalink' => $c['parent_permalink'],
                    'author_name' => $c['author_name'],
                    'thumbnail_url' => $c['thumbnail_url'],
                    'thumbnail_width' => $c['thumbnail_width'],
                    'thumbnail_height' => $c['thumbnail_height'],
                    'post_type' => $c['post_type']
                ]);

                // instagram_groupにデータを格納
                $insta_group = InstagramGroup::updateOrCreate([
                    'company_id' => Auth::user()->company_id,
                    'instagram_id' => $igs->id,
                    'group_id' => $group_id
                ],[]);
                $group_custom_orders = GroupCustomOrder::updateOrCreate([
                    'instagram_id' => $igs->id,
                    'group_id' => $group_id,
                    'sort_id' => 0
                ],[]);
            });
        }

        return ['status' => $result, 'msg' => '登録しました'];
    }

    public function products (Request $request)
    {
        $checked_ig = $request['checked_ig'];
        $product_ids =  $request['product_ids'];

        // 管理画面での設定を超過していないかチェック
        $old_ig_id = Instagram::where('company_id', Auth::user()->company_id)->pluck('ig_id')->toArray();
        $new_ig_id = [$checked_ig['id']];
        $limit = Auth::user()->company_tied ? Auth::user()->company_tied->get_ugc_limit() : INF;
        $count = count(array_unique(array_merge($old_ig_id, $new_ig_id)));
        if ($limit < $count) {
            return ['status' => false, 'msg' => '登録出来ませんでした。制限を超えています(' . $count . '/' . $limit . ')'];
        }
        $parts = explode('/', parse_url($checked_ig['permalink'], PHP_URL_PATH));
        $ig_info = $parts[2];
        $result = DB::transaction(function() use ($checked_ig, $product_ids, $ig_info) {
            // instagramの詳細情報
            $igs = Instagram::updateOrCreate([
                'company_id' => Auth::user()->company_id,
                'ig_id' => $ig_info.'-'.'cid'.Auth::user()->company_id,
            ],
            [
                'image' => $checked_ig['image'],
                'content' => is_null($checked_ig['content']) ? '' : $checked_ig['content'],
                'time_stamp' => $checked_ig['time_stamp'],
                'comments_count' => $checked_ig['comments_count'],
                'like_count' => $checked_ig['like_count'],
                'permalink' => $checked_ig['permalink'],
                'parent_permalink' => $checked_ig['parent_permalink'],
                'author_name' => $checked_ig['author_name'],
                'thumbnail_url' => $checked_ig['thumbnail_url'],
                'thumbnail_width' => $checked_ig['thumbnail_width'],
                'thumbnail_height' => $checked_ig['thumbnail_height'],
                'post_type' => $checked_ig['post_type']
            ]);

            foreach($product_ids as $p){
                // instagram_productsにデータを格納
                $insta_group = InstagramProduct::updateOrCreate([
                    'company_id' => Auth::user()->company_id,
                    'instagram_id' => $igs->id,
                    'product_id' => $p
                ],[]);
            }
        });

        return ['status' => $result, 'msg' => '登録しました'];
    }

    public function favorites($ig_id)
    {
        $company_id =  Auth::user()->company_id;

        $ins = InstagramFavorite::where('ig_id', $ig_id)->where('company_id', $company_id);
        if ($ins->count() > 0) {
            $ins->delete();
        } else {
            $ins = InstagramFavorite::create(['ig_id' => $ig_id, 'company_id' => $company_id]);
        }
        return 'success';
    }

    public function upload_media (Request $request)
    {
        $local_file = $request->file('file');
        $destination_file = $local_file->getClientOriginalName();

        $credentials = new Aws\Credentials\Credentials(
            config('app.aws_r2_access_key_id'),
            config('app.aws_r2_secret_access_key')
        );

        $endpoint = config('app.CLOUDFLARE_R2_ENDPOINT');

        $s3_client = new S3Client([
            'credentials' => $credentials,
            'endpoint' => $endpoint,
            'region' => 'auto',
            'version' => 'latest'
        ]);

        try {
            $ext = $local_file->getClientOriginalExtension();
            $time = time();

            $result = $s3_client->putObject([
                'Bucket' => config('app.aws_r2_bucket'),
                'Key' => Auth::user()->company_id.'/'.$time.'.'.$ext,
                'SourceFile' => $local_file->getRealPath(),
            ]);

            return Response::json([
                'uploaded_url' => config('app.CLOUDFLARE_R2_URL').Auth::user()->company_id.'/'.$time.'.'.$ext,
                'is_movie' => $local_file->getClientOriginalExtension() == 'mp4',
                'thumbnail_width' => 480,
                'thumbnail_height' => 480
            ], 200);
        } catch (\Exception $e) {
            // return Response::json([
            //     'uploaded_url' => "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQS9Hl9lTqjONacFKE_y1xxX4JgBLD9aWsq1Uwip0IRNw&s",
            //     'is_video' => $local_file->getClientOriginalExtension() == 'mp4',
            //     'thumbnail_width' => 480,
            //     'thumbnail_height' => 480
            // ], 200);
            return Response::json(['msg' => $e->getMessage()], 400);
        }
    }

    public function get_ugcs(Request $request)
    {
        $last_slash_pos = strrpos($request['permalink'], '/');
        $parent_permalink = substr($request['permalink'], 0, $last_slash_pos + 1);

        $company_id = Auth::User()->company_id;

        $s3_uploaded_ugcs = Instagram::whereRaw("parent_permalink = '{$parent_permalink}' or permalink = '{$parent_permalink}'")
            ->where('thumbnail_url', 'like', '%ugc-%')
            ->where('company_id', $company_id)
            ->where('ig_id', 'regexp', '^[0-9]+')
            ->get()
            ->toArray();
        
        $s3_still_not_uploaded_ugcs = Instagram::whereRaw("parent_permalink = '{$parent_permalink}' or permalink = '{$parent_permalink}'")
            ->where('thumbnail_url', 'not like', '%ugc-%')
            ->where('company_id', $company_id)
            ->where('ig_id', 'regexp', '^[0-9]+')
            ->get()
            ->toArray();

        $not_expired_s3_still_not_uploaded_ugcs = [];
        
        foreach ($s3_still_not_uploaded_ugcs as $item ) {
            try {
                $context = stream_context_create(['http' => ['ignore_errors' => true]]);
                $content_res = file_get_contents($item['thumbnail_url'], false, $context);
                if ($content_res != 'URL signature expired') {
                    $not_expired_s3_still_not_uploaded_ugcs[] = $item;
                }
            } catch(Exception $e) {
            }
        }

        $result = array_merge($s3_uploaded_ugcs, $not_expired_s3_still_not_uploaded_ugcs); 
        
        return Response::json(['ugcs' => $result], 200);
    }

    public function get_manual_uploaded_ugcs(Request $request)
    {
        $page = $request['page'] != null ? $request['page'] : 1;
        $limit = $request['limit'] != null ? $request['limit'] : 12;
        $company_id = Auth::User()->company_id;

        $ugcs = Instagram::where('company_id', $company_id)
            ->where('thumbnail_url', 'like', '%ugc-%')
            ->where('ig_id', 'regexp', '^(?![0-9])')
            ->orderBy('id', 'desc')
            ->limit($limit)
            ->offset(($page - 1) * $limit)
            ->get();

        $total_ugcs = Instagram::where('company_id', $company_id)
            ->where('thumbnail_url', 'like', '%ugc-%')
            ->where('ig_id', 'regexp', '^(?![0-9])')
            ->count(); 

        return Response::json(['ugcs' => $ugcs, 'total' => $total_ugcs], 200);
    }
  public function get_status(Request $request)
  {
    $media_status = InstagramSecondUsageRequest::whereIn('permalink', $request->permalinks)->where('user_id', Auth::user()->id)->get()
      ->map(function($res){ return [$res->permalink, $res->status]; });

    return ['result'=> 'success', 'media_status' => $media_status];
  }

    public function get_single_status(Request $request)
    {
        $status = 0;
        $request = InstagramSecondUsageRequest::where('ig_id', $request->ig_id)->orWhere('permalink', $request->permalink)->first();
        if ($request) {
            $status = $request->status;
        }

        return ['result'=> 'success', 'status' => $status];
    }

  public function get_posts(Request $request)
  {
    $posts = InstagramSecondUsageRequest::where('company_id', Auth::user()->company_id)
      ->where('user_id', Auth::user()->id)->where('status', $request->type)->get();

    return ['result'=> 'success', 'posts' => $posts];
  }

    private function get_manual_id_from_permarlink($permalink)
    {
        $splitted = explode('/', $permalink);
        $array = array_filter($splitted, array($this, 'filter_callback'));
        return end($array) . '_manual';
    }

    private function filter_callback($str)
    {
        return strlen($str) > 0 && strpos($str, '?', 0) === false;
    }

    public function tab ()
    {
        return view('instagram/tab');
    }
}
