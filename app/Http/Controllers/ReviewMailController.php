<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\MailTemplate;
use App\Site;
use App\Review;
use App\Product;
use App\Order;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\MailHandler;

class ReviewMailController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $mt = MailTemplate::where('company_id', Auth::user()->company_id)->first();
        if (!$mt) {
            $mt = new MailTemplate();
            $mt->set_default();
        }
        $display_embed = MailTemplate::DISPLAY_EMBED;
        $display_embed_page = MailTemplate::DISPLAY_EMBED_PAGE;
        $site = Site::where('company_id', Auth::user()->company_id)->first();

        return view('review_mail/index', compact('mt', 'display_embed', 'display_embed_page', 'site'));
    }

    public function get_info (Request $request)
    {
        $type = $request->type;
        $mt = MailTemplate::where('company_id', Auth::user()->company_id)->first();
        if (!$mt) {
            $mt = new MailTemplate();
            $mt->set_default();
        }

        $response = [];
        if ($type == 'stylesheet' || $type == 'header' || $type == 'footer') {
            $response['data'] = $mt->$type;
        } else if (in_array($type, ['product_review', 'site_review', 'comment_notify', 'qa_notify', 'address_confirm'])) {
            $tmp = $type . '_title';
            $tmp2 = $type . '_textmail';
            $response['data'] = $mt->$type;
            $response['text'] = $mt->$tmp2;
            $response['title'] = $mt->$tmp;
        }

        if (in_array($type, ['product_review', 'site_review'])) {
            $tmp = $type . '_dynamicmail';
            $response['dynamic'] = $mt->$tmp;

            $indexes = [1,2,3];
            foreach($indexes as $i){
                $data_prop = $type . '_r' . $i;
                $text_prop = $type . '_textmail_r' . $i;
                $dynamic_prop = $type . '_dynamicmail_r' . $i;
                $title_prop = $type . '_title_r' . $i;

                $response['data' . $i] = $mt->$data_prop;
                $response['text' . $i] = $mt->$text_prop;
                $response['dynamic' . $i] = $mt->$dynamic_prop;
                $response['title' . $i] = $mt->$title_prop;
            }
        }

        $response['result'] = true;

        return $response;
    }

    public function create (Request $request)
    {
        $site = Site::where('company_id', Auth::user()->company_id)->first();

        $type = $request->type;
        $mailtype = $request->mailtype;
        $mailnum = $request->mailnum;

        $mt = MailTemplate::where('company_id', Auth::user()->company_id)->first();
        if (!$mt) {
            $mt = new MailTemplate();
            $mt->company_id = Auth::user()->company_id;
            $mt->site_id = $site->id;
            $mt->set_default();
        }

        if ($type == 'stylesheet' || $type == 'header' || $type == 'footer') {
            $mt->$type = $request->data;
        } else if (in_array($type, ['product_review', 'site_review', 'comment_notify', 'qa_notify', 'address_confirm'])) {
            $postfix = $mailnum == '0' ? '' : '_r' . $mailnum;
            $title = $type . '_title' . $postfix;

            if ($mailtype == 'html') {
                $import_column = $type . $postfix;
            } else if ($mailtype == 'text') {
                $import_column = $type . '_textmail' . $postfix;
            } else if ($mailtype == 'dynamic') {
                $import_column = $type . '_dynamicmail' . $postfix;
            }

            $mt->$import_column = $request->data;
            $mt->$title = $request->title;
        }

        $mt->save();

        return redirect('/reviews/mails')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }

    public function send_test(Request $request)
    {
        $sendgridIds = explode(',', env('SENDGRID_IDS', ''));
        $is_sendgrid = in_array(Auth::user()->company_id, $sendgridIds);
        $mh = new MailHandler(Auth::user()->company_id, $is_sendgrid);
        if($is_sendgrid){
            $server = 'sendgrid';
        }else{
            $server = 'ses';
        }
        

        if (strpos($request->type, 'product_review') !== false) {
            // テストデータ
            $product = new Product();
            $product->set_test_data();

            $order = new Order();
            $order->set_test_data();

            if ($request->type == 'product_review') {
                $reminder_count = 0;
            } else if ($request->type == 'product_review_r1') {
                $reminder_count = 1;
            } else if ($request->type == 'product_review_r2') {
                $reminder_count = 2;
            } else if ($request->type == 'product_review_r3') {
                $reminder_count = 3;
            }

            $mh->product_review($product, $order, $reminder_count, $server, $request->email);
        } else if (strpos($request->type, 'site_review') !== false) {
            if ($request->type == 'site_review') {
                $reminder_count = 0;
            } else if ($request->type == 'site_review_r1') {
                $reminder_count = 1;
            } else if ($request->type == 'site_review_r2') {
                $reminder_count = 2;
            } else if ($request->type == 'site_review_r3') {
                $reminder_count = 3;
            }
            $order = new Order();
            $order->set_test_data();

            $mh->site_review($order, $reminder_count, $server, $request->email);
        } else if ($request->type == 'comment_notify') {
            $review = new Review();
            $review->set_test_data();

            $mh->comment_notify($review, $request->email);
        } else if ($request->type == 'address_confirm') {
            // 実際のデータを使う場合
            /* $review = Review::where('user_id', Auth::id())->whereNotNull('verification_token')
                ->inRandomOrder()->first();
            if (!$review) {
                $review = new Review();
                $review->set_test_data();
            } */
            // テストデータのみ
            $review = new Review();
            $review->set_test_data();
            $mh->address_confirm($review, $request->email);
        }
    }



}
