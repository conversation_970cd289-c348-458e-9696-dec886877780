<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Site;

class SiteController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $site = Site::where('company_id', Auth::user()->company_id)->first();
        if (!$site) {
            $site = new Site();
        }

        return view('site/index', compact('site'));
    }

    public function create (Request $request)
    {
        $arr = $request->all();

        if ($request->id) {
            // 更新
            $s = Site::where('company_id', Auth::user()->company_id)->first();
            unset($arr['id']);
            $s->fill($arr);
        } else {
            // 作成
            $arr = $request->all();
            $s = new Site($arr);
        }
        $s->company_id = Auth::user()->company_id;
        $s->save();

        return redirect('/reviews/sites/settings')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }



}
