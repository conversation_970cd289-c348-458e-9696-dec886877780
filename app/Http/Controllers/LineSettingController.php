<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\LineSetting;
use App\Site;
use Illuminate\Support\Facades\Log;

class LineSettingController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index (Request $request)
    {
        $ls = LineSetting::where('company_id', Auth::user()->company_id)->first();
        if (!$ls) {
            $ls = new LineSetting();
            $ls->set_default();
        }

        return view('line/setting', compact('ls'));
    }

    public function create (Request $request)
    {
        $arr = $request->all();

        if ($request->id) {
            // 更新
            $ms = LineSetting::where('company_id', Auth::user()->company_id)->first();
            unset($arr['id']);
            $ms->fill($arr);
        } else {
            // 作成
            $arr = $request->all();
            $ms = new LineSetting();
            $ms->set_default();
            $ms->fill($arr);
        }
        $ms->company_id = Auth::user()->company_id;
        $ms->site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;
        $ms->save();

        return redirect('/reviews/line/settings')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }




}
