<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;
use App\Instagram;
use App\InstagramProduct;
use App\Information;
use App\Indicator;
use Carbon\Carbon;
use Session;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // トップページ
    public function index ()
    {
        $products = Product::where('products.company_id', Auth::user()->company_id)
        ->join('instagram_products', 'instagram_products.product_id', '=', 'products.id')
        ->distinct()
        ->select('products.*')
        ->orderBy('products.created_at')
        ->get();

        // last_loginも設定する
        Auth::user()->last_login_at = Carbon::now();
        Auth::user()->save();

        // 未読新着があるかどうかチェック
        $infos = Information::select('information.*', 'information_id as read')->leftjoin('information_readings', function ($join) {
            $join->on('information_readings.information_id', '=', 'information.id')
              ->where('information_readings.company_id', '=', Auth::user()->company_id);
            })->whereNull('information_id')->get();
        Session::put('not_read_info', count($infos));

        return view('product/index', compact('products', 'infos'));
    }

    public function new ()
    {
        $product = new Product();
        $instagrams = Instagram::where('company_id', Auth::user()->company_id)->where('thumbnail_url', 'like', '%ugc-creative%')->get();
        $ig_products = InstagramProduct::where('company_id', 0)->get();

        foreach($instagrams as $i => $instagram){
            if (strpos($instagram->thumbnail_url, '.mp4') !== false) {
                $ext = 'video';
            }else{
                $ext = 'img';
            }
            $instagrams[$i]['ext'] = $ext;
        }
        return view('product/new', compact('product', 'instagrams', 'ig_products'));
    }

    public function edit ($id)
    {
        $product = Product::findOrFail($id);
        $instagrams = Instagram::where('company_id', Auth::user()->company_id)->where('thumbnail_url', 'like', '%ugc-creative%')->get();
        $ig_products = InstagramProduct::where('company_id', Auth::user()->company_id)->where('product_id', $id)->get();

        foreach($instagrams as $i => $instagram){
            if (strpos($instagram->thumbnail_url, '.mp4') !== false) {
                $ext = 'video';
            }else{
                $ext = 'img';
            }
            $instagrams[$i]['ext'] = $ext;
        }

        return view('product/new', compact('product', 'instagrams', 'ig_products'));
    }

    public function create (Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'product_url' => 'required|max:255'
        ]);

        if ($request->file('product_image')) {
            // 画像アップロードをしていた場合
            $path = $request->file('product_image')->store('public/product_images');
            $path = explode('/', $path)[2];
            $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/product_images/' . $path;
        } else {
            // URLから選択していた場合
            $image_url = $request['image_url'];
        }

        $product = Product::create([
            'company_id' => Auth::user()->company_id,
            'name' => $request['name'],
            'product_url' => $request['product_url'],
            'image_url' => $image_url,
        ]);

        // instagram_productに書き込み
        if (!empty($request->ugc_product)) {
            foreach($request->ugc_product as $instagram_id => $v){
                $ip = InstagramProduct::where('company_id', Auth::user()->company_id)
                    ->where('instagram_id', $instagram_id)
                    ->where('product_id', $product->id);
                if ($v == 0 && $ip->count() > 0) {
                    $ip->delete();
                } else if ($v == 1 && $ip->count() == 0) {
                    InstagramProduct::create([
                        'company_id' => Auth::user()->company_id,
                        'instagram_id' => $instagram_id,
                        'product_id' => $product->id,
                    ]);
                }
            }
        }

        return redirect('/')->with('flash_message', '作成しました');
    }

    public function update (Request $request)
    {
        $product = Product::findOrFail($request->id);

        $request->validate([
            'name' => 'required|max:255',
            'product_url' => 'required|max:255',
            'image_url' => 'required|max:255',
        ]);

        try {
            DB::transaction(function() use ($request, $product) {
                if ($request->file('product_image')) {
                    // 画像アップロード後、古い画像を削除
                    $path = $request->file('product_image')->store('public/product_images');
                    $path = explode('/', $path)[2];
                    $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/product_images/' . $path;
                    $prev_header_image = $product->header_image;
                    if ($request->file('header_image')) {
                        Storage::delete('public/product_images/' . $prev_header_image);
                    }
                    $product->image_url = $image_url;
                } else {
                    $product->image_url = $request->image_url;
                }

                $product->name = $request->name;
                $product->product_url = $request->product_url;
                $product->save();

                // instagram_productに書き込み
                foreach($request->ugc_product as $instagram_id => $v){
                    $ip = InstagramProduct::where('company_id', Auth::user()->company_id)
                        ->where('instagram_id', $instagram_id)
                        ->where('product_id', $product->id);
                    if ($v == 0 && $ip->count() > 0) {
                        $ip->delete();
                    } else if ($v == 1 && $ip->count() == 0) {
                        InstagramProduct::create([
                            'company_id' => Auth::user()->company_id,
                            'instagram_id' => $instagram_id,
                            'product_id' => $product->id,
                        ]);
                    }
                }

            });
        } catch (Throwable $e) {
            return redirect('/')->with('flash_message', '更新に失敗しました');
        }

        return redirect('/')->with('flash_message', '更新しました');
    }

    public function delete ($id)
    {
        $product = Product::findOrFail($id);

        // 紐づいているinstagram_productsも削除する
        DB::transaction(function() use ($product, $id) {
            $product->delete();
            InstagramProduct::where('product_id', $id)->where('company_id', Auth::user()->company_id)->delete();
        });

        return redirect('/')->with('flash_message', '削除しました');
    }

    public function get_url (Request $request)
    {
        $url = $request['url'];
        $str = file_get_contents($url);
        return $str;
    }

    public function index_by_instagram(Request $request)
    {
        $instagram_id = $request->id;
        $group_id = $request->group_id;

        $startTime_p = Carbon::now();
        $products = Product::where('company_id', Auth::user()->company_id)->whereHas('instagram_products', function($q) use ($instagram_id){
            $q->where('instagram_id', $instagram_id);
        })->get();
        $endTime_p = Carbon::now();
        $elapsedTime_p = $endTime_p->diffInSeconds($startTime_p);
        Log::info('$products取得開始 : '.$startTime_p);
        Log::info('$products取得終了 : '.$endTime_p);
        Log::info('$products取得 : '.$elapsedTime_p);

        $instagram = Instagram::find($instagram_id);

        $startTime_i = Carbon::now();
        $visit_count = Indicator::where([
            'indicator_type' => 'click',
            'instagram_id' => $instagram_id,
            'group_id' => $group_id
        ])->count();
        $endTime_i = Carbon::now();
        $elapsedTime_i = $endTime_i->diffInSeconds($startTime_i);
        Log::info('$visit_count取得開始 : '.$startTime_i);
        Log::info('$visit_count取得終了 : '.$endTime_i);
        Log::info('$visit_count取得 : '.$elapsedTime_i);

        $startTime_t = Carbon::now();
        $target_products = Product::where('company_id', Auth::user()->company_id)->get()->filter(
            function($t) use ($products) { return !$products->contains(function($p) use ($t) { return $t->id == $p->id;}); }
        );
        $endTime_t = Carbon::now();
        $elapsedTime_t = $endTime_t->diffInSeconds($startTime_t);
        Log::info('$target_products取得開始 : '.$startTime_t);
        Log::info('$target_products取得終了 : '.$endTime_t);
        Log::info('$target_products取得 : '.$elapsedTime_t);

        return view('product/instagrams', compact('products', 'instagram', 'group_id', 'visit_count', 'target_products'));
    }

    public function delete_instagram_product(Request $request)
    {
        $product_id = $request->id;
        $instagram_id = $request->instagram_id;
        $group_id = $request->group_id;
        $ip = InstagramProduct::where('company_id', Auth::user()->company_id)->where([
            'instagram_id' => $instagram_id,
            'product_id' => $product_id
        ]);
        $ip->delete();
        return redirect('/p/i/' . $instagram_id . '?group_id=' . $group_id)->with('flash_message', '削除しました');
    }

    public function create_instagram_product(Request $request)
    {
        $product_id = $request->product_id;
        $instagram_id = $request->instagram_id;
        $group_id = $request->group_id;

        $ip = InstagramProduct::firstOrCreate([
            'company_id' => Auth::user()->company_id,
            'instagram_id' => $instagram_id,
            'product_id' => $product_id
        ]);

        return redirect('/p/i/' . $instagram_id . '?group_id=' . $group_id)->with('flash_message', '追加しました');
    }

    public function indicate(Request $request)
    {
        $instagram_id = $request->instagram_id;
        $product_ids = $request->product_ids;
        $date_start =  $request->date_start;
        $date_end =  $request->date_end;

        $tmp = DB::table('indicator_products')
            ->select(DB::raw('product_id, indicator_type, count(*) as ind_count'))
            ->where('product_id', $product_ids);

        if ($date_start != "") {
            $tmp = $tmp->where('indicator_products.created_at', '>=', $date_start);
        }

        if ($date_end != "") {
            $tmp = $tmp->where('indicator_products.created_at', '<=', $date_end . ' 23:59:59');
        }

        $tmp->groupBy('indicator_type', 'product_id');

        $visit_query = Indicator::where([
            'indicator_type' => 'click',
            'instagram_id' => $instagram_id,
        ]);

        if ($date_start != "") {
            $visit_query = $visit_query->where('created_at', '>=', $date_start);
        }

        if ($date_end != "") {
            $visit_query = $visit_query->where('created_at', '<=', $date_end . ' 23:59:59');
        }

        return [
            'visit_count' => $visit_query->count(),
            'data' => $tmp->get()->toArray()
        ];
    }
}
