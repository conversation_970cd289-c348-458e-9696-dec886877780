<?php

namespace App\Http\Controllers\TikTok;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\TiktokProduct;
use App\Tiktok;
use App\TiktokProductRelationship;
use App\Information;
use App\TiktokIndicator;
use Carbon\Carbon;
use Session;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ProductController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // トップページ
    public function index ()
    {
        // TODO: Need change
        $products = TiktokProduct::where('tiktok_products.company_id', Auth::user()->company_id)
            ->join('tiktok_product_relationship', 'tiktok_product_relationship.tiktok_product_id', '=', 'tiktok_products.id')
            ->distinct()
            ->select('tiktok_products.*')
            ->orderBy('tiktok_products.created_at')
            ->get();

        // last_loginも設定する
        Auth::user()->last_login_at = Carbon::now();
        Auth::user()->save();

        // 未読新着があるかどうかチェック
        $infos = Information::select('information.*', 'information_id as read')->leftjoin('information_readings', function ($join) {
            $join->on('information_readings.information_id', '=', 'information.id')
              ->where('information_readings.company_id', '=', Auth::user()->company_id);
            })->whereNull('information_id')->get();
        Session::put('not_read_info', count($infos));

        return view('tiktok/product/index', compact('products', 'infos'));
    }

    public function new ()
    {
        $product = new TiktokProduct();
        $tiktoks = Tiktok::where('company_id', Auth::user()->company_id)->get();
        $ig_products = TiktokProductRelationship::where('company_id', 0)->get();

        return view('tiktok/product/new', compact('product', 'tiktoks', 'ig_products'));
    }

    public function edit ($id)
    {
        $product = TiktokProduct::findOrFail($id);
        $tiktoks = Tiktok::where('company_id', Auth::user()->company_id)->get();
        $ig_products = TiktokProductRelationship::where('company_id', Auth::user()->company_id)->where('tiktok_product_id', $id)->get();

        return view('tiktok/product/new', compact('product', 'tiktoks', 'ig_products'));
    }

    public function create (Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
            'product_url' => 'required|max:255'
        ]);

        if ($request->file('product_image')) {
            // 画像アップロードをしていた場合
            $path = $request->file('product_image')->store('public/product_images');
            $path = explode('/', $path)[2];
            $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/product_images/' . $path;
        } else {
            // URLから選択していた場合
            $image_url = $request['image_url'];
        }

        $product = TiktokProduct::create([
            'company_id' => Auth::user()->company_id,
            'name' => $request['name'],
            'product_url' => $request['product_url'],
            'image_url' => $image_url,
        ]);

        // instagram_productに書き込み
        if (!empty($request->ugc_product)) {
            foreach($request->ugc_product as $tiktok_id => $v){
                $ip = TiktokProductRelationship::where('company_id', Auth::user()->company_id)
                    ->where('tiktok_id', $tiktok_id)
                    ->where('tiktok_product_id', $product->id);
                if ($v == 0 && $ip->count() > 0) {
                    $ip->delete();
                } else if ($v == 1 && $ip->count() == 0) {
                    TiktokProductRelationship::create([
                        'company_id' => Auth::user()->company_id,
                        'tiktok_id' => $tiktok_id,
                        'tiktok_product_id' => $product->id
                    ]);
                }
            }
        }

        return redirect('/tiktok/products')->with('flash_message', '作成しました');
    }

    public function update (Request $request)
    {
        $product = TiktokProduct::findOrFail($request->id);

        $request->validate([
            'name' => 'required|max:255',
            'product_url' => 'required|max:255',
            'image_url' => 'required|max:255',
        ]);

        try {
            DB::transaction(function() use ($request, $product) {
                if ($request->file('product_image')) {
                    // 画像アップロード後、古い画像を削除
                    $path = $request->file('product_image')->store('public/product_images');
                    $path = explode('/', $path)[2];
                    $image_url = request()->getSchemeAndHttpHost() . '/ugc/storage/product_images/' . $path;
                    $prev_header_image = $product->header_image;
                    if ($request->file('header_image')) {
                        Storage::delete('public/product_images/' . $prev_header_image);
                    }
                    $product->image_url = $image_url;
                } else {
                    $product->image_url = $request->image_url;
                }

                $product->name = $request->name;
                $product->product_url = $request->product_url;
                $product->save();

                // instagram_productに書き込み
                if (!empty($request->ugc_product)) {
                    foreach($request->ugc_product as $tiktok_id => $v) {
                        $ip = TiktokProductRelationship::where('company_id', Auth::user()->company_id)
                            ->where('tiktok_id', $tiktok_id)
                            ->where('tiktok_product_id', $product->id);
                        if ($v == 0 && $ip->count() > 0) {
                            $ip->delete();
                        } else if ($v == 1 && $ip->count() == 0) {
                            TiktokProductRelationship::create([
                                'company_id' => Auth::user()->company_id,
                                'tiktok_id' => $tiktok_id,
                                'tiktok_product_id' => $product->id
                            ]);
                        }
                    }
                }
            });
        } catch (Throwable $e) {
            return redirect('/tiktok/products')->with('flash_message', '更新に失敗しました');
        }

        return redirect('/tiktok/products')->with('flash_message', '更新しました');
    }

    public function delete ($id)
    {
        $product = TiktokProduct::findOrFail($id);

        // 紐づいているinstagram_productsも削除する
        DB::transaction(function() use ($product, $id) {
            $product->delete();
            TiktokProductRelationship::where('tiktok_product_id', $id)->where('company_id', Auth::user()->company_id)->delete();
        });

        return redirect('/tiktok/products')->with('flash_message', '削除しました');
    }

    public function get_url(Request $request)
    {
        $url = $request['url'];
        $str = file_get_contents($url);
        return $str;
    }

    public function index_by_tiktok(Request $request)
    {
        $tiktok_id = $request->id;
        $group_id = $request->group_id;
        $products = TiktokProduct::where('company_id', Auth::user()->company_id)->whereHas('tiktok_product_relationship', function($q) use ($tiktok_id){
            $q->where('tiktok_id', $tiktok_id);
        })->get();

        $tiktok = Tiktok::find($tiktok_id);
        $visit_count = TiktokIndicator::where([
            'indicator_type' => 'click',
            'tiktok_id' => $tiktok_id,
            'tiktok_group_id' => $group_id
        ])->count();

        $target_products = TiktokProduct::where('company_id', Auth::user()->company_id)->get()->filter(
            function($t) use ($products) { return !$products->contains(function($p) use ($t) { return $t->id == $p->id;}); }
        );

        return view('/tiktok/product/tiktoks', compact('products', 'tiktok', 'group_id', 'visit_count', 'target_products'));
    }

    public function delete_tiktok_product(Request $request)
    {
        $product_id = $request->id;
        $tiktok_id = $request->tiktok_id;
        $group_id = $request->group_id;

        $ip = TiktokProductRelationship::where('company_id', Auth::user()->company_id)->where([
            'tiktok_id' => $tiktok_id,
            'tiktok_product_id' => $product_id
        ]);
        $ip->delete();

        return redirect('/tiktok/p/i/' . $tiktok_id . '?group_id=' . $group_id)->with('flash_message', '削除しました');
    }

    public function create_tiktok_product(Request $request)
    {
        $product_id = $request->product_id;
        $tiktok_id = $request->tiktok_id;
        $group_id = $request->group_id;

        $ip = TiktokProductRelationship::firstOrCreate([
            'company_id' => Auth::user()->company_id,
            'tiktok_id' => $tiktok_id,
            'tiktok_product_id' => $product_id
        ]);

        return redirect('/tiktok/p/i/' . $tiktok_id . '?group_id=' . $group_id)->with('flash_message', '追加しました');
    }

    public function indicate(Request $request)
    {
        $tiktok_id = $request->tiktok_id;
        $product_ids = $request->product_ids;
        $date_start =  $request->date_start;
        $date_end =  $request->date_end;

        $tmp = DB::table('tiktok_indicator_products')
            ->select(DB::raw('tiktok_product_id, indicator_type, count(*) as ind_count'))
            ->whereIn('tiktok_product_id', $product_ids);

        if ($date_start != "") {
            $tmp = $tmp->where('tiktok_indicator_products.created_at', '>=', $date_start);
        }

        if ($date_end != "") {
            $tmp = $tmp->where('tiktok_indicator_products.created_at', '<=', $date_end . ' 23:59:59');
        }

        $tmp->groupBy('indicator_type', 'tiktok_product_id');

        $visit_query = TiktokIndicator::where([
            'indicator_type' => 'click',
            'tiktok_id' => $tiktok_id,
        ]);

        if ($date_start != "") {
            $visit_query = $visit_query->where('created_at', '>=', $date_start);
        }

        if ($date_end != "") {
            $visit_query = $visit_query->where('created_at', '<=', $date_end . ' 23:59:59');
        }

        return [
            'visit_count' => $visit_query->count(),
            'data' => $tmp->get()->toArray()
        ];
    }
}
