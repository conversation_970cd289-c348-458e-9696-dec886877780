<?php

namespace App\Http\Controllers\TikTok;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\TiktokGroup;
use App\Tiktok;
use App\TiktokProduct;
use App\User;
use App\TiktokGroupRelationship;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApiController extends Controller
{
    public function slider (Request $request)
    {
        // qidからgroup_idに変換
        $qid = $request->qid;
        $g = TiktokGroup::where('qid', $qid)->first();
        if (!$g || !$g->is_active) {
            return '';
        }

        // バージョンによって表示するスライダーを変更する
        $ver = $request->v;
        switch ($ver) {
            case 2:
                $css = 'slider/slider_tiktok.css';
                $js = 'tiktoks/embed_v2.js';
                break;
            default:
                $css = 'slider.css';
                $js = 'tiktoks/embed.js';
                break;
        }

        // グループにABテストが設定されている場合50%の確率でそれに変更する
        if (!empty($g->abtest_group_id)) {
            if (mt_rand(1, 100) > 50) {
                $g_b = TiktokGroup::where('id', $g->abtest_group_id)->first();

                // Bグループ表示にする
                // もしBグループが非アクティブだった場合何もしない
                if ($g_b && $g_b->is_active){
                    $g = $g_b;
                    $qid = $g_b->qid;
                }
            }
        }

        $tiktoks = (new TiktokGroup())->get_tiktoks_from_group_id($g->id);
        $video_ids = $tiktoks->map(function($v){ return $v->video_id; });

        $products = (new TiktokProduct())->get_product_from_ig_ids($video_ids, $g->company_id);
        $is_preview = $request->is_preview;

        return view('tiktok/api/slider', compact('tiktoks', 'products', 'qid', 'g','css','js', 'is_preview'))->render();
    }

    public function cv (Request $request)
    {
        $qid = $request->qid;
        return view('tiktok/api/cv', compact('qid'))->render();
    }

    public function get_option (Request $request)
    {
        // qidからgroup_idに変換
        $qid = $request->qid;
        $g = TiktokGroup::where('qid', $qid)->first();

        if ($g) {
            return $g;
        } else {
            return [];
        }
    }

    public function indicate (Request $request)
    {
        $video_ids = $request->get('igs');
        $qid = $request->get('qid');
        $type = $request->get('type');

        // qidからどのgroup_idかを決定する
        $g = TiktokGroup::where('qid', $qid)->first();

        if ($g) {
            // $igからどのtiktok.idかを決定する
            $tiktok_ids = Tiktok::whereIn('video_id', explode(',', $video_ids))->pluck('id');

            return DB::transaction(function() use ($tiktok_ids, $g, $type) {
                // インクリメント
                TiktokGroupRelationship::whereIn('tiktok_id', $tiktok_ids)->where('tiktok_group_id', $g->id)->increment($type);

                // indicatorにも入れる
                $data = [];
                foreach ($tiktok_ids as $tiktok_id) {
                    $data[] = [
                        'indicator_type' => $type,
                        'tiktok_id' => $tiktok_id,
                        'tiktok_group_id' => $g->id,
                        'created_at' => date("Y-m-d H:i:s")
                    ];
                }
                DB::table('tiktok_indicators')->insert($data);
            });
        } else {
            return '';
        }
    }

    public function indicate_noig (Request $request)
    {
        $g = TiktokGroup::where('qid', $request->qid)->first();
        if ($g) {
            // group_look追加
            DB::table('tiktok_indicators')->insert([
                [
                    'indicator_type' => 'group_look',
                    'tiktok_id' => 0,
                    'tiktok_group_id' => $g->id,
                    'created_at' => date("Y-m-d H:i:s"),
                ]
            ]);
        }
    }

    public function indicate_product (Request $request)
    {
        $product_ids = $request->get('product_ids');
        $qid = $request->get('qid');
        $type = $request->get('type'); // click or cv

        // qidは使用しないが他userのproduct_idに影響を及ぼしにくくするため有効性のチェック
        $g = TiktokGroup::where('qid', $qid)->first();

        if ($g) {
            $product_ids = explode(',', $product_ids);
            if (count($product_ids) == 0) {
                return '';
            }

            return DB::transaction(function() use ($product_ids, $type) {
                // インクリメント
                TiktokProduct::whereIn('id', $product_ids)->increment($type);

                // indicatorにも入れる
                $data = [];
                foreach ($product_ids as $product_id) {
                    $data[] = [
                        'indicator_type' => $type,
                        'tiktok_product_id' => $product_id,
                        'created_at' => date("Y-m-d H:i:s")
                    ];
                }
                DB::table('tiktok_indicator_products')->insert($data);
            });
        } else {
            return '';
        }
    }
}
