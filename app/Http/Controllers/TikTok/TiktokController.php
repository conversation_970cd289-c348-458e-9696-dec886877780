<?php

namespace App\Http\Controllers\TikTok;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Tiktok;
use App\TiktokProduct;
use App\TiktokGroup;
use App\TiktokGroupRelationship;
use App\TiktokProductRelationship;
use App\TiktokFavorite;
use App\TiktokGroupCustomOrder;
use App\TiktokSearch;

use App\SecondUsage;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Session;

use Aws\S3\S3Client;
use Aws;

class TiktokController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $groups = TiktokGroup::where(['company_id' => Auth::user()->company_id])->get();
        $products = TiktokProduct::where('company_id', Auth::user()->company_id)->get();

        return view('/tiktok/tiktoks/index', compact('groups', 'products'));
    }

    public function set_products (Request $request)
    {
        try
        {
            $product_ids =  $request->product_ids;
            $author_name = $request->author_name;
            $content = $request->content;
            $comment_count = $request->comment_count;
            $digg_count = $request->digg_count;
            $play_count = $request->play_count;
            $share_count = $request->share_count;
            $image = $request->image;
            $permalink = $request->permalink;
            $thumbnail_url = $request->thumbnail_url;
            $video_id = $request->video_id;
            $company_id = Auth::user()->company_id;
            $video_url = $request->video_url;

            $result = DB::transaction(function() use ($product_ids, $author_name, $content, $comment_count,
                $digg_count, $play_count, $share_count, $image, $permalink, $thumbnail_url, $video_id, $company_id, $video_url) {
                $tiktok = Tiktok::updateOrCreate([
                    'company_id' => $company_id,
                    'video_id' => $video_id
                ],
                [
                    'author_name' => $author_name,
                    'content' => $content,
                    'comment_count' => $comment_count,
                    'digg_count' => $digg_count,
                    'play_count' => $play_count,
                    'share_count' => $share_count,
                    'image' => $image,
                    'permalink' => $permalink,
                    'thumbnail_url' => $thumbnail_url,
                    'like_count' => 0,
                    'video_url' => $video_url
                ]);

                foreach($product_ids as $product_id){
                    $tiktok_product = TiktokProductRelationship::updateOrCreate([
                        'company_id' => $company_id,
                        'tiktok_id' => $tiktok->id,
                        'tiktok_product_id' => $product_id
                    ], []);
                }
            });

            return ['statusCode' => 200, 'msg' => '登録しました'];
        } catch (Throwable $e) {
            report($e);

            return ['statusCode' => 500];
        }
    }

    public function set_group (Request $request)
    {
        try
        {
            $group_id =  $request->group_id;
            $author_name = $request->author_name;
            $content = $request->content;
            $comment_count = $request->comment_count;
            $digg_count = $request->digg_count;
            $play_count = $request->play_count;
            $share_count = $request->share_count;
            $image = $request->image;
            $permalink = $request->permalink;
            $thumbnail_url = $request->thumbnail_url;
            $video_id = $request->video_id;
            $company_id = Auth::user()->company_id;
            $video_url = $request->video_url;

            $result = DB::transaction(function() use ($group_id, $author_name, $content, $comment_count,
                $digg_count, $play_count, $share_count, $image, $permalink, $thumbnail_url, $video_id, $company_id, $video_url) {
                $tiktok = Tiktok::updateOrCreate([
                    'company_id' => $company_id,
                    'video_id' => $video_id
                ],
                [
                    'author_name' => $author_name,
                    'content' => $content,
                    'comment_count' => $comment_count,
                    'digg_count' => $digg_count,
                    'play_count' => $play_count,
                    'share_count' => $share_count,
                    'image' => $image,
                    'permalink' => $permalink,
                    'thumbnail_url' => $thumbnail_url,
                    'like_count' => 0,
                    'video_url' => $video_url
                ]);

                $tiktok_product = TiktokGroupRelationship::updateOrCreate([
                    'company_id' => $company_id,
                    'tiktok_id' => $tiktok->id,
                    'tiktok_group_id' => $group_id
                ], []);

                $group_custom_orders = TiktokGroupCustomOrder::updateOrCreate([
                    'tiktok_id' => $tiktok->id,
                    'tiktok_group_id' => $group_id,
                    'sort_id' => 0
                ],[]);
            });

            return ['statusCode' => 200, 'msg' => '登録しました'];
        } catch (Throwable $e) {
            report($e);

            return ['statusCode' => 500];
        }
    }

    public function search_video_by_id(Request $request)
    {
        try {
            $permalink = $request->input('permalink');
            if($permalink === '') {
                return ['statusCode' => 500];
            }

            $arr_data_split = explode('/video/', $permalink);
            $video_id = explode('?', $arr_data_split[1])[0];

            $file = fopen(public_path('keyword_search_video.csv'), 'w');
            $columns = array('video_id');
            fputcsv($file, $columns);

            $row['video_id'] = $video_id;
            fputcsv($file, array($row['video_id']));
            fclose($file);

            $local_filepath = public_path() . "/storage/tmp/tiktok_video/{$video_id}.mp4";

            exec("python3 " . app_path() . "/Providers/TiktokApiGetVideoStatistics.py 2>&1",$output);
            Log::info($output);
            $command_download = "python3 -m tiktok_downloader --url '" . $permalink . "' --snaptik --save " . $local_filepath;
            shell_exec($command_download);

            $data_csv = TiktokController::get_video_data();
            if(!empty($data_csv)) {
                unset($data_csv[0]);
            }

            $check_video_file_exists = file_exists($local_filepath);
            $data = $data_csv[1];
            if ($data[1] == '404' || !$check_video_file_exists) {
                return ['statusCode' => 500];
            } else {
                $s3_upload_tiktok_video = TiktokController::s3_upload_tiktok_video($video_id, $local_filepath);
                $object_url = $s3_upload_tiktok_video['result']['ObjectURL'];
                $r2_bucket = config('app.aws_r2_bucket');
                $object_base_url = config('app.CLOUDFLARE_R2_ENDPOINT').'/';
                $r2_url = config('app.CLOUDFLARE_R2_URL');
                $object_url = str_replace($r2_bucket.'.', '', $object_url);
                $video_url = str_replace($object_base_url, $r2_url, $object_url);
                Log::info('$object_url:'.$object_url);
                Log::info('$object_base_url:'.$object_base_url);
                Log::info('$r2_url:'.$r2_url);
                Log::info('$video_url:'.$video_url);

                return [
                    'statusCode' => 200,
                    'data' => [
                        'video_id' => $video_id,
                        'permalink' => $permalink,
                        'video_comment_count' => $data[1],
                        'video_digg_count' => $data[2],
                        'video_play_count' => $data[3],
                        'video_share_count' => $data[4],
                        'video_content' => $data[5],
                        'video_thumbnail_url' => $data[6],
                        'video_author_name' => $data[7],
                        'video_image'=> $data[8],
                        'video_url'=> $video_url
                    ]
                ];
            }
        } catch (Throwable $e) {
            report($e);

            return ['statusCode' => 500];
        }
    }

    public function get_video_data()
    {
        $data_csv = [];
        if (($open = fopen(public_path('tiktok_video.csv'), 'r')) !== FALSE) {
            while (($data = fgets($open)) !== FALSE) {
                $data_csv[] = explode(",",$data);
            }

            fclose($open);
        }

        return $data_csv;
    }

    public function s3_upload_tiktok_video($video_id, $local_filepath)
    {
        $postfix = '';
        $prefix = 'tiktok/videos/';
        $credentials = new Aws\Credentials\Credentials(
            config('app.aws_r2_access_key_id'),
            config('app.aws_r2_secret_access_key')
        );

        $endpoint = config('app.CLOUDFLARE_R2_ENDPOINT');

        $s3_client = new S3Client([
            'credentials' => $credentials,
            'endpoint' => $endpoint,
            'region' => 'auto',
            'version' => 'latest'
        ]);

        // S3 upload
        try {
            $result = $s3_client->putObject([
                'Bucket' => config('app.aws_r2_bucket'),
                'Key' =>  $prefix.$video_id . $postfix . '.' . 'mp4',
                'SourceFile' => $local_filepath,
            ]);
        } catch (\Exception $e) {
            return ['status' => false, 'message' => $e->getMessage()];
        }

        unlink($local_filepath);

        return ['status' => true, 'result' => $result ];
    }
}
