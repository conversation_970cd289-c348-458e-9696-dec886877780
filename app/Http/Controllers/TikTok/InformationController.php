<?php

namespace App\Http\Controllers\TikTok;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Information;
use App\InformationReading;
use Session;

class InformationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $infos = Information::select('information.*', 'information_id as read')->leftjoin('information_readings', function ($join) {
            $join->on('information_readings.information_id', '=', 'information.id')
              ->where('information_readings.company_id', '=', Auth::user()->company_id);
            })->orderBy('information.created_at', 'desc')->get();

        return view('tiktok/information/index', compact('infos'));
    }

    public function show ($id)
    {
        $info = Information::findOrFail($id);

        // 既読を付ける
        InformationReading::updateOrCreate([
            'information_id' => $id, 'company_id' => Auth::user()->company_id
        ],[]);

        // 未読新着があるかどうかチェック
        $infos = Information::select('information.*', 'information_id as read')->leftjoin('information_readings', function ($join) {
            $join->on('information_readings.information_id', '=', 'information.id')
              ->where('information_readings.company_id', '=', Auth::user()->company_id);
            })->whereNull('information_id')->get();
        Session::put('not_read_info', count($infos));

        return view('tiktok/information/show', compact('info'));
    }

}
