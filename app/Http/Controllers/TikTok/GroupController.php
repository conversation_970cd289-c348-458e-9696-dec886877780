<?php

namespace App\Http\Controllers\TikTok;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\TiktokGroup;
use App\Tiktok;
use App\TiktokGroupRelationship;
use App\TiktokProductRelationship;
use App\TiktokProduct;
use App\TiktokGroupCustomOrder;
use App\TiktokGroupAbTest;
use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class GroupController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    //Tiktokのグループを取得
    public function index ()
    {
        $groups = TiktokGroup::where(['company_id' => Auth::user()->company_id])->get();

        return view('/tiktok/group/index', compact('groups'));
    }

    public function new ()
    {
        $group = new TiktokGroup();
        // デフォルト値の設定
        $group->set_default();
        $is_edit = false;
        $ab_target_groups = TiktokGroup::where('company_id', Auth::user()->company_id)->where('is_active', 1)->get();

        return view('/tiktok/group/new', compact('group', 'is_edit', 'ab_target_groups'));
    }

    public function testpage (Request $request)
    {
        $device = $request->device ? $request->device : 'sd';
        $qid = $request->qid;

        return view('/tiktok/group/test', compact('device', 'qid'));
    }

    public function edit (Request $request)
    {
        $group = TiktokGroup::where('company_id', Auth::user()->company_id)->where('id', $request->id)->first();
        $is_edit = true;
        $ab_target_groups = TiktokGroup::where('company_id', Auth::user()->company_id)->where('is_active', 1)->whereNotIn('id', [$group->id])->get();

        return view('tiktok/group/new', compact('group', 'is_edit', 'ab_target_groups'));
    }

    // グループの作成
    public function create (Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
        ]);

        // 管理画面での設定を超過していないかチェック
        $group_count = TiktokGroup::where('company_id', Auth::user()->company_id)->count();
        $limit = Auth::user()->company_tied ? Auth::user()->company_tied->get_ugc_group_limit() : INF;
        if ($limit < $group_count) {
            return redirect('/tiktok/groups')->with('flash_message', '登録出来ませんでした。制限を超えています(' . $group_count . '/' . $limit . ')');
        }

        if ($request->file('header_image')) {
            $path = $request->file('header_image')->store('public/header_images');
            $path = explode('/', $path)[2];
        } else {
            $path = $request['header_image'];
        }

        try {
            DB::transaction(function() use ($request, $path) {
                $group = TiktokGroup::create([
                    'company_id' => Auth::user()->company_id,
                    'name' => $request['name'],
                    'url' => $request['url'],
                    'display_type' => $request['display_type'],
                    'is_display_comment' => $request['is_display_comment'],
                    'header_image' => $path,
                    'main_color' => $request['main_color'],
                    'border_color' => $request['border_color'],
                    'sd_short' => $request['sd_short'],
                    'sd_wide' => $request['sd_wide'],
                    'pc_short' => $request['pc_short'],
                    'pc_wide' => $request['pc_wide'],
                    'more_text' => $request['more_text'],
                    'modal_background_color' => $request['modal_background_color'],
                    'qid' => 'qid',
                    'is_border_type' => $request['is_border_type'],
                    'bottom_text_align' => $request['bottom_text_align'],
                    'bottom_right_text_option' => $request['bottom_right_text_option'],
                    'bottom_right_text' => $request['bottom_right_text'],
                ]);

                $group->update(['qid' => md5($group->id . 'group')]);
            });
        } catch (Throwable $e) {
            //return $e->getMessage();
            return redirect('/tiktok/groups')->with('flash_message', '作成に失敗しました');
        }

        return redirect('/tiktok/groups')->with('flash_message', '作成しました');
    }

    // グループの更新
    public function update (Request $request)
    {
        $request->validate([
            'name' => 'required|max:255',
        ]);
        if ($request->file('header_image')) {
            $path = $request->file('header_image')->store('public/header_images');
            $path = explode('/', $path)[2];
        } else {
            $path = $request['header_image'];
        }

        try {
            DB::transaction(function() use ($request, $path) {
                $group = TiktokGroup::where('company_id', Auth::user()->company_id)->find($request->id);
                $prev_header_image = $group->header_image;

                $group->update([
                    'name' => $request['name'],
                    'url' => $request['url'],
                    'display_type' => $request['display_type'],
                    'is_display_comment' => $request['is_display_comment'],
                    'header_image' => $path,
                    'main_color' => $request['main_color'],
                    'border_color' => $request['border_color'],
                    'sd_short' => $request['sd_short'],
                    'sd_wide' => $request['sd_wide'],
                    'pc_short' => $request['pc_short'],
                    'pc_wide' => $request['pc_wide'],
                    'more_text' => $request['more_text'],
                    'modal_background_color' => $request['modal_background_color'],
                    'is_border_type' => $request['is_border_type'],
                    'bottom_right_text_option' => $request['bottom_right_text_option'],
                    'bottom_right_text' => $request['bottom_right_text'],
                    'bottom_text_align' => $request['bottom_text_align'],
                ]);

                if ($request->file('header_image')) {
                    Storage::delete('public/header_images/' . $prev_header_image);
                }
            });
        } catch (Throwable $e) {
            return redirect('/tiktok/groups')->with('flash_message', '更新に失敗しました');
        }

        return redirect('/tiktok/groups')->with('flash_message', '更新しました');
    }

    // グループの削除
    public function delete ($id)
    {
        // 削除対象の権限があるかチェック
        $group = TiktokGroup::find($id);

        if ($group->company_id != Auth::user()->company_id) {
            return redirect('/tiktok/groups')->with('flash_message', '削除できませんでした（権限なし）');
        }

        $group->delete();

        return redirect('/tiktok/groups')->with('flash_message', '削除しました');
    }

    // 個別インスタグラム投稿の削除
    public function tiktok_group_delete ($tiktok_id, $group_id)
    {
        // 削除対象の権限があるかチェック
        $group = TiktokGroup::find($group_id);

        if ($group->company_id != Auth::user()->company_id) {
            return redirect('/tiktok/groups')->with('flash_message', '削除できませんでした（権限なし）');
        }

        $tiktok = Tiktok::where('id', $tiktok_id)->where('company_id', Auth::user()->company_id)->first();
        if(!$tiktok) {
            return redirect('/tiktok/groups')->with('flash_message', '削除できませんでした');
        }

        // グループとの紐づけ削除
        $tiktok_group = TiktokGroupRelationship::where('tiktok_group_id', $group_id)->where('tiktok_id', $tiktok->id);
        $tiktok_group->delete();
        $tiktok_group_custom_order = TiktokGroupCustomOrder::where('tiktok_group_id', $group_id)->where('tiktok_id', $tiktok->id);
        $tiktok_group_custom_order->delete();

        // 商品との紐づけ削除
        $tiktok_product = TiktokProductRelationship::where('tiktok_id', $tiktok->id)->where('company_id', Auth::user()->company_id);
        $tiktok_product->delete();

        return redirect('/tiktok/group/show/' . $group_id)->with('flash_message', '削除しました');
    }

    // ダッシュボード
    public function show ($id)
    {
        $group = TiktokGroup::where('company_id', Auth::user()->company_id)->where('id', $id)->first();
        if (!$group) {
            return redirect('/tiktok/groups')->with('flash_message', '指定されたグループは存在しません');
        }

        $tiktoks = (new TiktokGroup())->get_tiktoks_from_group_id($id);
        $video_ids = $tiktoks->map(function($v){ return $v->video_id; });

        return view('/tiktok/group/show', compact('group', 'tiktoks'));
    }

    // ダッシュボード（ABテスト）
    public function show_abtest ($id)
    {
        $group = TiktokGroup::where('company_id', Auth::user()->company_id)->where('id', $id)->first();
        if (!$group) {
            return redirect('/tiktok/groups')->with('flash_message', '指定されたグループは存在しません');
        }
        $abtests = TiktokGroupAbTest::where('company_id', Auth::user()->company_id)->where('tiktok_group_id', $id)->orderBy('created_at', 'desc')->get();
        $group_digg_count[] = [];
        $group_digg_count[0] = 0;
        $group_digg_count[$id] = $group->tiktoks->sum('digg_count');

        $results = [];
        foreach($abtests as $abtest) {
            $tmp = DB::table('tiktok_groups')
            ->select(DB::raw("tiktok_groups.id as group_id, tiktok_groups.name as group_name, sum(case when indicator_type = 'look' then 1 else 0 end) as look, sum(case when indicator_type = 'click' then 1 else 0 end) as click, sum(case when indicator_type = 'cv' then 1 else 0 end) as cv, sum(case when indicator_type = 'group_look' then 1 else 0 end) as group_look"))
            ->leftjoin('tiktok_indicators', 'tiktok_indicators.tiktok_group_id', '=', 'tiktok_groups.id')
            ->whereIn('tiktok_groups.id', [$id, $abtest->target_group_id]);

            if (!empty($abtest->end_at)){
                $tmp = $tmp->whereBetween('tiktok_indicators.created_at', [$abtest->start_at, $abtest->end_at]);
            } else {
                $tmp = $tmp->where('tiktok_indicators.created_at', '>=', $abtest->start_at);
            }

            // 順番はAグループを必ず上に
            $tmp = $tmp->groupBy('tiktok_groups.id')->groupBy('tiktok_groups.name')
                ->orderByRaw("FIELD(tiktok_groups.id, {$id}, {$abtest->target_group_id})");
            $data = $tmp->get();

            $groups = [
                TiktokGroup::where('id', $abtest->tiktok_group_id)->first(),
                TiktokGroup::where('id', $abtest->target_group_id)->first()
            ];
            if (isset($groups[1])) {
                $group_digg_count[$abtest->target_group_id] = $groups[1]->tiktoks->sum('digg_count');
            } else {
                $group_digg_count[$abtest->target_group_id] = 0;
            }

            // データが無かったら０で埋める
            $result = [];
            foreach([0, 1] as $i) {
                if (!$data->slice($i, 1)->isEmpty()) {
                    $result[$i] = [
                        'tiktok_group_id' => $data[$i]->group_id,
                        'tiktok_group_name' => $data[$i]->group_name,
                        'group_look' => $data[$i]->group_look,
                        'look' => $data[$i]->look,
                        'click' => $data[$i]->click,
                        'cv' => $data[$i]->cv,
                    ];
                } else {
                    $result[$i] = [
                        'tiktok_group_id' => isset($groups[$i]) ? $groups[$i]->id : 0,
                        'tiktok_group_name' => isset($groups[$i]) ? $groups[$i]->name : '-',
                        'group_look' => 0,
                        'look' => 0,
                        'click' => 0,
                        'cv' => 0,
                    ];
                }
            }

            $results[] = $result;
        }

        return view('tiktok/group/show_abtest', compact('group', 'results', 'abtests', 'group_digg_count'));
    }

    // 期間指定で指標データを返す
    public function indicate (Request $request)
    {
        $group_id = $request['group_id'];
        $date_start =  $request['date_start'];
        $date_end =  $request['date_end'];

        $tmp = DB::table('tiktok_indicators')
            ->select(DB::raw('video_id, indicator_type, count(*) as ind_count'))
            ->join('tiktoks', 'tiktok_indicators.tiktok_id', '=', 'tiktoks.id')
            ->where('tiktoks.company_id', Auth::user()->company_id);

        if ($request['group_id']) {
            $tmp = $tmp->where('tiktok_group_id', $group_id);
        }

        if ($date_start != "") {
            $tmp = $tmp->where('tiktok_indicators.created_at', '>=', $date_start);
        }

        if ($date_end != "") {
            $tmp = $tmp->where('tiktok_indicators.created_at', '<=', $date_end . ' 23:59:59');
        }

        $tmp->groupBy('indicator_type', 'video_id');

        return $tmp->get();
    }

    // グループ一覧画面で保存ボタンを押したとき
    public function group_list_save(Request $request)
    {
        foreach($request['post_data'] as $a){
            $group = TiktokGroup::where('id', $a['group_id'])->first();

            $prev_abtest_group_id = $group->abtest_group_id;

            $group->is_active = $a['is_active'];
            $group->abtest_group_id = $a['abtest'] == '' ? NULL : $a['abtest'];
            $group->save();

            // ABテストテーブルの更新
            if (!empty($group->abtest_group_id)) {
                if (empty($prev_abtest_group_id)) {
                    // 「指定なし」状態 -> 何か指定　にした場合
                    TiktokGroupAbTest::create([
                        'company_id' => Auth::user()->company_id,
                        'tiktok_group_id' => $group->id,
                        'target_group_id' => $group->abtest_group_id,
                        'start_at' => date("Y-m-d H:i:s")
                    ]);
                } else {
                    // 何か指定 -> 別の何か指定　にした場合
                    if ($group->abtest_group_id != $prev_abtest_group_id) {
                        TiktokGroupAbTest::where([
                            'tiktok_group_id' => $group->id,
                            'target_group_id' => $prev_abtest_group_id
                        ])->whereNull('end_at')->update([
                            'end_at' => date("Y-m-d H:i:s")
                        ]);
                        TiktokGroupAbTest::create([
                            'company_id' => Auth::user()->company_id,
                            'tiktok_group_id' => $group->id,
                            'target_group_id' => $group->abtest_group_id,
                            'start_at' => date("Y-m-d H:i:s")
                        ]);
                    }
                }
            } else {
                // ABテストを「指定なし」にした場合
                if (!empty($prev_abtest_group_id)) {
                    // 何か指定されていた状態から「指定なし」にされていれば該当テーブルに修了時刻を入れる
                    TiktokGroupAbTest::where([
                        'tiktok_group_id' => $group->id,
                        'target_group_id' => $prev_abtest_group_id
                    ])->whereNull('end_at')->update([
                        'end_at' => date("Y-m-d H:i:s")
                    ]);
                }
            }
        }

        return ['result'=> 'success'];
    }

    // UGCセット内ソート順の保存
    public function sort_save(Request $request)
    {
        $display_order = $request['display_order'];
        $group_id = $request['id'];

        $group = TiktokGroup::find($group_id);
        $group->update(['display_order' => $display_order]);

        // 一度削除して更新
        if ($display_order == 'custom') {
            try {
                DB::transaction(function() use ($group, $request) {
                    TiktokGroupCustomOrder::where('tiktok_group_id', $group->id)->delete();
                    foreach($request['sort_ids'] as $i => $sort_id){
                        if ($sort_id == '') continue;
                        $ins = Tiktok::where('video_id', $request['video_ids'][$i])->where('company_id', Auth::user()->company_id)->first();
                        $tiktok_group_custom_order = new TiktokGroupCustomOrder();
                        $tiktok_group_custom_order->sort_id = $sort_id;
                        $tiktok_group_custom_order->tiktok_group_id = $group->id;
                        $tiktok_group_custom_order->tiktok_id = $ins->id;
                        $tiktok_group_custom_order->save();
                    }
                });
            } catch (Throwable $e) {
                return redirect('/tiktok/group/show/' . $group_id)->with('flash_message', '更新に失敗しました');
            }
        }
        return redirect('/tiktok/group/show/' . $group_id)->with('flash_message', '更新しました');
    }
}
