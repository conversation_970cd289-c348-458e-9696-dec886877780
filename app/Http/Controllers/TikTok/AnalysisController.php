<?php

namespace App\Http\Controllers\TikTok;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Tiktok;

class AnalysisController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $tiktoks = (new Tiktok())->get_tiktoks_from_company_id(Auth::user()->company_id);
        return view('/tiktok/analysis/index', compact('tiktoks'));
    }
}
