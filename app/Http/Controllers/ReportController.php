<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Report;
use App\Instagram;
use App\Group;

class ReportController extends Controller
{

    public function new (Request $request)
    {
        $qid = $request['qid'];
        $ig_id =  $request['ig_id'];
        $ig = Instagram::where('ig_id', $ig_id)->first();

        $report = new Report();

        return view('reports/new', compact('ig', 'ig_id', 'qid', 'report'));
    }

    public function create (Request $request)
    {
        $qid = $request['qid'];
        $ig_id =  $request['ig_id'];
        $ig = Instagram::where('ig_id', $ig_id)->first();
        $group = Group::where('qid', $qid)->first();

        $report = new Report();

        // qidとig_idの整合性チェック
        if ($group) {
            $correct_ig_ids = $group->instagrams()->pluck('ig_id');
        }

        if(!$group || !in_array($ig_id, $correct_ig_ids->all()))
        return view('reports/new', compact('ig', 'ig_id', 'qid', 'report'))->with(
            'flash_message', 'URLに誤りがあります'
        );

        // reportsテーブルに保存
        $report->email = $request['email'];
        $report->content = $request['content'];
        $report->ig_id = $ig_id;
        $report->group_id = $group->id;

        $report->save();

        return view('reports/create');
    }


}
