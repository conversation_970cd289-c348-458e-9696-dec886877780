<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Instagram;
use App\Indicate;

class AnalysisController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index ()
    {
        $instagrams = (new Instagram())->get_ig_from_company_id(Auth::user()->company_id);
        if($instagrams){
            $ids = '';
            foreach($instagrams as $i => $ig){
                // 計算処理用のidを割り振る
                $instagrams[$i]['id_for_calc'] = $i;
                $ids .= $ig['ig_id'].$i.'_'.$ig['instagram_id'].',';

                if (strpos($ig->thumbnail_url, '.mp4') !== false) {
                    $ext = 'video';
                }else{
                    $ext = 'img';
                }
                $instagrams[$i]['ext'] = $ext;
            }
            $ids = rtrim($ids,',');
        }
        return view('analysis/index', compact('instagrams', 'ids'));
    }


}
