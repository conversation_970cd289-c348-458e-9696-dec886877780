<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\LineMember;
use App\LineQrSetting;
use App\LineSetting;
use App\LineAutoSetting;
use App\library\LineSender;

class LineController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('site');
    }

    public function index ()
    {
        $line_members = LineMember::where('company_id', Auth::user()->company_id);

        return view('line/index', compact('line_members'));
    }

    public function search (Request $request)
    {
        $res = $request->all();
        $line_members = LineMember::where('company_id', Auth::user()->company_id)->where('follow_status', 'follow');

        if (array_key_exists('query', $res) && $res['query']){
            $res2 = $res['query'];

            if(array_key_exists('status', $res2) && $res2['status'] != ''){
                if ($res2['status'] == 'new') {
                    $line_members->where('line_send_count', 0);
                } else if ($res2['status'] == 'old'){
                    $line_members->whereRaw('line_send_count >= 1');
                }
            }
        }

        $meta = [
            "page" => 1,
            "pages" => 1,
            "perpage" => -1,
            "total" => $line_members->count(),
            "sort" => "asc",
            "field" => "id"
        ];
        $data = $line_members->get()->toArray();

        return ['meta' => $meta, 'data' => $data];
    }

    public function send (Request $request)
    {
        $ids = $request->ids;
        $line_members = LineMember::where('company_id', Auth::user()->company_id)
            ->whereIn('id', $ids)->where('follow_status', 'follow')->get();
        $ls = LineSetting::where('company_id', Auth::user()->company_id)->first();
        $las = LineAutoSetting::where('company_id', Auth::user()->company_id)->first();
        if (!$ls || !$las) {
            return ['status' => false, 'message' => 'LINEの設定/自動設定がされていません'];
        }
        $res = LineSender::text_and_image($line_members, $ls, $las);
        return $res;
    }

    public function qr ()
    {
        $ls = LineQrSetting::where('company_id', Auth::User()->company_id)->first();
        if (!$ls) {
            $ls = new LineQrSetting();
            $ls->set_default();
        }

        return view('line/qr', compact('ls'));
    }

    public function qr_create (Request $request)
    {
        $arr = $request->all();

        if ($request->id) {
            // 更新
            $l = LineQrSetting::where('company_id', Auth::user()->company_id)->first();
            unset($arr['id']);
            $l->fill($arr);
        } else {
            // 作成
            $arr = $request->all();
            $l = new LineQrSetting($arr);
        }
        $l->company_id = Auth::user()->company_id;
        $l->site_id = Site::where('company_id', Auth::user()->company_id)->first()->id;
        $l->save();

        return redirect('/reviews/line/qr')->with('flash_message', ['type' => 'success', 'msg' => '更新しました']);
    }


}
