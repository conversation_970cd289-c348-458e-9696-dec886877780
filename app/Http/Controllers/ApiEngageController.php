<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;
use App\User;
use App\Site;
use App\Engage;


class ApiEngageController extends Controller
{

    public function _common_auth($user_id, $request) {

        $user = User::find($user_id)->first();

        if (!$user) {
            return ['status' => false, 'msg' => 'Bad Authority(user)'];
        }

        $product = Product::where('company_id', $user->company_id)->where('product_display_id', $request->product_display_id)->first();

        // サイト設定をしてない場合
        $site = Site::where('company_id', $user->company_id)->first();
        if (!$site) {
            return ['status' => false, 'msg' => 'Bad Authority(no site setting)'];
        }

        return ['status' => true, 'product' => $product];
    }

    // api_reviews/embed.jsからsendされるデータを受け取る
    public function store (Request $request)
    {
        $user_id = $request->user_id;

        $res = $this->_common_auth($user_id, $request);
        if (!$res['status']) { return $res['msg']; }

        $product = $res['product'];

        $engage_type = $request->engage_type;
        $visitor_id = $request->visitor_id;

        $user = User::find($user_id);
        $data = [
            'company_id' => $user->company_id,
            'visitor_id' => $visitor_id,
            'engage_type' => $engage_type,
            'product_id' => !empty($product) ? $product->id : null,
        ];

        if (Engage::where($data)->count() == 0) {
            Engage::create($data);
        }

        return ['status' => true];
    }


}
