<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Product;
use App\Order;
use App\OrderProduct;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PendingOrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth:api');
        $user = Auth::guard('api')->user();
        if ($user && $user->expires_at < Carbon::now()) {
            echo "APIトークンの有効期限が切れています。更新してください。";
            exit();
        }
    }

    public function create(Request $request)
    {
        $id = '';
        try {
            DB::transaction(function() use ($request) {
                // もし既存の注文IDが来たら上書き
                $exist_order = Order::where([
                    'company_id'=> Auth::guard('api')->user()->company_id,
                    'display_order_id'=> $request->pending_order_info['display_order_id']
                ])->get()->toArray();

                // すでに存在していたらエラー
                if (!empty($exist_order)){
                    throw new Exception('already exists', 409);
                }

                $order = new Order();
                $order->company_id = Auth::guard('api')->user()->company_id;
                $order->name = $request->pending_order_info['name'];
                $order->nickname = $request->pending_order_info['nickname'];
                $order->email = $request->pending_order_info['email'];
                $order->phone = $request->pending_order_info['phone'];
                $order->display_order_id = $request->pending_order_info['display_order_id'];
                $order->order_date = $request->pending_order_info['order_date'];
                $order->currency = $request->pending_order_info['currency'];
                $order->order_type = $request->pending_order_info['order_type'];
                $order->save();

                // order_productのデータ作成
                $op_ids = $request->pending_order_info['order_product_ids'];
                $inserts = [];
                foreach($op_ids as $op_id){
                    if (empty($op_id)) { continue; }
                    // もしすでに存在していたらスキップ
                    if (OrderProduct::where(['company_id' => Auth::guard('api')->user()->company_id, 'order_id' => $order->id, 'product_id' => $op_id])->count() > 0) {
                        continue;
                    }
                    $inserts[] = [
                        'company_id' => Auth::guard('api')->user()->company_id,
                        'order_id' => $order->id,
                        'product_id' => $op_id,
                        'created_at' => date("Y-m-d H:i:s"),
                        'updated_at' => date("Y-m-d H:i:s")
                    ];
                }

                DB::table('order_products')->insert($inserts);
            });
        } catch (\Exception $e) {
            if ($e->getCode() == 409) {
                $info = [
                    'code' => 409,
                    'message' => 'already exists'
                ];
            }else{
                $info = [
                    'code' => 400,
                    'message' => 'error'
                ];
            }
            
            return $info;
        }

        // API実行企業の最新の注文IDを返す
        $id = Order::where('company_id',Auth::guard('api')->user()->company_id)->where('display_order_id',$request->pending_order_info['display_order_id'])->first()->id;

        $info = [
            'code' => 200,
            'message' => 'success',
            'order_id' => $id
        ];

        return $info;

    }

    public function bulkInsert(Request $request)
    {
        try {
            $order_info = $request->pending_order_info;
            $chunks = array_chunk($order_info, 500);
            DB::transaction(function() use ($chunks){
                $chank_num = 0;
                foreach ($chunks as $i => $chunk) {
                    foreach ($chunk as $order_data) {
                        // 各項目が空だったらエラー
                        if (empty($order_data['name'])) { 
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['display_order_id'])){
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['nickname'])){
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['email'])){
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['phone'])){
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['order_date'])){
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['currency'])){
                            throw new Exception($chank_num, 405);
                        }
                        if(empty($order_data['order_type'])){
                            throw new Exception($chank_num, 405);
                        }

                        // すでに作成されていたらエラー
                        $exist_order = Order::where([
                            'company_id'=> Auth::guard('api')->user()->company_id,
                            'display_order_id'=> $order_data['display_order_id']
                        ])->get()->toArray();

                        if (!empty($exist_order)){
                            throw new Exception($chank_num, 409);
                        }

                        // もし既存の注文IDが来たら上書き
                        $order = new Order();
                        $order->name = $order_data['name'];
                        $order->company_id = Auth::guard('api')->user()->company_id;
                        $order->nickname = $order_data['nickname'];
                        $order->email = $order_data['email'];
                        $order->phone = $order_data['phone'];
                        $order->display_order_id = $order_data['display_order_id'];
                        $order->order_date = $order_data['order_date'];
                        $order->currency = $order_data['currency'];
                        $order->order_type = $order_data['order_type'];
                        $order->save();

                        // order_productのデータ作成
                        $op_ids = $order_data['order_product_ids'];

                        $inserts = [];
                        foreach($op_ids as $op_id){
                            if (empty($op_id)) { continue; }
                            // もしすでに存在していたらスキップ
                            if (OrderProduct::where(['company_id' => Auth::guard('api')->user()->company_id, 'order_id' => $order->id, 'product_id' => $op_id])->count() > 0) {
                                continue;
                            }
                            $inserts[] = [
                                'company_id' => Auth::guard('api')->user()->company_id,
                                'order_id' => $order->id,
                                'product_id' => $op_id,
                                'created_at' => date("Y-m-d H:i:s"),
                                'updated_at' => date("Y-m-d H:i:s")
                            ];
                        }
                        DB::table('order_products')->insert($inserts);

                        $chank_num ++;
                    }

                    // バルクインサート確認用
                    var_dump($i);
                }
            });
        } catch (\Exception $e) {
            $errorCols = (intval($e->getMessage()));
            if($e->getCode() == 409){
                $info = [
                    'code' => 409,
                    'message' => 'No. ' . $errorCols . ' already exist.' //すでに作成済み
                ];
            }elseif ($e->getCode() == 405) {
                $info = [
                    'code' => 405,
                    'message' => 'No. ' . $errorCols . ' missing parameter.' //不十分である
                ];
            }else{
                $info = [
                    'code' => 400,
                    'message' => 'error.',
                ];
            }
            
            return $info;
        }

        $info = [
            'code' => 200,
            'message' => 'success'
        ];

        return $info;

    }



    public function show(Request $request)
    {
        
        $id = $request->id;
        $company_id = Auth::guard('api')->user()->company_id;
        $order = Order::where('company_id',$company_id)->where('id',$id)->first();

        if(empty($order)){
            $info = [
                'code' => 400,
                'message' => 'not found'
            ];
            return $info;
        }else{
            $info = [
                'code' => 200,
                'message' => 'success',
                'order' => $order
            ];
        }
        return $info;
    }


    public function update(Request $request)
    {
        try {
            DB::transaction(function() use ($request) {
                $id = $request->id;
                $order = Order::find($id);
                if (empty($order)) {
                    throw new \Exception('not found', 400);
                }

                $order_info = $request->pending_order_info;
                if (isset($order_info['name']) && $order_info['name'] != '') {
                    $order->name = $order_info['name'];
                }
                if (isset($order_info['nickname']) && $order_info['nickname'] != '') {
                    $order->nickname = $order_info['nickname'];
                }
                if (isset($order_info['email']) && $order_info['email'] != '') {
                    $order->email = $order_info['email'];
                }
                if (isset($order_info['phone']) && $order_info['phone'] != '') {
                    $order->phone = $order_info['phone'];
                }
                if (isset($order_info['display_order_id']) && $order_info['display_order_id'] != '') {
                    $order->display_order_id = $order_info['display_order_id'];
                }
                if (isset($order_info['order_date']) && $order_info['order_date'] != '') {
                    $order->order_date = $order_info['order_date'];
                }
                if (isset($order_info['currency']) && $order_info['currency'] != '') {
                    $order->currency = $order_info['currency'];
                }
                if (isset($order_info['order_type']) && $order_info['order_type'] != '') {
                    $order->order_type = $order_info['order_type'];
                }
                $order->save();

                // order_productの元あるデータを削除して、新たにデータ作成
                if (isset($order_info['product_ids']) && $order_info['product_ids'] != '') {

                    if (OrderProduct::where(['company_id' => Auth::guard('api')->user()->company_id, 'order_id' => $order->id])->count() > 0) {
                        OrderProduct::where('order_id', $order->id)->delete();
                    }

                    $op_ids = $request->pending_order_info['product_ids'];
                        $inserts = [];
                        foreach($op_ids as $op_id){
                            if (empty($op_id)) { continue; }
                            
                            $inserts[] = [
                                'company_id' => Auth::guard('api')->user()->company_id,
                                'order_id' => $order->id,
                                'product_id' => $op_id,
                                'created_at' => date("Y-m-d H:i:s"),
                                'updated_at' => date("Y-m-d H:i:s")
                            ];
                        }

                        DB::table('order_products')->insert($inserts);
                }
            });
        } catch (\Exception $e) {
            $info = [
                'code' => $e->getCode(),
                'message' => $e->getMessage()
            ];
            return $info;
        }

        $id = $request->id;
        $order = Order::find($id);

        $info = [
            'code' => 200,
            'message' => 'success',
            'order' => $order
        ];

        return $info;
    }


    public function destroy(Request $request)
    {
        // 
    }

    public function updateToken(Request $request)
    {
        try {
            DB::transaction(function() use ($request) {
                $user = Auth::guard('api')->user();
                $user->api_token = Str::random(60);
                $user->expires_at = $request->expires_date;
                $user->save();
            });
        } catch (\Exception $e) {
            $info = [
                'code' => 400,
                'message' => 'failed update token'
            ];
            return $info;
        }

        $user = Auth::guard('api')->user();
        $info = [
            'code' => 200,
            'message' => 'success',
            'api_token' => $user->api_token
        ];

        return $info;
    }

    public function products(Request $request)
    {
        $user = Auth::guard('api')->user();
        $company_id = $user->company_id;

        if($user->company_id != $company_id){
            $info = [
                'code' => 400,
                'message' => 'failed'
            ];
            return $info;
        }

        $products = Product::where('company_id', $company_id)->get(['id', 'name'])->toArray();

        $info = [
            'code' => 200,
            'message' => 'success',
            'products' => $products
        ];

        return $info;
    }

    public function showCompanyId(Request $request)
    {
        $user = Auth::guard('api')->user();
        $company_id = $user->company_id;

        $info = [
            'code' => 200,
            'message' => 'success',
            'company_id' => $company_id
        ];

        return $info;
    }
}
