<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Review;
use App\ReviewWidget;
use App\ReviewWidgetReview;
use App\Product;
use App\Order;
use App\Site;
use App\library\CSVImport;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Mail\MailHandler;

class ReviewHtmlController extends Controller
{
    public function __construct()
    {
    }

    public function index (Request $request)
    {
        $html = file_get_contents( public_path() . "/UgcCreativeReviewArea.html", true);
        return response()->json(['html' => $html]);
    }
}