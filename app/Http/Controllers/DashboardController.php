<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Engage;
use App\Product;
use App\Order;
use App\Review;
use App\MailLog;
use App\MailTrackLog;
use Carbon\Carbon;


class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index (Request $request)
    {
        $startTime = microtime(true);

        // 1. 商品取得
        $products = Product::where('company_id', Auth::user()->company_id)->get();
        Log::info('1. 商品取得時間: ' . (microtime(true) - $startTime) . '秒');
        $startTime = microtime(true);
    
        $product_id = $request->product_id;
        $daterange = $request->daterange;
    
        // 2. エンゲージメントの計算
        $engage_q = DB::table('engages')->select(DB::raw('count(*) as c, engage_type'));
        $engage_q = $engage_q->where('company_id', Auth::user()->company_id);
        $engage_q = $this->_filter($engage_q, $product_id, $daterange);
        $d = $engage_q->groupBy('engage_type')->get();
    
        $engage = [];
        foreach (['visit', 'look_review', 'look_star', 'look_image'] as $i) {
            $tmp = $d->where('engage_type', $i)->first();
            if ($tmp) {
                $engage[$i] = $tmp->c;
            } else {
                $engage[$i] = 0;
            }
        }
    
        if ($engage['visit'] > 0) {
            $engage['review'] = $engage['look_review'] / $engage['visit'] * 100;
            $engage['star'] = $engage['look_star'] / $engage['visit'] * 100;
            $engage['image'] = $engage['look_image'] / $engage['visit'] * 100;
        } else {
            $engage['review'] = 0;
            $engage['star'] = 0;
            $engage['image'] = 0;
        }
        Log::info('2. エンゲージメント計算時間: ' . (microtime(true) - $startTime) . '秒');
        $startTime = microtime(true);
    
        // 3. エンゲージメントが高い商品
        $engage_q = DB::table('engages')->select(
            DB::raw("case when sum(case when engage_type = 'visit' then 1 else 0 end) = 0 then 0 else sum(case when engage_type = 'look_review' then 1 else 0 end) / sum(case when engage_type = 'visit' then 1 else 0 end) end as engage_rate, product_id")
        );
        $engage_q = $engage_q->where('company_id', Auth::user()->company_id);
        $engage_q = $this->_filter($engage_q, null, $daterange);
    
        $d = $engage_q->groupBy('product_id')->orderBy('engage_rate', 'desc')->first();
        if ($d) {
            $best_product = Product::find($d->product_id);
        } else {
            $best_product = null;
        }
        Log::info('3. エンゲージメント高い商品計算時間: ' . (microtime(true) - $startTime) . '秒');
        $startTime = microtime(true);
    
        // 4. 受注売り上げとエンゲージメント率
        $engage_q = Engage::where('company_id', Auth::user()->company_id)->where('engage_type', 'cv');
        $engage_q = $this->_filter($engage_q, $product_id, $daterange);
        $engage_q = $engage_q->get();
    
        $engage_ratio['total'] = $engage_q->sum(function ($e) {
            if (!$e->product) {
                return 0;
            } else {
                return $e->product['price'];
            }
        });
        $engage_ratio['count'] = $engage_q->count();
    
        $cv_visitor_ids = $engage_q->pluck('visitor_id');
        $look_review_count = Engage::where('company_id', Auth::user()->company_id)
            ->whereIn('visitor_id', $cv_visitor_ids)
            ->where('engage_type', 'look_review');
        $look_review_count = $this->_filter($look_review_count, $product_id, $daterange);
        $look_review_count = $look_review_count->pluck('visitor_id')->unique()->count();
        $cv_visitor_id_count = $cv_visitor_ids->unique()->count();
    
        if ($cv_visitor_id_count > 0) {
            $engage_ratio['ratio'] = $look_review_count / $cv_visitor_id_count * 100;
        } else {
            $engage_ratio['ratio'] = 0;
        }
        Log::info('4. 受注売り上げとエンゲージメント率計算時間: ' . (microtime(true) - $startTime) . '秒');
        $startTime = microtime(true);
    
        // 5. レビュー
        $review_q = Review::where('company_id', Auth::user()->company_id)->where('published', 1);
        $review_q = $this->_filter($review_q, $product_id, $daterange);
    
        $review['total'] = $review_q->count();
        $review['avg'] = $review_q->avg('ratings_of_review');
        Log::info('5. レビュー計算時間: ' . (microtime(true) - $startTime) . '秒');
        $startTime = microtime(true);
    
        // 6. レビュー, 獲得率
        $startTime61 = microtime(true);
        // $review2_count = $review_q->where('channel', 'mail')->pluck('email_of_reviewer')->unique()->count();
        $review2_count = DB::table('reviews')->select(DB::raw('distinct email_of_reviewer'))->where('company_id', Auth::user()->company_id)->where('published', 1)->where('channel', 'mail')->get()->count();
        Log::info('6.1 review2_count 計算時間: ' . (microtime(true) - $startTime61) . '秒');

        $startTime62 = microtime(true);
        $review2_mail_q = MailLog::whereHas('order', function ($query) {
            $query->where('company_id', Auth::user()->company_id);
        });
        Log::info('6.2 review2_mail_q クエリ構築時間: ' . (microtime(true) - $startTime62) . '秒');

        $startTime63 = microtime(true);
        $review2_mail_q = $this->_filter($review2_mail_q, $product_id, $daterange);
        Log::info('6.3 review2_mail_q _filter 適用時間: ' . (microtime(true) - $startTime63) . '秒');

        $startTime64 = microtime(true);
        // $review2_mail_q_count = count(DB::select("SELECT DISTINCT email FROM mail_logs WHERE EXISTS (SELECT * FROM orders WHERE mail_logs.order_id = orders.id AND company_id = ?)", [Auth::user()->company_id]));
        $review2_mail_q_count = DB::table('mail_logs')
            ->select(DB::raw('COUNT(DISTINCT email) as count'))
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('orders')
                    ->whereRaw('mail_logs.order_id = orders.id')
                    ->where('company_id', Auth::user()->company_id);
            })
            ->value('count');
        Log::info('6.4 review2_mail_q_count 計算時間: ' . (microtime(true) - $startTime64) . '秒');

        if ($review2_mail_q_count > 0) {
            $review['ratio'] = $review2_count / $review2_mail_q_count * 100;
        } else {
            $review['ratio'] = 0;
        }
        Log::info('6. レビュー獲得率計算時間: ' . (microtime(true) - $startTime) . '秒');
        $startTime = microtime(true);
    
        // 7. メール解析データ

        // 日付指定がある場合は、指定された日の送信数とその送信したメールに対しての開封率とクリック率を取得する
        if (!empty($daterange)) {
            $mail_tracks_q = DB::table('mail_track_logs')->select(DB::raw('product_id, order_id, type, reminder_count'))->where('type', 'send');
            $mail_tracks_q = $mail_tracks_q->where('company_id', Auth::user()->company_id);
            $mail_tracks_q = $this->_filter($mail_tracks_q, $product_id, $daterange);
            $send = $mail_tracks_q->get();
            $send_count = $send->count();
            $mail_tracks = [];

            if ($send_count > 0) {
                // 送信したメールのopenとclickの数を取得する
                $mail_tracks['send'] = $send_count;
                $mail_tracks['open'] = 0;
                $mail_tracks['click'] = 0;

                $startTimeMail = microtime(true);
                foreach ($send as $s) {
                    $open = '';
                    $click = '';
                    $open_count = 0;
                    $click_count = 0;

                    if($s->reminder_count !== null){
                        $open_count = MailTrackLog::where('order_id', $s->order_id)
                        ->where('product_id', $s->product_id)
                        ->where('company_id', Auth::user()->company_id)
                        ->where('type', 'open')
                        ->where('reminder_count', $s->reminder_count)
                        ->first();

                        $click_count = MailTrackLog::where('order_id', $s->order_id)
                        ->where('product_id', $s->product_id)
                        ->where('company_id', Auth::user()->company_id)
                        ->where('type', 'click')
                        ->where('reminder_count', $s->reminder_count)
                        ->first();
                    }else{
                        $open_count = MailTrackLog::where('order_id', $s->order_id)
                        ->where('product_id', $s->product_id)
                        ->where('company_id', Auth::user()->company_id)
                        ->where('type', 'open')
                        ->where('reminder_count', null)
                        ->first();

                        $click_count = MailTrackLog::where('order_id', $s->order_id)
                        ->where('product_id', $s->product_id)
                        ->where('company_id', Auth::user()->company_id)
                        ->where('type', 'click')
                        ->where('reminder_count', null)
                        ->first();
                    }

                    if ($open_count) {
                        $mail_tracks['open'] += 1;
                    }

                    if ($click_count) {
                        $mail_tracks['click'] += 1;
                    }
                }
                Log::info('7. メール解析時間: ' . (microtime(true) - $startTimeMail) . '秒');
            } else {
                // 送信数が0の場合は、全て0にする
                $mail_tracks['send'] = 0;
                $mail_tracks['open'] = 0;
                $mail_tracks['click'] = 0;
            }

            $mail['total'] = $mail_tracks['send'];
            $mail['open_rate'] = ($mail_tracks['send'] > 0) ? ($mail_tracks['open'] / $mail_tracks['send'] * 100) : 0;
            $mail['click_rate'] = ($mail_tracks['send'] > 0) ? ($mail_tracks['click'] / $mail_tracks['send'] * 100) : 0;

        }else{
            $mail_tracks_q = DB::table('mail_track_logs')->select(DB::raw('count(*) as c, type'));
            $mail_tracks_q = $mail_tracks_q->where('company_id', Auth::user()->company_id);
            $mail_tracks_q = $this->_filter($mail_tracks_q, $product_id, $daterange);
        
            $d = $mail_tracks_q->groupBy('type')->get();

            $mail_tracks = [];
            foreach (['send', 'open', 'click'] as $i) {
                $tmp = $d->where('type', $i)->first();
                if ($tmp) {
                    $mail_tracks[$i] = $tmp->c;
                } else {
                    $mail_tracks[$i] = 0;
                }
            }

            if ($mail_tracks['send'] > 0) {
                $mail['total'] = $mail_tracks['send'];
                $mail['open_rate'] = $mail_tracks['open'] / $mail_tracks['send'] * 100;
                $mail['click_rate'] = $mail_tracks['click'] / $mail_tracks['send'] * 100;
            } else {
                $mail['total'] = 0;
                $mail['open_rate'] = 0;
                $mail['click_rate'] = 0;
            }
        }

        Log::info('7. メール解析計算時間: ' . (microtime(true) - $startTime) . '秒');

        return view('dashboard/index', compact('products', 'engage', 'best_product', 'engage_ratio', 'review', 'mail', 'product_id', 'daterange'));
    }

    public function engage_ratio (Request $request)
    {
        $engage_q = DB::table('engages')->select(DB::raw("sum(case when engage_type = 'look_review' then 1 else 0 end) as cl, sum(case when engage_type = 'visit' then 1 else 0 end) as cvi, cast(created_at as date) as created_date"));
        $engage_q = $engage_q->where('company_id', Auth::user()->company_id)
            ->whereRaw('created_at >= DATE_SUB(CURDATE(),INTERVAL 30 DAY )');
        $engage_q = $this->_filter($engage_q, $request->product_id, null);

        $engage_data = $engage_q->groupBy(DB::raw('created_date'))->get();

        $look_reviews = [];
        $nolook_reviews = [];
        for ($d = (new Carbon())->addDays(-30); $d <= new Carbon(); $d->addDays(1)) {
            $tmp = $engage_data->where('created_date', $d->toDateString())->first();
            if ($tmp) {
                array_push($look_reviews, $tmp->cl);
                array_push($nolook_reviews, $tmp->cvi - $tmp->cl);
            } else {
                array_push($look_reviews, 0);
                array_push($nolook_reviews, 0);
            }
        }

        $data = [
            ['data' => $look_reviews, 'name' => 'レビューを見た数'],
            ['data' => $nolook_reviews, 'name' => 'レビューを見ていない数']
        ];
        return $data;
    }

    public function review (Request $request)
    {
        $query = DB::table('reviews')->select(DB::raw('count(*) as c, ratings_of_review'))
        ->where('company_id', Auth::user()->company_id)->where('published', 1);
        $query = $this->_filter($query, $request->product_id, null);

        $d = $query->groupBy('ratings_of_review')->get();

        $arr = [];
        foreach ([5,4,3,2,1] as $i) {
            $tmp = $d->where('ratings_of_review', $i)->first();
            if ($tmp) {
                array_push($arr, $tmp->c);
            } else {
                array_push($arr, 0);
            }
        }

        return $arr;
    }

    public function mail (Request $request)
    {
        $q = DB::table('mail_track_logs')->select(DB::raw("count(*) as c, cast(created_at as date) as created_date"));
        $q = $q->where('company_id', Auth::user()->company_id)
            ->where('type', 'send');
        if ($request->input('daterange') !== null) {
            $q = $q->whereBetween('created_at', [preg_replace('/\s*\/.*$/isU', '', request('daterange')), preg_replace('/^.*?\/\s+/isU', '', request('daterange'))]);
        } else {
            $q = $q->whereRaw('created_at >= DATE_SUB(CURDATE(),INTERVAL 30 DAY )');
        }
        $q = $this->_filter($q, $request->product_id, null);

        $q_data = $q->groupBy(DB::raw('created_date'))->get();

        $mails = [];
        if ($request->input('daterange') !== null) {
            $start = (new Carbon())->parse(preg_replace('/\s*\/.*$/isU', '', request('daterange')));
            $end = (new Carbon())->parse(preg_replace('/^.*?\/\s+/isU', '', request('daterange')));
        } else {
            $start = (new Carbon())->addDays(-30);
            $end = new Carbon();
        }
        for ($d = $start; $d <= $end; $d->addDays(1)) {
            $tmp = $q_data->where('created_date', $d->toDateString())->first();
            if ($tmp) {
                array_push($mails, $tmp->c);
            } else {
                array_push($mails, 0);
            }
        }

        $data = [
            [
                'name' => '送信分',
                'data' => $mails
            ]
        ];
        return $data;
    }

    public function _filter ($engage_q, $product_id, $daterange, $datekey = 'created_at') {
        if (!empty($product_id)) {
            $engage_q = $engage_q->where('product_id', $product_id);
        }
        if (!empty($daterange)) {
            $arr = explode('/', $daterange);
            $sd = trim($arr[0]);
            $ed = trim($arr[1]);
            $engage_q = $engage_q->whereBetween($datekey, [$sd, $ed . ' 23:59:59']);
        }
        return $engage_q;
    }
}
