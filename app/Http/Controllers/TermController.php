<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Product;

class TermController extends Controller
{

    public function privacy_policy () 
    {
        return view('terms/pri');
    }

    public function term_of_service () 
    {
        $provider = config('app.provider');
        if ($provider == 'deel') {
            return redirect()->away('https://ugc-creative.com/agreement');
        }

        return view('terms/ser', compact('provider'));
    }

    public function second_usage () 
    {
        return view('terms/sec');
    }

}
