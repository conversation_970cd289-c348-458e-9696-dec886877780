<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Auth;
use Closure;
use Carbon\Carbon;
use Mail;
use Config;
use App\Mail\TwoFactorAuthPassword;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            return route('login');
        }
    }

    public function handle($request, Closure $next, ...$guards)
    {
        /*if (config('app.env') !== 'staging' && Auth()->user() !== null && Auth()->user()->is_admin() === false && Auth()->user()->login_environment !== request()->server->get('REMOTE_ADDR') . ':' . request()->server->get('HTTP_USER_AGENT')) {
            if (Auth()->user()->login_token === null || Auth()->user()->login_expiration < Carbon::now()) {
                $random_password = '';

                for($i = 0 ; $i < 4 ; $i++) {
                    $random_password .= strval(rand(0, 9));
                }

                Auth()->user()->login_token = $random_password;
                Auth()->user()->login_expiration = now()->addMinutes(10);
                Auth()->user()->login_environment = null;
                Auth()->user()->save();

                // メール送信
                Mail::to(Auth()->user())->send(new TwoFactorAuthPassword($random_password));
            }
            return redirect('/auth/twofactorauth/' . md5(Auth()->user()->login_token));
        }*/

        return parent::handle($request, $next, ...$guards);
    }
}
