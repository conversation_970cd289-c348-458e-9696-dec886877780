<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\User;

class RedirectIfNoSiteSetting
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::user()->company_tied->sites()->count() == 0) {
            return redirect('/reviews/sites/settings')->with('flash_message', ['type' => 'info', 'msg' => '初めにウェブサイト設定をしてください']);
        }

        return $next($request);
    }
}
