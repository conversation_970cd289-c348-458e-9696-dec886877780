<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;

class Administrator
{
    /**
     * @param Request $request
     * @param Closure $next
     * @param null $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        /** @var User $user */
        $user = $request->user();

        // ユーザー作成権限のあるメールアドレスを管理
        $admins = ['<EMAIL>', '<EMAIL>'];
        if (!in_array($user->email, $admins)) {
            return redirect('/');
        }

        return $next($request);
    }
}